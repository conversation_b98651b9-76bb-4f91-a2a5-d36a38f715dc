<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="cep_user.ideas_list">
        <t t-call="website.layout">
            <t t-set="title">My Ideas</t>
            <t t-set="head">
                <t t-call-assets="web.assets_common"/>
                <t t-call-assets="web.assets_frontend"/>
                <t t-call-assets="cep_user.cep_user_portal_table_view"/>

                <!-- DataTables CSS -->
                <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet"/>
            </t>
            <t t-if=" ideas != undefined ">
                <br/>
                <div class="oe_structure">
                    <div class="container">
                        <main>
                            <section class="container my-5">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h3>Your Idea List</h3>
                                </div>
                                <hr class="border-primary"/>
                                <table id="portal-table" class="table table-striped shadow" style="width: 100%">
                                    <thead>
                                        <tr>
                                            <th>Title</th>
                                            <th>Status</th>
                                            <th>Create Date</th>
                                            <th>Tags</th>
                                            <th style="min-width: 200px">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <t t-foreach="ideas" t-as="idea">
                                            <tr>
                                                <a>
                                                    <td>
                                                        <a t-att-href="'/ideas/' + str(idea.id) + '/view'">
                                                            <t t-esc="idea.title"/>
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <t t-esc="idea.status"/>
                                                    </td>
                                                    <td>
                                                        <t t-esc="idea.create_date.strftime('%Y-%m-%d')"/>
                                                    </td>
                                                    <td>
                                                        <t t-foreach="idea.tag_ids" t-as="tag">
                                                            <span class="badge">
                                                                <t t-esc="tag.name"/>
                                                            </span>
                                                        </t>
                                                    </td>
                                                    <td style="min-width: 200px">
                                                        <a t-att-href="'/ideas/edit/%d' % idea.id"
                                                            class="btn btn-primary">
                                                            <span>Edit</span>
                                                        </a>
                                                        <a t-att-href="'/ideas/%d/delete' % idea.id"
                                                            class="btn btn-primary delete-idea" data-bs-toggle="modal"
                                                            data-bs-target="#delete-idea-modal">
                                                            <span>Delete</span>
                                                        </a>
                                                    </td>
                                                </a>
                                            </tr>
                                        </t>
                                    </tbody>
                                </table>
                            </section>
                        </main>
                    </div>
                </div>
                <br/>
            </t>
            <div class="modal fade" id="delete-idea-modal" tabindex="-1" aria-labelledby="delete-idea-modal-label"
                aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h1 class="modal-title fs-5" id="delete-idea-modal-label">idea Delete Confirmation</h1>
                        </div>

                        <div class="modal-body"> Are you sure you want to delete this idea <span></span>? </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
                            <a href="" type="button" class="btn btn-primary" id="delete-idea-modal-yes">Yes</a>
                        </div>
                    </div>
                </div>
            </div>
            <!-- DataTables JS -->
            <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
            <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
        </t>
    </template>
</odoo>