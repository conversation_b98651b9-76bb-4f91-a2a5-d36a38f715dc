<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="cep_user.proposals_list">
    <t t-call="website.layout">
        <t t-set="title">My Proposals</t>
        <t t-set="head">
            <t t-call-assets="web.assets_common"/>
            <t t-call-assets="web.assets_frontend"/>
            <t t-call-assets="cep_user.cep_user_portal_table_view"/>

            <!-- DataTables CSS -->
            <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet"/>
        </t>
        <t t-if=" proposals != undefined ">
            <br/>
            <div class="oe_structure">
                <div class="container">
                    <main>
                        <section class="container my-5">
                            <div class="d-flex justify-content-between align-items-center">
                                <h3>Your Proposal List</h3>
                                <div class="d-flex justify-content-between">
                                    <!-- Edit Button -->
                                    <a
                                    href="/proposals/new"
                                    class="btn btn-primary h-50 px-3 d-flex gap-2 rounded-2"
                                    >
                                        <img
                                            src="/cep_user/static/src/img/pencil.png"
                                            width="20"
                                            height="20"
                                            alt="edit_icon"
                                        />
                                        <span>New Proposal</span>
                                    </a>
                                </div>
                            </div>
                            <hr class="border-primary" />
                            <table
                                id="portal-table"
                                class="table table-striped shadow"
                                style="width: 100%"
                            >
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Location</th>
                                        <th>Minimum Vote</th>
                                        <th>End Date</th>
                                        <th>Tags</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="proposals" t-as="proposal">
                                        <tr>
                                            <td class="title">
                                                <a t-att-href="'/proposals/' + str(proposal.id) + '/view'">
                                                    <t t-esc="proposal.title"/>
                                                </a>
                                            </td>
                                            <td><t t-esc="proposal.location"/></td>
                                            <td><t t-esc="proposal.min_votes"/></td>
                                            <td><t t-esc="proposal.end_date"/></td>
                                            <td>
                                                <t t-foreach="proposal.tag_ids" t-as="tag">
                                                    <span class="badge"><t t-esc="tag.name"/></span>
                                                </t>
                                            </td>
                                            <td>
                                                <a t-att-href="'/proposals/edit/%d' % proposal.id" class="btn btn-primary mb-1">
                                                    <span>Edit</span>
                                                </a>

                                                <a
                                                    t-att-href="'/proposals/%d/delete' % proposal.id"
                                                    class="btn btn-primary mb-1 delete-proposal"
                                                    data-bs-toggle="modal" data-bs-target="#delete-proposal-modal"
                                                >
                                                    <span>Delete</span>
                                                </a>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </section>
                    </main>
                </div>
            </div>
            <br/>
        </t>

        <div class="modal fade" id="delete-proposal-modal" tabindex="-1" aria-labelledby="delete-proposal-modal-label" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="delete-proposal-modal-label">Proposal Delete Confirmation</h1>
                    </div>

                    <div class="modal-body">
                        Are you sure you want to delete this proposal <span></span>?
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
                        <a href="" type="button" class="btn btn-primary" id="delete-proposal-modal-yes">Yes</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- DataTables JS -->
        <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    </t>
    </template>
</odoo>