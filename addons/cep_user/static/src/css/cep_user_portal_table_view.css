.dataTables_wrapper .row:first-child {
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .dataTables_wrapper .row:first-child {
    gap: 10px;
  }
}

.dataTables_wrapper .row:last-child {
  margin-top: 1rem;
}

.dataTables_wrapper .badge {
  background-color: #eef3fe;
  color: rgb(74, 74, 74);
  padding: 10px;
  border: 1px solid #ccc;
  margin: 2px;
}

/* Change cursor and row highlight */
.dataTables_wrapper tbody tr[data-href] {
  cursor: pointer;
}

.review_download {
  white-space: nowrap;
  width: 50%;
  overflow: hidden;
  text-overflow: ellipsis;

  display: inline-block;
  vertical-align: text-bottom;
}
