from odoo import models, fields, api
import json
import logging

_logger = logging.getLogger(__name__)

class RecommendationData(models.Model):
    _name = 'cep_rmt.recommendation_data'
    _description = 'Recommendation Mapping Data'
    _order = 'create_date desc'

    # Basic fields
    title = fields.Char(string='Title', required=True)
    user_id = fields.Many2one('res.users', string='User', required=True, default=lambda self: self.env.user)
    output_result_count = fields.Integer(string='Output Result Count', default=3)
    
    # Step selection field
    step = fields.Selection([
        ('recommendation', 'Recommendation'),
        ('stakeholders_involved', 'Stakeholders Involved'),
        ('stakeholders', 'Stakeholders'),
        ('governance_rules', 'Governance Rules'),
        ('identified_challenges', 'Identified Challenges'),
        ('coordination_plan', 'Coordination Plan'),
        ('team_info', 'Team Info'),
        ('implementation_steps', 'Implementation Steps'),
        ('expected_outcomes', 'Expected Outcomes'),
        ('monitoring_indicators', 'Monitoring Indicators'),
        ('feedback_mechanisms', 'Feedback Mechanisms'),
        ('supporting_docs', 'Supporting Docs'),
    ], string='Step', required=True)
    step_number = fields.Integer(string='Step Number', compute='_compute_step_number', store=True)
    # Status field
    status = fields.Selection([
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ], string='Status', default='running')
    
    # Error message field
    error_message = fields.Text(string='Error Message')
    
    # JSON fields for all data types
    recommendation = fields.Json(string='Recommendation Data')
    stakeholders_involved = fields.Json(string='Stakeholders Involved Data')
    stakeholders = fields.Json(string='Stakeholders Data')
    governance_rules = fields.Json(string='Governance Rules Data')
    identified_challenges = fields.Json(string='Identified Challenges Data')
    coordination_plan = fields.Json(string='Coordination Plan Data')
    team_info = fields.Json(string='Team Info Data')
    implementation_steps = fields.Json(string='Implementation Steps Data')
    expected_outcomes = fields.Json(string='Expected Outcomes Data')
    monitoring_indicators = fields.Json(string='Monitoring Indicators Data')
    feedback_mechanisms = fields.Json(string='Feedback Mechanisms Data')
    supporting_docs = fields.Json(string='Supporting Docs Data')
    
    attachment_ids = fields.One2many(
        'ir.attachment', 'res_id', domain=[('res_model', '=', 'cep_rmt.recommendation_data')],
        string='Attachments', ondelete='cascade')
    suggestions = fields.Json(string='Suggestions Data')
    
    
    
    
    def save_suggestions(self,  body):
        try:
            result = body.get("results")
            project_id = body.get('project_id')
            record = self.env['cep_rmt.recommendation_data'].browse(int(project_id)).sudo()

            record.write({
                'status': 'completed',
                'step': 'stakeholders_involved',
                'suggestions': result
                })
        except Exception as e:
            _logger.error(f"Error saving agendas: {str(e.with_traceback(None))}")
    
  
   

    @api.depends('step')
    def _compute_step_number(self):
        step_order = {
            'recommendation': 1,
            'stakeholders_involved': 2,
            'stakeholders': 3,
            'governance_rules': 4,
            'identified_challenges': 5,
            'coordination_plan': 6,
            'team_info': 7,
            'implementation_steps': 8,
            'expected_outcomes': 9,
            'monitoring_indicators': 10,
            'feedback_mechanisms': 11,
            'supporting_docs': 12,
        }
        for record in self:
            record.step_number = step_order.get(record.step, 0)