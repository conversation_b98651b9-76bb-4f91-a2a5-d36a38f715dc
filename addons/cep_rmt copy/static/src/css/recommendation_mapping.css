/* Recommendation Mapping Tool Styles */

.recommendation-form {
    max-width: 100%;
}

/* Drop zone styles */
#drop-zone {
    transition: all 0.3s ease;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#drop-zone:hover {
    background-color: #e3f2fd !important;
    border-color: #1976d2 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

#drop-zone.dragover {
    background-color: #e3f2fd !important;
    border-color: #1976d2 !important;
    transform: scale(1.02);
}

/* File upload success state */
.alert.alert-success {
    border-left: 4px solid #28a745;
}

/* Form styling */
.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 7px 12px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button styling */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    border-radius: 8px;
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Results cards */
.card {
    border-radius: 12px;
    border: 1px solid #e9ecef;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.card-text {
    color: #6c757d;
    line-height: 1.5;
}

/* Progress bar styling */
.progress {
    border-radius: 4px;
    background-color: #f8f9fa;
}

.progress-bar {
    border-radius: 4px;
    transition: width 0.6s ease;
}

/* Network visualization placeholder */
#network-visualization {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

#network-visualization::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23dee2e6" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

/* Responsive design */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem;
    }
    
    .lead {
        font-size: 1rem;
    }
    
    #drop-zone {
        min-height: 120px;
        padding: 2rem !important;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Loading spinner */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Info icon styling */
.fa-info-circle {
    cursor: help;
    opacity: 0.7;
}

.fa-info-circle:hover {
    opacity: 1;
}

/* Header styling */
.text-primary {
    color: #155493 !important;
}

.display-4 {
    font-weight: 600;
    margin-bottom: 1rem;
}

.lead {
    font-size: 1.1rem;
    line-height: 1.6;
    text-align: justify;
}

/* Card header styling */
.card-header.bg-primary {
    background-color: #007bff !important;
    border-bottom: none;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.card-header .card-title {
    color: white;
    font-weight: 600;
}

/* Key features styling */
.list-unstyled li {
    padding-left: 1.5rem;
    position: relative;
}

.list-unstyled li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

/* Smooth animations */
* {
    transition: all 0.3s ease;
}

/* Focus states for accessibility */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Multi-step form specific styles */
.form-header-title {
    font-size: 40px;
    color: #155493;
}

.step-line {
    width: 70px;
    height: 4px;
    margin-left: 12px;
}

.step-line-active {
    background: #249AFB;
    
}

.step-line-inactive {
    background: #E2E8F0;
}

.form-card {
    background: #F8FAFC;
    border: 1px solid #E2E8F0;
    border-radius: 16px;
}

.form-card .card-body{
    background-color: #F8FAFC !important
}

.form-label-primary {
    color: #0F182A;
    font-size: 16px;
}

.info-icon {
    color: #0F182A;
}

.form-textarea {
    border: 1px solid #E2E8F0;
    border-radius: 6px;
    min-height: 200px;
    color: #64748B;
}

.drop-zone-custom {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #E2E8F0;
    border-radius: 6px;
    min-height: 200px;
}

.form-select-custom {
    max-width: 100px;
    border: 1px solid #E2E8F0;
    border-radius: 6px;
}

.form-select-custom-wide {
    max-width: 200px;
    border: 1px solid #E2E8F0;
    border-radius: 6px;
}

.stakeholders-textarea {
    border: 1px solid #E2E8F0;
    border-radius: 6px;
    color: #64748B;
}

.suggestions-container {
    border: 1px solid #E2E8F0;
    border-radius: 6px;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
}

.checkbox-label {
    color: #0F182A;
}

.btn-prev {
    border: 1px solid #249AFB;
    color: #249AFB;
    border-radius: 6px;
    font-weight: 700;
}

.btn-next {
    background: #249AFB;
    border: none;
    border-radius: 6px;
    font-weight: 700;
    color: white;
}

.flowchart-image {
    width: 100%;
}