// Step 10: Monitoring Indicators Component
window.CEP_RMT = window.CEP_RMT || {};
window.CEP_RMT.Steps = window.CEP_RMT.Steps || {};

window.CEP_RMT.Steps.Step10 = (function() {
  'use strict';

  const STEP_NUMBER = 10;
  const STEP_NAME = 'monitoring_indicators';

  // Initialize step-specific functionality
  function init() {
    bindEvents();
    window.CEP_RMT.SuggestionsHandler.populateAutomatedSuggestions(STEP_NUMBER);
  }

  // Bind step-specific events
  function bindEvents() {
    // Input field event
    $('#step10_monitoring_input').off('input').on('input', function () {
      const formState = window.CEP_RMT.FormBase.getFormState();
      formState.data.monitoring = $(this).val();
    });

    // Suggestion checkboxes
    $(document).off('change', `input[id^="step${STEP_NUMBER}_suggestion"]`)
               .on('change', `input[id^="step${STEP_NUMBER}_suggestion"]`, function () {
      window.CEP_RMT.SuggestionsHandler.updateSelectedSuggestions(STEP_NUMBER);
    });

    // Reference link
    $('#step10_reference_link').off('click').on('click', function (e) {
      e.preventDefault();
      window.CEP_RMT.SuggestionsHandler.showReferencesModal(STEP_NUMBER);
    });
  }

  // Validate step data
  function validate() {
    const formState = window.CEP_RMT.FormBase.getFormState();
    
    if (!formState.data.monitoring.trim()) {
      window.CEP_RMT.FormBase.showErrorMessage('Please enter monitoring indicators information');
      return false;
    }
    
    return true;
  }

  // Collect step data for API submission
  function collectData() {
    const formState = window.CEP_RMT.FormBase.getFormState();
    
    return {
      monitoring_text: formState.data.monitoring,
      selected_monitoring: formState.data.selectedMonitoring
    };
  }

  // Submit step data
  async function submit() {
    if (!validate()) {
      return false;
    }

    try {
      const inputData = collectData();
      const result = await window.CEP_RMT.SuggestionsHandler.submitStepData(
        STEP_NUMBER, 
        STEP_NAME, 
        inputData
      );
      
      return result;
    } catch (error) {
      console.error('Step 10 submission error:', error);
      return false;
    }
  }

  // Load existing data into the form
  function loadData(data) {
    if (data) {
      const formState = window.CEP_RMT.FormBase.getFormState();
      
      if (data.monitoring_text) {
        formState.data.monitoring = data.monitoring_text;
        $('#step10_monitoring_input').val(data.monitoring_text);
      }
      
      if (data.selected_monitoring) {
        formState.data.selectedMonitoring = data.selected_monitoring;
        updateCheckboxesFromData(data.selected_monitoring);
      }
    }
  }

  // Update checkboxes based on loaded data
  function updateCheckboxesFromData(selectedItems) {
    $(`input[id^="step${STEP_NUMBER}_suggestion"]`).each(function() {
      const label = $(this).next('label').text().trim();
      const isSelected = selectedItems.includes(label);
      $(this).prop('checked', isSelected);
    });
  }

  // Public API
  return {
    init,
    validate,
    submit,
    loadData,
    getStepNumber: () => STEP_NUMBER,
    getStepName: () => STEP_NAME
  };
})();
