// Step 8: Implementation Steps Component
window.CEP_RMT = window.CEP_RMT || {};
window.CEP_RMT.Steps = window.CEP_RMT.Steps || {};

window.CEP_RMT.Steps.Step8 = (function() {
  'use strict';

  const STEP_NUMBER = 8;
  const STEP_NAME = 'implementation_steps';

  // Initialize step-specific functionality
  function init() {
    bindEvents();
    window.CEP_RMT.SuggestionsHandler.populateAutomatedSuggestions(STEP_NUMBER);
  }

  // Bind step-specific events
  function bindEvents() {
    // Input field event
    $('#step8_implementation_input').off('input').on('input', function () {
      const formState = window.CEP_RMT.FormBase.getFormState();
      formState.data.implementation = $(this).val();
    });

    // Suggestion checkboxes
    $(document).off('change', `input[id^="step${STEP_NUMBER}_suggestion"]`)
               .on('change', `input[id^="step${STEP_NUMBER}_suggestion"]`, function () {
      window.CEP_RMT.SuggestionsHandler.updateSelectedSuggestions(STEP_NUMBER);
    });

    // Reference link
    $('#step8_reference_link').off('click').on('click', function (e) {
      e.preventDefault();
      window.CEP_RMT.SuggestionsHandler.showReferencesModal(STEP_NUMBER);
    });
  }

  // Validate step data
  function validate() {
    const formState = window.CEP_RMT.FormBase.getFormState();
    
    if (!formState.data.implementation.trim()) {
      window.CEP_RMT.FormBase.showErrorMessage('Please enter implementation steps information');
      return false;
    }
    
    return true;
  }

  // Collect step data for API submission
  function collectData() {
    const formState = window.CEP_RMT.FormBase.getFormState();
    
    return {
      implementation_text: formState.data.implementation,
      selected_implementation: formState.data.selectedImplementation
    };
  }

  // Submit step data
  async function submit() {
    if (!validate()) {
      return false;
    }

    try {
      const inputData = collectData();
      const result = await window.CEP_RMT.SuggestionsHandler.submitStepData(
        STEP_NUMBER, 
        STEP_NAME, 
        inputData
      );
      
      return result;
    } catch (error) {
      console.error('Step 8 submission error:', error);
      return false;
    }
  }

  // Load existing data into the form
  function loadData(data) {
    if (data) {
      const formState = window.CEP_RMT.FormBase.getFormState();
      
      if (data.implementation_text) {
        formState.data.implementation = data.implementation_text;
        $('#step8_implementation_input').val(data.implementation_text);
      }
      
      if (data.selected_implementation) {
        formState.data.selectedImplementation = data.selected_implementation;
        updateCheckboxesFromData(data.selected_implementation);
      }
    }
  }

  // Update checkboxes based on loaded data
  function updateCheckboxesFromData(selectedItems) {
    $(`input[id^="step${STEP_NUMBER}_suggestion"]`).each(function() {
      const label = $(this).next('label').text().trim();
      const isSelected = selectedItems.includes(label);
      $(this).prop('checked', isSelected);
    });
  }

  // Public API
  return {
    init,
    validate,
    submit,
    loadData,
    getStepNumber: () => STEP_NUMBER,
    getStepName: () => STEP_NAME
  };
})();
