// Step 3: Implementation Stakeholders Component
window.CEP_RMT = window.CEP_RMT || {};
window.CEP_RMT.Steps = window.CEP_RMT.Steps || {};

window.CEP_RMT.Steps.Step3 = (function() {
  'use strict';

  const STEP_NUMBER = 3;
  const STEP_NAME = 'stakeholders';

  // Initialize step-specific functionality
  function init() {
    bindEvents();
  }

  // Bind step-specific events
  function bindEvents() {
    // Implementation stakeholder
    $('#step3_implementation_stakeholder').off('input').on('input', function () {
      const formState = window.CEP_RMT.FormBase.getFormState();
      formState.data.implementationStakeholder = $(this).val();
    });

    // Ownership stakeholder
    $('#step3_ownership_stakeholder').off('input').on('input', function () {
      const formState = window.CEP_RMT.FormBase.getFormState();
      formState.data.ownershipStakeholder = $(this).val();
    });

    // Progress tracker
    $('#step3_progress_tracker').off('input').on('input', function () {
      const formState = window.CEP_RMT.FormBase.getFormState();
      formState.data.progressTracker = $(this).val();
    });

    // Feedback handler
    $('#step3_feedback_handler').off('input').on('input', function () {
      const formState = window.CEP_RMT.FormBase.getFormState();
      formState.data.feedbackHandler = $(this).val();
    });

    // Outcome evaluator
    $('#step3_outcome_evaluator').off('input').on('input', function () {
      const formState = window.CEP_RMT.FormBase.getFormState();
      formState.data.outcomeEvaluator = $(this).val();
    });
  }

  // Validate step data
  function validate() {
    const formState = window.CEP_RMT.FormBase.getFormState();
    
    if (!formState.data.implementationStakeholder.trim()) {
      window.CEP_RMT.FormBase.showErrorMessage('Implementation stakeholder is required');
      return false;
    }
    
    return true;
  }

  // Collect step data for API submission
  function collectData() {
    const formState = window.CEP_RMT.FormBase.getFormState();
    
    return {
      implementation_stakeholder: formState.data.implementationStakeholder,
      ownership_stakeholder: formState.data.ownershipStakeholder,
      progress_tracker: formState.data.progressTracker,
      feedback_handler: formState.data.feedbackHandler,
      outcome_evaluator: formState.data.outcomeEvaluator
    };
  }

  // Submit step data
  async function submit() {
    if (!validate()) {
      return false;
    }

    try {
      const inputData = collectData();
      const result = await window.CEP_RMT.SuggestionsHandler.submitStepData(
        STEP_NUMBER, 
        STEP_NAME, 
        inputData
      );
      
      return result;
    } catch (error) {
      console.error('Step 3 submission error:', error);
      return false;
    }
  }

  // Load existing data into the form
  function loadData(data) {
    if (data) {
      const formState = window.CEP_RMT.FormBase.getFormState();
      
      if (data.implementation_stakeholder) {
        formState.data.implementationStakeholder = data.implementation_stakeholder;
        $('#step3_implementation_stakeholder').val(data.implementation_stakeholder);
      }
      
      if (data.ownership_stakeholder) {
        formState.data.ownershipStakeholder = data.ownership_stakeholder;
        $('#step3_ownership_stakeholder').val(data.ownership_stakeholder);
      }
      
      if (data.progress_tracker) {
        formState.data.progressTracker = data.progress_tracker;
        $('#step3_progress_tracker').val(data.progress_tracker);
      }
      
      if (data.feedback_handler) {
        formState.data.feedbackHandler = data.feedback_handler;
        $('#step3_feedback_handler').val(data.feedback_handler);
      }
      
      if (data.outcome_evaluator) {
        formState.data.outcomeEvaluator = data.outcome_evaluator;
        $('#step3_outcome_evaluator').val(data.outcome_evaluator);
      }
    }
  }

  // Public API
  return {
    init,
    validate,
    submit,
    loadData,
    getStepNumber: () => STEP_NUMBER,
    getStepName: () => STEP_NAME
  };
})();
