// Base Form Component for CEP RMT
window.CEP_RMT = window.CEP_RMT || {};

window.CEP_RMT.FormBase = (function () {
  'use strict';

  // Multi-step form state
  const formState = {
    currentStep: 2,
    totalSteps: 12,
    data: {
      // Step 1
      recommendation: '',
      selectedFile: null,
      fileName: '',
      outputCount: 3,
      // Step 2
      stakeholders: '',
      selectedStakeholders: [],
      // Step 3
      implementationStakeholder: '',
      ownershipStakeholder: '',
      progressTracker: '',
      feedbackHandler: '',
      outcomeEvaluator: '',
      // Step 4
      governance: '',
      selectedGovernance: [],
      // Step 5
      challenges: '',
      selectedChallenges: [],
      // Step 6
      coordination: '',
      selectedCoordination: [],
      // Step 7
      grupMotor: 'yes',
      teamLeader: {
        name: '',
        phone: '',
        email: '',
      },
      teamMembers: {
        name: '',
        background: '',
        taskDescription: '',
        role: '',
        contact: '',
      },
      activityTimeline: {
        activityName: '',
        milestone: '',
        startDate: '',
        endDate: '',
        deliverables: '',
        responsiblePerson: '',
        budget: '',
      },
      // Step 8
      implementation: '',
      selectedImplementation: [],
      // Step 9
      outcomes: '',
      selectedOutcomes: [],
      // Step 10
      monitoring: '',
      selectedMonitoring: [],
      // Step 11
      feedback: '',
      selectedFeedback: [],
      // Step 12
      title: '',
      author: '',
      deliverableLink: '',
      supportingDocs: [],
      results: [],
    },
    isLoading: false,
    recordId: null,
  };

  // Results state for suggestions
  let resultsState= {
  "Top Processes": {
    "references": [
      {
        "line": 84,
        "page": 66,
        "sentence": "Inform the involved parties of the possible climate risks"
      },
      {
        "line": 87,
        "page": 68,
        "sentence": "taking into account climate change, and also promote"
      },
      {
        "line": 18,
        "page": 48,
        "sentence": "The action directions include modelling climate scenarios and creating an"
      },
      {
        "line": 25,
        "page": 76,
        "sentence": "climate change impacts, developing adaptation to climate"
      },
      {
        "line": 7,
        "page": 55,
        "sentence": "The measures for adaptation to climate change to a large extent should be implemented within"
      },
      {
        "line": 62,
        "page": 73,
        "sentence": "Integrate the aspects of adaptation to climate change in"
      },
      {
        "line": 29,
        "page": 44,
        "sentence": "climate resilience;"
      },
      {
        "line": 89,
        "page": 77,
        "sentence": "Ensure that climate-resilient development aspects are taken"
      },
      {
        "line": 35,
        "page": 44,
        "sentence": "climate-resilient development"
      }
    ],
    "recommendations": [
      {
        "score": 0.5832869410514832,
        "title": "Develop climate narrative and communication frameworks.",
        "description": "Shape public understanding and support for climate action."
      },
      {
        "score": 0.5090707540512085,
        "title": "Create climate adaptation planning processes.",
        "description": "Prepare communities for long-term climate change impacts."
      },
      {
        "score": 0.5026066899299622,
        "title": "Develop adaptive governance models for climate resilience.",
        "description": "Make policies flexible to respond to evolving challenges."
      }
    ]
  },
  "Top Stakeholders": {
    "references": [
      {
        "line": 61,
        "page": 80,
        "sentence": "Studies of climate experts"
      },
      {
        "line": 103,
        "page": 77,
        "sentence": "Carry out expert training on integrated aspects of climate"
      },
      {
        "line": 7,
        "page": 81,
        "sentence": "Naswa et al., 2015, Good Practice in Designing and Implementing National Monitoring Systems for Adaptation to Climate"
      },
      {
        "line": 38,
        "page": 13,
        "sentence": "Climate impact,"
      },
      {
        "line": 33,
        "page": 53,
        "sentence": "Climate impact,"
      },
      {
        "line": 17,
        "page": 51,
        "sentence": "social background, are protected from the adverse effects of climate change"
      },
      {
        "line": 29,
        "page": 44,
        "sentence": "climate resilience;"
      },
      {
        "line": 35,
        "page": 44,
        "sentence": "climate-resilient development"
      },
      {
        "line": 89,
        "page": 77,
        "sentence": "Ensure that climate-resilient development aspects are taken"
      }
    ],
    "recommendations": [
      {
        "score": 0.4564324617385864,
        "title": "Experts",
        "description": "Independent climate experts to design the Climate Check tool."
      },
      {
        "score": 0.43956610560417175,
        "title": "Gender & Climate Advocates",
        "description": "Address the disproportionate climate impacts on women and marginalized groups."
      },
      {
        "score": 0.26710620522499084,
        "title": "Resilience Planners",
        "description": "Develop strategies to make cities adaptable to climate extremes."
      }
    ]
  },
  "Top Governance Rules": {
    "references": [
      {
        "line": 44,
        "page": 12,
        "sentence": "Certain measures for adaptation to climate change in order to reduce climate change risks and"
      },
      {
        "line": 24,
        "page": 53,
        "sentence": "significant risks and also measures to be implemented to promote adaptation to climate change"
      },
      {
        "line": 72,
        "page": 71,
        "sentence": "Integrate climate change impacts, scenarios and adaptation"
      },
      {
        "line": 9,
        "page": 52,
        "sentence": "The objective is aimed at ensuring the climate resilience of infrastructure and buildings"
      },
      {
        "line": 5,
        "page": 68,
        "sentence": "Strategic objective 3: Infrastructure and buildings are climate-resilient and planned according to possible climate risks"
      },
      {
        "line": 23,
        "page": 13,
        "sentence": "Infrastructure and buildings are climate-resilient and planned according to possible"
      },
      {
        "line": 24,
        "page": 53,
        "sentence": "significant risks and also measures to be implemented to promote adaptation to climate change"
      },
      {
        "line": 52,
        "page": 79,
        "sentence": "71 OECD, 2015, National Climate Change Adaptation: Emerging Practices in Monitoring and"
      },
      {
        "line": 28,
        "page": 13,
        "sentence": "projections that promote integration of the aspects of adaptation to climate change in"
      }
    ],
    "recommendations": [
      {
        "score": 0.35852518677711487,
        "title": "Climate Adaptation Frameworks",
        "description": "Plans to prepare for and reduce risks from climate change impacts."
      },
      {
        "score": 0.34195372462272644,
        "title": "Urban Heat Resilience Strategies",
        "description": "Prepare cities for extreme heat events."
      },
      {
        "score": 0.31379327178001404,
        "title": "National Adaptation Plans",
        "description": "Government plans to address climate risks."
      }
    ]
  },
  "Top Feedback Mechanisms": {
    "references": [
      {
        "line": 45,
        "page": 12,
        "sentence": "to benefit from the possibilities of climate change are included in the existing action policies;"
      },
      {
        "line": 38,
        "page": 44,
        "sentence": "climate change adaptation process, prepare an action policy and inform the Convention"
      },
      {
        "line": 19,
        "page": 75,
        "sentence": "Action direction 5.1: Development and maintenance of a monitoring, reporting and evaluation system for adaptation to climate change"
      },
      {
        "line": 25,
        "page": 45,
        "sentence": "policies, plans, actions and mechanisms"
      },
      {
        "line": 46,
        "page": 12,
        "sentence": "however, a comprehensive, coordinated policy and implementation of additional measures are"
      },
      {
        "line": 57,
        "page": 45,
        "sentence": "policies”,"
      },
      {
        "line": 48,
        "page": 79,
        "sentence": "available data characterising the current climate and future climate change scenarios ensured"
      },
      {
        "line": 68,
        "page": 76,
        "sentence": "Update and publish future climate change scenarios developed"
      },
      {
        "line": 19,
        "page": 71,
        "sentence": "requirements are to be adjusted to the current climate"
      }
    ],
    "recommendations": [
      {
        "score": 0.663959801197052,
        "title": "Create climate action feedback integration",
        "description": "Connects outcomes to policy development."
      },
      {
        "score": 0.6150579452514648,
        "title": "Create feedback loops between policy makers and implementers",
        "description": "Ensures policies are practical and effective."
      },
      {
        "score": 0.5978148579597473,
        "title": "Regularly update the Climate Check criteria based on emerging climate challenges",
        "description": "Keeps the evaluation process relevant to new climate risks."
      }
    ]
  },
  "Top Monitoring Indicators": {
    "references": [
      {
        "line": 45,
        "page": 12,
        "sentence": "to benefit from the possibilities of climate change are included in the existing action policies;"
      },
      {
        "line": 26,
        "page": 15,
        "sentence": "However, in the event no climate change mitigation measures described in the climate"
      },
      {
        "line": 30,
        "page": 55,
        "sentence": "climate-related measures, including adaptation to climate change activities"
      },
      {
        "line": 26,
        "page": 50,
        "sentence": "Goals of Adaptation to Climate Change and Action Directions"
      },
      {
        "line": 80,
        "page": 59,
        "sentence": "there climate change issues and possible actions to adapt"
      },
      {
        "line": 45,
        "page": 12,
        "sentence": "to benefit from the possibilities of climate change are included in the existing action policies;"
      },
      {
        "line": 84,
        "page": 66,
        "sentence": "Inform the involved parties of the possible climate risks"
      },
      {
        "line": 36,
        "page": 66,
        "sentence": "Conduct studies on the impact of risks caused by climate"
      },
      {
        "line": 8,
        "page": 51,
        "sentence": "information in matters related to the impacts of and adaptation to climate change"
      }
    ],
    "recommendations": [
      {
        "score": 0.607964277267456,
        "title": "Reduction in climate action gaps",
        "description": "Measures coverage and completeness."
      },
      {
        "score": 0.4900026023387909,
        "title": "Reduction in climate action resistance",
        "description": "Measures acceptance and support."
      },
      {
        "score": 0.4879744350910187,
        "title": "Improvement in climate risk communication",
        "description": "Measures effectiveness of information sharing."
      }
    ]
  },
  "Top Coordination Mechanisms": {
    "references": [
      {
        "line": 85,
        "page": 65,
        "sentence": "management to promote the climate resilience thereof"
      },
      {
        "line": 53,
        "page": 11,
        "sentence": "Intergovernmental Panel on Climate Change Working Group II"
      },
      {
        "line": 72,
        "page": 77,
        "sentence": "Organise training for local governments on climate change"
      },
      {
        "line": 103,
        "page": 69,
        "sentence": "communications infrastructure according to climate change"
      },
      {
        "line": 84,
        "page": 66,
        "sentence": "Inform the involved parties of the possible climate risks"
      },
      {
        "line": 54,
        "page": 80,
        "sentence": "Data characterising climate"
      },
      {
        "line": 29,
        "page": 44,
        "sentence": "climate resilience;"
      },
      {
        "line": 35,
        "page": 44,
        "sentence": "climate-resilient development"
      },
      {
        "line": 89,
        "page": 77,
        "sentence": "Ensure that climate-resilient development aspects are taken"
      }
    ],
    "recommendations": [
      {
        "score": 0.3153526484966278,
        "title": "Adaptive Management Working Groups",
        "description": "Enable flexible and responsive climate governance approaches."
      },
      {
        "score": 0.25790292024612427,
        "title": "Climate Communication and Media Networks",
        "description": "Ensure accurate and accessible climate information sharing."
      },
      {
        "score": 0.24192114174365997,
        "title": "Climate Adaptation Planning Committees",
        "description": "Develop long-term strategies for community resilience."
      }
    ]
  },
  "Top Socio-ecological Challenges": {
    "references": [
      {
        "line": 17,
        "page": 35,
        "sentence": "Migration of the population due to climate and"
      },
      {
        "line": 17,
        "page": 8,
        "sentence": "Adaptation to climate"
      },
      {
        "line": 8,
        "page": 48,
        "sentence": "the promotion of adaptation to climate change"
      },
      {
        "line": 39,
        "page": 31,
        "sentence": "Climate change risks and potential consequences in the field of civil protection and disaster"
      },
      {
        "line": 24,
        "page": 13,
        "sentence": "climate risks;"
      },
      {
        "line": 53,
        "page": 80,
        "sentence": "to climate risks"
      },
      {
        "line": 31,
        "page": 57,
        "sentence": "of extreme weather events"
      },
      {
        "line": 10,
        "page": 15,
        "sentence": "climate events (extremes)"
      },
      {
        "line": 42,
        "page": 6,
        "sentence": "Extreme climate events"
      }
    ],
    "recommendations": [
      {
        "score": 0.8524788618087769,
        "title": "Climate-induced displacement",
        "description": "Communities forced to migrate due to environmental degradation."
      },
      {
        "score": 0.6360057592391968,
        "title": "Urban climate risks",
        "description": "Increasing flood events, urban heat islands, carbon emissions."
      },
      {
        "score": 0.5754202008247375,
        "title": "Extreme weather events",
        "description": "More frequent and intense hurricanes, heatwaves, and droughts."
      }
    ]
  },
  "Top Community Satisfaction Factors": {
    "references": [
      {
        "line": 34,
        "page": 45,
        "sentence": "disaster preparedness"
      },
      {
        "line": 28,
        "page": 45,
        "sentence": "order to increase the preparedness for efficient response, periodic review of disaster"
      },
      {
        "line": 85,
        "page": 65,
        "sentence": "management to promote the climate resilience thereof"
      },
      {
        "line": 26,
        "page": 50,
        "sentence": "Goals of Adaptation to Climate Change and Action Directions"
      },
      {
        "line": 58,
        "page": 2,
        "sentence": "Goals of Adaptation to Climate Change and Action Directions ....................................................50"
      },
      {
        "line": 13,
        "page": 13,
        "sentence": "the main goal of adaptation to climate change and 6 (six) strategic objectives, 14 (fourteen)"
      },
      {
        "line": 19,
        "page": 71,
        "sentence": "requirements are to be adjusted to the current climate"
      },
      {
        "line": 39,
        "page": 15,
        "sentence": "the future taking into account various climate change scenarios8"
      },
      {
        "line": 36,
        "page": 13,
        "sentence": "new climatic conditions and also, where necessary, introduces effective planning, coordination,"
      }
    ],
    "recommendations": [
      {
        "score": 0.****************,
        "title": "Enhanced community preparedness for climate emergencies.",
        "description": "Building capacity to respond effectively to extreme weather and other climate impacts."
      },
      {
        "score": 0.****************,
        "title": "Better integration of climate action with other community priorities.",
        "description": "Aligning environmental goals with economic, social, and cultural objectives."
      },
      {
        "score": 0.****************,
        "title": "Better preparation for future climate challenges.",
        "description": "Building adaptive capacity for unknown future conditions."
      }
    ]
  },
  "Top Improved Project Resilience Factors": {
    "references": [
      {
        "line": 24,
        "page": 53,
        "sentence": "significant risks and also measures to be implemented to promote adaptation to climate change"
      },
      {
        "line": 44,
        "page": 12,
        "sentence": "Certain measures for adaptation to climate change in order to reduce climate change risks and"
      },
      {
        "line": 72,
        "page": 71,
        "sentence": "Integrate climate change impacts, scenarios and adaptation"
      },
      {
        "line": 29,
        "page": 44,
        "sentence": "climate resilience;"
      },
      {
        "line": 35,
        "page": 44,
        "sentence": "climate-resilient development"
      },
      {
        "line": 26,
        "page": 3,
        "sentence": "adapt and build resilience to climate change and its consequences, and also benefit from the"
      },
      {
        "line": 16,
        "page": 7,
        "sentence": "The climate change scenarios described in the Plan refer to"
      },
      {
        "line": 23,
        "page": 74,
        "sentence": "taking into account climate change scenarios"
      },
      {
        "line": 48,
        "page": 79,
        "sentence": "available data characterising the current climate and future climate change scenarios ensured"
      }
    ],
    "recommendations": [
      {
        "score": 0.****************,
        "title": "Projects minimize carbon emissions and adapt to climate risks.",
        "description": "Ensuring developments reduce greenhouse gas output and incorporate climate adaptation strategies."
      },
      {
        "score": 0.*****************,
        "title": "More research on local climate adaptation needs.",
        "description": "Tailoring resilience strategies to specific regional risks."
      },
      {
        "score": 0.****************,
        "title": "Stronger integration of climate risk assessments in planning.",
        "description": "Using data-driven models to anticipate and mitigate climate risks."
      }
    ]
  }
}

  
  

  
  const API_ENDPOINT = '/recommendation_mapping/api/step-data';

  
  return {
    getFormState: () => formState,
    getResultsState: () => resultsState,
    setResultsState: (newState) => {
      resultsState = newState;
    },
    getApiEndpoint: () => API_ENDPOINT,

    
    showSuccessMessage: function (message) {
      
      const alertHtml =
        '<div class="alert alert-success alert-dismissible fade show" role="alert">' +
        '<i class="fa fa-check-circle me-2"></i>' +
        message +
        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
        '</div>';

      
      $('.alert').remove();

      
      $('#recommendation-form-container').prepend(alertHtml);

      
      setTimeout(function () {
        $('.alert-success').fadeOut();
      }, 5000);
    },

    showErrorMessage: function (message) {
      var alertHtml =
        '<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
        '<i class="fa fa-exclamation-triangle me-2"></i>' +
        message +
        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
        '</div>';

      
      $('.alert').remove();

      
      $('#recommendation-form-container').prepend(alertHtml);

      
      setTimeout(function () {
        $('.alert-danger').fadeOut();
      }, 10000);
    },

    
    fileToBase64: function (file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = (error) => reject(error);
      });
    },
  };
})();
