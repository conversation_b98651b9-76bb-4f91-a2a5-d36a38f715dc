# CEP RMT Component-Based JavaScript Architecture

## Overview

The CEP RMT (Recommendation Mapping Tool) JavaScript codebase has been refactored into a modular, component-based architecture. This structure promotes maintainability, reusability, and easier testing.

## Architecture Structure

```
addons/cep_rmt/static/src/js/
├── common/
│   ├── form_base.js           # Base form state and utilities
│   └── suggestions_handler.js  # Common suggestions and API handling
├── steps/
│   ├── step_1.js              # Step 1: Recommendation Input
│   ├── step_2.js              # Step 2: Stakeholders
│   ├── step_3.js              # Step 3: Implementation Stakeholders
│   ├── step_4.js              # Step 4: Governance Rules
│   ├── step_5.js              # Step 5: Socio-ecological Challenges
│   ├── step_6.js              # Step 6: Coordination Mechanisms
│   ├── step_8.js              # Step 8: Implementation Steps
│   ├── step_9.js              # Step 9: Expected Outcomes
│   ├── step_10.js             # Step 10: Monitoring Indicators
│   └── step_11.js             # Step 11: Feedback Mechanisms
└── form_controller.js         # Main form orchestrator
```

## Component Responsibilities

### 1. Common Components

#### `form_base.js`
- **Purpose**: Central state management and shared utilities
- **Exports**: `window.CEP_RMT.FormBase`
- **Key Features**:
  - Form state management (`formState`)
  - Results state for suggestions (`resultsState`)
  - Step-to-category mapping
  - Common utility functions (file conversion, messaging)
  - API endpoint configuration

#### `suggestions_handler.js`
- **Purpose**: Handles automated suggestions and references
- **Exports**: `window.CEP_RMT.SuggestionsHandler`
- **Key Features**:
  - Dynamic suggestion population
  - References modal management
  - Common API submission for steps 2-11
  - Checkbox event binding

### 2. Step Components

Each step component follows a consistent pattern:

```javascript
window.CEP_RMT.Steps.StepX = (function() {
  const STEP_NUMBER = X;
  const STEP_NAME = 'step_name';

  return {
    init,           // Initialize step-specific functionality
    validate,       // Validate step data
    submit,         // Submit step data to API
    loadData,       // Load existing data into form
    getStepNumber,  // Return step number
    getStepName     // Return step name
  };
})();
```

#### Step-Specific Features:

- **Step 1**: File upload, recommendation processing, polling for results
- **Steps 2, 4-6, 8-11**: Text input + automated suggestions + API submission
- **Step 3**: Multiple stakeholder fields (no suggestions)
- **Steps 7, 12**: Complex forms (to be implemented)

### 3. Form Controller

#### `form_controller.js`
- **Purpose**: Main orchestrator for the multi-step form
- **Key Features**:
  - Step component registration
  - Navigation handling (next/previous)
  - Step validation and submission coordination
  - Progress tracking
  - Common event binding

## API Integration

### Common API Endpoint for Steps 2-11
- **Endpoint**: `/recommendation-mapping/api/step-data`
- **Method**: POST
- **Payload**:
  ```json
  {
    "step_number": 2,
    "step_name": "stakeholders_involved",
    "input_data": {
      "stakeholders_text": "...",
      "selected_stakeholders": [...]
    },
    "record_id": 123
  }
  ```

### Step 1 Special Handling
- **Endpoint**: `/recommendation-mapping/api/process-recommendations`
- **Features**: File upload, RabbitMQ integration, polling

### Additional Endpoints
- `/recommendation-mapping/api/user-data` - Load existing data
- `/recommendation-mapping/api/poll-results/<id>` - Poll processing status

## Usage

### Loading Order (in manifest.py)
```python
'cep_rmt.assets': [
    'https://code.jquery.com/jquery-3.7.1.min.js',
    # Base components (load first)
    'cep_rmt/static/src/js/common/form_base.js',
    'cep_rmt/static/src/js/common/suggestions_handler.js',
    # Step components
    'cep_rmt/static/src/js/steps/step_1.js',
    'cep_rmt/static/src/js/steps/step_2.js',
    # ... other steps
    # Main controller (load last)
    'cep_rmt/static/src/js/form_controller.js',
]
```

### Adding New Steps
1. Create new step file in `steps/` directory
2. Follow the standard step component pattern
3. Register in `form_controller.js`
4. Add to manifest.py assets
5. Update API endpoint mapping if needed

## Benefits

1. **Modularity**: Each step is self-contained
2. **Reusability**: Common functionality shared across components
3. **Maintainability**: Easy to modify individual steps
4. **Testability**: Components can be tested in isolation
5. **Scalability**: Easy to add new steps or modify existing ones
6. **Consistency**: Standardized API for all step components

## Migration Notes

- Replaced monolithic `recommendation_form.js` with component-based structure
- Maintained all existing functionality
- Improved error handling and validation
- Standardized API communication
- Enhanced code organization and readability
