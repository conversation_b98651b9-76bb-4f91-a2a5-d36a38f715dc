<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="survey_list" name="survey List">
        <section class="s_surveys bg-white">
            <div class="container">
                <div class="d-flex flex-column flex-md-row justify-content-md-between my-4 gap-3">
                    <h4 class="fw-700 c-dark-gunmetal pt-1" id="survey-count">Surveys (<span class="survey-count">0</span>)</h4>
                    
                    <div class="mx-auto w-100 w-md-25">
                        <!-- <div class="search input-group">
                            <input class="form-control" id="search-text" type="text" placeholder="Search ..."/>
                            <span class="input-group-append">
                                <button class="btn" type="button" id="search-btn">
                                    <i class="fa fa-search"></i>
                                </button>
                            </span>
                        </div> -->
                    </div>

                    <div class="d-flex flex-row gap-3">
                        <div class="search input-group">
                            <input class="form-control" id="search-text" type="search" placeholder="Search ..."/>
                            <span class="input-group-append">
                                <button class="btn" type="button" id="search-btn">
                                    <i class="fa fa-search"></i>
                                </button>
                            </span>
                            <!-- Dropdown for suggestions -->
                            <ul class="dropdown-menu position-absolute w-100 p-0" id="suggestion-list" style="display: none; max-height: 200px; overflow-y: auto; top: 42px">
                                <!-- Suggestions will be injected here -->
                            </ul>
                        </div>
                        <!-- <div class="dropdown">
                            <button class="btn btn-md btn-filter dropdown-toggle" type="button" id="dropdownMenuButton1"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                All surveys
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                <li><a class="dropdown-item" href="#">Action</a></li>
                                <li><a class="dropdown-item" href="#">Another action</a></li>
                                <li><a class="dropdown-item" href="#">Something else here</a></li>
                            </ul>
                        </div>

                        <div class="dropdown">
                            <button class="btn btn-md btn-filter dropdown-toggle" type="button" id="dropdownMenuButton2"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                Tags
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton2">
                                <li><a class="dropdown-item" href="#">Action</a></li>
                                <li><a class="dropdown-item" href="#">Another action</a></li>
                                <li><a class="dropdown-item" href="#">Something else here</a></li>
                            </ul>
                        </div> -->
                    </div>
                </div>

                <div class="row mt-4" id="surveys">
                    <!-- survey cards here -->
                </div>

                <nav class="mt-4">
                    <ul class="pagination justify-content-center">
                        
                    </ul>
                </nav>
            </div>
        </section>
    </template>
</odoo>