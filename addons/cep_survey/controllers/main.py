from odoo import http


class SurveyController(http.Controller):

    @http.route('/surveys', type='http', auth='public', website=True)
    def list(self, **kwargs):

        # Fetch survey records from Odoo database
        Survey = http.request.env['survey.survey'].sudo()
        surveys = Survey.search([('access_mode', '=', 'public')])

        # Render template with survey data
        return http.request.render('cep_survey.list', {'data': surveys})

    # Get survey list by page no number API
    @http.route('/survey-list/<int:page_no>/<int:per_page>', type='json', auth='public', website=True, methods=['POST'])
    def survey_list(self, page_no, per_page, **kwargs):
        print('page_no------', page_no)
        print('per_page------', per_page)
        offset = (page_no - 1) * per_page
        if offset < 0:
            offset = 0
        surveys = http.request.env['survey.survey'].sudo().search(
            [('access_mode', '=', 'public')], offset=offset, limit=per_page)
        survey_list = []
        for survey in surveys:
            survey_object = {
                "survey_id": survey.id,
                "survey_title": survey.title,
                "survey_logo": survey.logo,
                "survey_cover_photo": survey.background_image_url,
                "survey_create_date": survey.create_date.strftime("%d %b %Y"),
                "survey_access_token": survey.access_token,
                "survey_participants_count": survey.answer_count,
            }
            survey_list.append(survey_object)

        # get survey count
        survey_count = http.request.env['survey.survey'].sudo(
        ).search_count([('access_mode', '=', 'public')])

        total_pages = (survey_count + per_page - 1) // per_page

        return {
            "survey_list": survey_list,
            "total_pages": total_pages,
            "survey_count": survey_count
        }

    # Survey search API
    @http.route('/survey-search', type='json', auth='public', website=True, methods=['POST'])
    def survey_search(self, **kwargs):
        keyword = kwargs['keyword']
        print('keyword------', keyword)
        surveys = http.request.env['survey.survey'].sudo().search([
            '|',
            ('title', 'ilike', keyword),
            ('description', 'ilike', keyword),
            ('access_mode', '=', 'public'),
        ])
        survey_list = []
        for survey in surveys:
            survey_object = {
                "survey_id": survey.id,
                "survey_title": survey.title,
                "survey_cover_photo": survey.background_image_url,
                "survey_create_date": survey.create_date.strftime("%d %b %Y"),
                "survey_access_token": survey.access_token,
                "survey_participants_count": survey.answer_count,
            }
            survey_list.append(survey_object)

        # get survey count
        survey_count = len(survey_list)

        return {
            "survey_list": survey_list,
            "survey_count": survey_count
        }

    # Proposal Search suggestions API
    @http.route('/survey/search_suggestions', type='json', auth='public', methods=['POST'], website=True)
    def survey_suggestions(self, **kwargs):
        keyword = kwargs['keyword']
        surveys = http.request.env['survey.survey'].sudo().search([
            '|',
            ('title', 'ilike', keyword),
            ('description', 'ilike', keyword),
            ('access_mode', '=', 'public'),
        ])

        survey_list = []
        for survey in surveys:
            survey_object = {
                "survey_access_token": survey.access_token,
                "survey_title": survey.title,
            }
            survey_list.append(survey_object)

        return {
            "status": "success",
            "surveys": survey_list,
        }
