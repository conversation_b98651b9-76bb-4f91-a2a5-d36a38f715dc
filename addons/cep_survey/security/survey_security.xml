<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.rule" id="survey_multi_company_rule">
        <field name="name">Survey multi-company</field>
        <field name="model_id" ref="model_survey_survey"/>
        <field name="domain_force">['|', ('company_ids', 'in', user.company_ids.ids), ('company_ids', '=', False)]</field>
    </record>

    <record model="ir.rule" id="survey_question_multi_company_rule">
        <field name="name">Survey question multi-company</field>
        <field name="model_id" ref="survey.model_survey_question"/>
        <field name="domain_force">['|', ('survey_id.company_ids', '=', False), ('survey_id.company_ids', 'in',
            user.company_ids.ids)]</field>
    </record>

    <record model="ir.rule" id="survey_question_answer_multi_company_rule">
        <field name="name">Survey question answer multi-company</field>
        <field name="model_id" ref="survey.model_survey_question_answer"/>
        <field name="domain_force">[
            '|',
            '&amp;',
            ('question_id', '!=', False),
            '|',
            ('question_id.survey_id.company_ids', '=', False),
            ('question_id.survey_id.company_ids', 'in', user.company_ids.ids),
            '&amp;',
            ('matrix_question_id', '!=', False),
            '|',
            ('matrix_question_id.survey_id.company_ids', '=', False),
            ('matrix_question_id.survey_id.company_ids', 'in', user.company_ids.ids)
            ]</field>
    </record>


    <record model="ir.rule" id="survey_user_input_multi_company_rule">
        <field name="name">Survey user input multi-company</field>
        <field name="model_id" ref="survey.model_survey_user_input"/>
        <field name="domain_force">['|', ('survey_id.company_ids', '=', False), ('survey_id.company_ids', 'in',
            user.company_ids.ids)]</field>
    </record>

    <record model="ir.rule" id="survey_user_input_line_multi_company_rule">
        <field name="name">Survey user input line multi-company</field>
        <field name="model_id" ref="survey.model_survey_user_input_line"/>
        <field name="domain_force">['|', ('user_input_id.survey_id.company_ids', '=', False),
            ('user_input_id.survey_id.company_ids', 'in', user.company_ids.ids)]</field>
    </record>
</odoo>