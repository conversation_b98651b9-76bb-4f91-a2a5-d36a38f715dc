# -*- coding: utf-8 -*-
from odoo import api, fields, models


class Survey(models.Model):
    _inherit = 'survey.survey'

    logo = fields.Binary(string='Logo', required=True, attachment=True)
    logo_name =fields.Char(string='Logo Name', )
    company_ids = fields.Many2many('res.company', required=True, default=lambda self: self.env.user.company_ids, string="Allowed Companies")
    user_id = fields.Many2one('res.users', string='Responsible', domain=[('share', '=', False)], tracking=True, readonly=True, default=lambda self: self.env.user)