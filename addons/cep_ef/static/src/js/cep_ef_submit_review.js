$(document).ready(function () {
  $('#reviewed_file').on('change', function () {
    if (this.files && this.files.length > 0) {
      var fileName = this.files[0].name;
      $('#reviewed_filename').val(fileName);
    }
  });

  $('#review_submit_button').click(function () {
    const form = $('#review_submit_form')[0];

    if (form.checkValidity()) {
      $('#delete-review-modal')
        .addClass('show modal_shown')
        .css('display', 'block')
        .attr({
          'aria-modal': 'true',
          'aria-hidden': 'false',
        });
    } else {
      form.reportValidity();
    }
  });

  $('#delete-review-modal-yes').click(function () {
    $('#review_submit_form').submit();
  });

  $('#delete-review-modal-no').click(function () {
    $('#delete-review-modal')
      .removeClass('show modal_shown')
      .css('display', 'none')
      .attr({
        'aria-modal': 'false',
        'aria-hidden': 'true',
      });
  });
});
