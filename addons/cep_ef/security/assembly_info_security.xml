<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.rule" id="assembly_info_user_access_rule">
        <field name="name">Assembly Info User Access Rule</field>
        <field name="model_id" ref="model_cep_ef_assembly_info"/>
        <field name="domain_force">[('create_uid', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
    </record>
</odoo>