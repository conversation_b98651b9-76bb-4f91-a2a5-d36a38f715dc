from odoo import models, fields, api

class Surveys(models.Model):
    _name = 'cep.ef.surveys'
    _description = 'Surveys'
    _rec_name = 'survey_name'

    survey_name = fields.Char('Survey Name', required=True)
    survey_link = fields.Char('Survey Link', required=True)
    status = fields.Selection([
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
    ], string='Status', default='not_started', required=True)

    activity_id = fields.Many2one('cep.ef.activities', string='Activity', required=True, ondelete='cascade')
    
    def action_send_to(self):
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'survey.send.to.wizard',
            'view_mode': 'form',
            'view_id': self.env.ref('cep_ef.view_survey_send_to_wizard_form').id,
            'target': 'new',
            'context': {
                'default_survey_id': self.id,
            }
        }