from odoo import fields, models, api
from odoo.exceptions import ValidationError

from datetime import datetime, timedelta

class AssemblyInfo(models.Model):
    _name = "cep.ef.assembly.info"
    _description = "CEP EF Assembly Info"
    _rec_name = 'assembly_title'

    assembly_title = fields.Char('Assembly Title', required=True, translate=True)
    cover_photo = fields.Image("Cover Photo")
    assembly_summary_public = fields.Html('Public Summary', required=True, )
    assembly_summary_executive = fields.Html('Executive Summary', required=True)
    location = fields.Char(string='Location', required=True)
    start_date = fields.Date(string='Start Date', default=lambda self: (datetime.now()).strftime('%Y-%m-%d'), required=True)
    end_date = fields.Date(string='End Date', default=lambda self: (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d'), required=True)
    commissioners = fields.Char(string='Commissioners')

    organization_ids = fields.One2many('cep.ef.organizations', 'assembly_info_id', string='Organizations')
    expert_ids = fields.One2many('cep.ef.experts', 'assembly_info_id', string='Experts')
    facilitator_ids = fields.One2many('cep.ef.facilitators', 'assembly_info_id', string='Facilitators')
    activity_ids = fields.One2many('cep.ef.activities', 'assembly_info_id', string='Activities')
    participant_ids = fields.One2many('cep.ef.participants', 'assembly_info_id', string='Participants')

    def action_open_activity_tree_view(self):
        create_view_id = self.env.ref('cep_ef.view_cep_ef_activity_form').id
        tree_view_id = self.env.ref('cep_ef.view_cep_ef_activity_tree').id
        calendar_view_id = self.env.ref('cep_ef.view_cep_ef_activity_calendar').id
        return {
            'name': 'Activity List',
            'view_mode': 'tree',
            'views': [(tree_view_id, 'tree'), (create_view_id, 'form'), (calendar_view_id, 'calendar')],
            'res_model': 'cep.ef.activities',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'domain': [('assembly_info_id', '=', self.id)],
            'context': {
                'default_assembly_info_id': self.id,  # Set the default value for assembly_info_id
            },
        }


    