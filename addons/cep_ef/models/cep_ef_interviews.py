from odoo import models, fields, api

class Interviews(models.Model):
    _name = 'cep.ef.interviews'
    _description = 'Interviews'
    _rec_name = 'interview_title'

    interview_title = fields.Char('Interview Title', required=True)
    questionnaire_link = fields.Char('Questionnaire Link', required=True)
    interview_format = fields.Char('Interview Format')
    recording_format = fields.Char('Recording Format')

    activity_id = fields.Many2one('cep.ef.activities', string='Activity', required=True)

    