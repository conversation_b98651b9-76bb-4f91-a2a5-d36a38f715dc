from odoo import models, fields, api

class DocReviewReviewer(models.Model):
    _name = 'cep.ef.doc_review_reviewer'
    _description = 'Document Review - Reviewer Relation'
    _rec_name = 'display_name'

    doc_review_id = fields.Many2one('cep.ef.doc_review', string='Document Title', required=True, ondelete='cascade')
    doc_reviewer_id = fields.Many2one('cep.ef.doc.reviewer', string='Reviewer', required=True, ondelete='cascade')
    reviewed_file = fields.Binary(string='Reviewed File')
    reviewed_filename =fields.Char(string='File Name', attachment=True)
    reviewer_comments = fields.Text(string='Reviewer Comments')
    status = fields.Selection([
        ('pending', 'Pending'),
        ('submitted', 'Submitted'),
    ], string='Status', default='pending')
    download_url = fields.Char(string='Document URL', compute='_compute_document_url')

    @api.depends('reviewed_file')
    def _compute_document_url(self):
        for record in self:
            if record.reviewed_file:
                record.download_url = f'/web/content/{record._name}/{record.id}/reviewed_file?download=true'
            else:
                record.download_url = False

    display_name = fields.Char(compute='_compute_display_name')

    @api.depends('doc_review_id', 'doc_reviewer_id')
    def _compute_display_name(self):
        for record in self:
            record.display_name = 'Submitted Review Details'

    # @api.model
    # def create(self, vals):
    #     # Set a value for status if it's not already set in vals
    #     if 'status' not in vals:
    #         vals['status'] = 'pending'
    #     return super(DocReviewReviewerRel, self).create(vals)
