from odoo import models, fields, api
from datetime import datetime, timedelta

class Deliverables(models.Model):
    _name = 'cep.ef.deliverables'
    _description = 'Deliverables'
    _rec_name = 'deliverable_name'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    deliverable_name = fields.Char(string='Deliverable Name', required=True, translate=True)
    deadline = fields.Date(string='Deadline', default=lambda self: (datetime.now()).strftime('%Y-%m-%d'), required=True)

    activity_id = fields.Many2one('cep.ef.activities', string='Activity', required=True, ondelete='cascade')

    # Deliverable CRUD operations logging in the Activity chatter
    def write(self, vals):
        for record in self:
            changes = []
            for field, new_value in vals.items():
                if field in record._fields:
                    old_value = record[field]
                    field_name = record._fields[field].string
                    # Convert relational fields to display names for better readability
                    if record._fields[field].type in ['many2one', 'one2many', 'many2many']:
                        old_value = old_value.name if old_value else 'False'
                        new_value = self.env[record._fields[field].comodel_name].browse(new_value).name if new_value else 'False'
                    changes.append(f"{field_name}: {old_value} <i class=\"fa fa-long-arrow-right\" aria-hidden=\"true\"></i> <span class=\"o_TrackingValue_newValue me-1 fw-bold text-info\">{new_value}</span>")

            if changes:
                change_message = "<br/>".join(changes)
                record.activity_id.message_post(
                    body=f"Deliverable '{record.deliverable_name}' has been updated.<br/>{change_message}"
                )

        return super(Deliverables, self).write(vals)

    def unlink(self):
        for record in self:
            record.activity_id.message_post(
                body=f"Deliverable '{record.deliverable_name}' has been deleted."
            )
        return super(Deliverables, self).unlink()

    @api.model
    def create(self, vals):
        record = super(Deliverables, self).create(vals)
        record.activity_id.message_post(
            body=f"Deliverable '{record.deliverable_name}' has been created."
        )
        return record