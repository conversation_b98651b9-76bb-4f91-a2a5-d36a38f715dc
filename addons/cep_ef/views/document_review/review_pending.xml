<?xml version="1.0" encoding="UTF-8"?>

<odoo>

    <template id="cep_ef.review_pending" name="Review Pending List">
        <t t-call="website.layout">
            <t t-set="title">Review Pending Documents</t>

            <t t-set="head">
                <t t-call-assets="web.assets_common"/>
                <t t-call-assets="web.assets_frontend"/>
                <t t-call-assets="cep_user.cep_user_portal_table_view"/>

                <!-- DataTables CSS -->
                <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet"/>
            </t>

            <br/>
            <div class="oe_structure">
                <div class="container">
                    <main>
                        <section class="container my-5">
                            <h3>Review Pending Documents</h3>
                            <hr class="border-primary"/>

                            <table id="portal-table" class="table table-striped shadow" style="width: 100%">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Title</th>
                                        <th>File</th>
                                        <th>Deadline</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <t t-set="serial_no" t-value="1"/>

                                    <t t-foreach="pending_reviews_data" t-as="review_data">
                                        <tr>
                                            <td>
                                                <t t-esc="serial_no"/>
                                            </td>
                                            <td>
                                                <t t-esc="review_data['doc_review'].document_title"/>
                                            </td>
                                            <td>
                                                <a t-att-href="review_data['doc_review'].download_url"
                                                    t-att-download="review_data['doc_review'].document_title">
                                                    <span class="review_download">
                                                        <t t-esc="review_data['doc_review'].document_filename"/>
                                                    </span>
                                                    <i class="fa fa-download"></i>
                                                </a>
                                            </td>
                                            <td>
                                                <t t-esc="review_data['doc_review'].review_deadline"/>
                                            </td>
                                            <td>
                                                <a
                                                    t-att-href="'submit-review/' + str(review_data['doc_review'].id) + '/' + str(review_data['reviewer_id']) + '/view'"
                                                    class="btn btn-sm btn-success">Submit your review</a>
                                            </td>
                                        </tr>

                                        <t t-set="serial_no" t-value="serial_no + 1"/>
                                    </t>
                                </tbody>
                            </table>
                        </section>
                    </main>
                </div>
            </div>

            <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
            <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
        </t>
    </template>

</odoo>