<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_ef_assembly_info_search" model="ir.ui.view">
        <field name="name">cep.ef.assembly.info.search</field>
        <field name="model">cep.ef.assembly.info</field>
        <field name="arch" type="xml">
            <search string="CEP EF Assembly Info">
                <field name="assembly_title"/>
                <field name="assembly_summary_public"/>
                <field name="assembly_summary_executive"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_ef_assembly_info_tree" model="ir.ui.view">
        <field name="name">cep.ef.assembly.info.tree</field>
        <field name="model">cep.ef.assembly.info</field>
        <field name="arch" type="xml">
            <tree>
                <field name="assembly_title"/>
                <field name="start_date"/>
                <field name="end_date"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_ef_assembly_info_form" model="ir.ui.view">
        <field name="name">cep.ef.assembly.info.form</field>
        <field name="model">cep.ef.assembly.info</field>
        <field name="arch" type="xml">
            <form string="Assembly Info">
                <header>
                    <!-- Add a button box on top of the form view -->
                    <!-- <button string="Phases" class="oe_highlight" icon="fa-bolt" type="object" name="action_open_project_phase_form_view"/> -->
                    <button string="Activities" class="oe_highlight" icon="fa-check" type="object" name="action_open_activity_tree_view"/>
                </header>
                <sheet>
                    <h3>Assembly Info</h3>
                    <hr/>
                    <field name="cover_photo" widget="image" class="oe_avatar"/>
                     <group>
                        <field name="assembly_title"/>
                        <field name="assembly_summary_public"/>
                        <field name="assembly_summary_executive"/>
                        <!-- <field name="tag_ids" widget="many2many_tags"/> -->
                        <field name="location"/>
                        <field name="commissioners"/>
                        <field name="start_date"/>
                        <field name="end_date"/>
                    </group>
                    <!-- HTML header styled as <h5> -->
                    <div class="o_form_label o_form_field o_form_field_text">
                        <h4>Relevant Authorities:</h4>
                    </div>
                    <notebook>
                        <!-- Organizations -->
                        <page name="organizations" string="Organizations">
                            <field name="organization_ids" nolabel="1">
                                <tree>
                                    <field name="name"/>
                                    <field name="email"/>
                                    <field name="address"/>
                                    <field name="website"/>
                                    <field name="role"/>
                                </tree>
                                <form>
                                    <group>
                                        <field name="name"/>
                                        <field name="email" widget="email"/>
                                        <field name="address"/>
                                        <field name="website"/>
                                        <field name="role"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <!-- Experts -->
                        <page name="experts" string="Experts">
                            <field name="expert_ids" nolabel="1">
                                <tree>
                                    <field name="name"/>
                                    <field name="email"/>
                                    <field name="role"/>
                                    <field name="task"/>
                                </tree>
                                <form>
                                    <group>
                                        <field name="name"/>
                                        <field name="email" widget="email"/>
                                        <field name="role"/>
                                        <field name="background"/>
                                        <field name="task"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <!-- Facilitators -->
                        <page name="facilitators" string="Facilitators">
                            <field name="facilitator_ids" nolabel="1">
                                <tree>
                                    <field name="name"/>
                                    <field name="email" widget="email"/>
                                    <field name="role"/>
                                    <field name="task"/>
                                </tree>
                                <form>
                                    <group>
                                        <field name="name"/>
                                        <field name="email" widget="email"/>
                                        <field name="role"/>
                                        <field name="background"/>
                                        <field name="task"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <!-- Participants -->
                        <page name="participants" string="Other Participants">
                            <field name="participant_ids" nolabel="1">
                                <tree>
                                    <field name="name"/>
                                    <field name="email"/>
                                    <field name="role"/>
                                    <field name="task"/>
                                </tree>
                                <form>
                                    <group>
                                        <field name="name"/>
                                        <field name="email" widget="email"/>
                                        <field name="role"/>
                                        <field name="background"/>
                                        <field name="task"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Calender View -->
    <record id="view_cep_ef_assembly_info_calendar" model="ir.ui.view">
        <field name="name">cep.ef.assembly.info.calendar</field>
        <field name="model">cep.ef.assembly.info</field>
        <field name="arch" type="xml">
            <calendar string="Assembly Info Calendar" date_start="start_date">
                <field name="assembly_title"/>
                <!-- <field name="phase_id"/>
                <field name="criteria_id"/>
                <field name="start_date"/>
                <field name="end_date"/> -->
            </calendar>
        </field>
    </record>

    <!-- action -->
    <record id='cep_ef_assembly_info_action' model='ir.actions.act_window'>
        <field name="name">Assembly Info</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.ef.assembly.info</field>
        <field name="view_mode">tree,form,calendar</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new Assembly
            </p>
        </field>
    </record>

    <!-- menu -->
    <menuitem id="cep_ef_root" name="CA Evaluation and Monitoring" sequence="0">
        <menuitem id="cep_ef_assembly_info" name="Assembly Info" action="cep_ef_assembly_info_action"/>
    </menuitem>
</odoo>