<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- download button server action, needs to be defined before the tree view -->
    <record id="action_download_reviewed_file" model="ir.actions.server">
        <field name="name">Download Reviewed File</field>
        <field name="model_id" ref="model_cep_ef_doc_reviewer"/>
        <field name="binding_model_id" ref="model_cep_ef_doc_reviewer"/>
        <field name="code">
            action = {
            'type': 'ir.actions.act_url',
            'url': record.download_url,
            'target': 'self',
            }
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_ef_submitted_reviews_tree" model="ir.ui.view">
        <field name="name">cep.ef.doc_review_reviewer.tree</field>
        <field name="model">cep.ef.doc_review_reviewer</field>
        <field name="arch" type="xml">
            <tree>
                <field name="doc_review_id"/>
                <field name="doc_reviewer_id"/>
                <!-- <field name="status" widget="badge" decoration-danger="status == 'pending'"
                decoration-success="status == 'submitted'"/> -->
                <!-- <field name="reviewer_comments"/> -->
                <!-- <button name="%(action_download_reviewed_file)d" string="Download Review" type="action"
                icon="fa-download"/> -->
            </tree>
        </field>
    </record>

    <!-- Form view -->
    <record id="view_cep_ef_submitted_reviews_form" model="ir.ui.view">
        <field name="name">cep.ef.doc_review_reviewer.form</field>
        <field name="model">cep.ef.doc_review_reviewer</field>
        <field name="arch" type="xml">
            <form string="Submitted Reviews">
                <sheet>
                    <group>
                        <field name="doc_review_id" options="{'no_create': True, 'no_open': True}" readonly="1"/>
                        <field name="doc_reviewer_id" options="{'no_create': True, 'no_open': True}" readonly="1"/>
                        <field name="reviewed_file" filename="reviewed_filename"
                            options="{'no_create': True, 'no_open': True}" readonly="1"/>
                        <field name="reviewed_filename" filename="reviewed_filename" invisible="1"/>
                        <field name="reviewer_comments" options="{'no_create': True, 'no_open': True}" readonly="1"/>
                        <!-- <field name="status"/>
                        <field name="reviewer_comments"/>
                        <field name="reviewed_file"/> -->
                    </group>
                </sheet>
            </form>
        </field>
    </record>
</odoo> 