<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_ef_phases_search" model="ir.ui.view">
        <field name="name">cep.ef.phases.search</field>
        <field name="model">cep.ef.phases</field>
        <field name="arch" type="xml">
            <search string="CEP EF Phase">
                <field name="phase_name"/>
                <field name="description"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_ef_phase_tree" model="ir.ui.view">
        <field name="name">cep.ef.phases.tree</field>
        <field name="model">cep.ef.phases</field>
        <field name="arch" type="xml">
            <tree>
                <field name="phase_name"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_ef_phase_form" model="ir.ui.view">
        <field name="name">cep.ef.phases.form</field>
        <field name="model">cep.ef.phases</field>
        <field name="arch" type="xml">
            <form string="Phase">
                <header>
                    <!-- Add a button box on top of the form view -->
                    <!-- <button string="Phases" class="oe_highlight" icon="fa-bolt" type="object" name="action_open_project_phase_form_view"/> -->
                    <!-- <button string="Button 2" class="oe_highlight" icon="fa-check"/> -->
                </header>
                <sheet>
                    <h3>Phase</h3>
                    <hr/>
                    <!-- <field name="cover_photo" widget="image" class="oe_avatar"/> -->
                     <group>
                        <field name="phase_name"/>
                        <field name="description"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id='cep_ef_phase_action' model='ir.actions.act_window'>
        <field name="name">Phase</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.ef.phases</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new Phase
            </p>
        </field>
    </record>

    <menuitem id="cep_ef_phase" name="Phase" parent="cep_ef_root" action="cep_ef_phase_action"/>
</odoo>