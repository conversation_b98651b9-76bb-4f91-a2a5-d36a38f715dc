<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_ef_criteria_search" model="ir.ui.view">
        <field name="name">cep.ef.criteria.search</field>
        <field name="model">cep.ef.criteria</field>
        <field name="arch" type="xml">
            <search string="CEP EF Criteria">
                <field name="criteria"/>
                <field name="description"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_ef_criteria_tree" model="ir.ui.view">
        <field name="name">cep.ef.criteria.tree</field>
        <field name="model">cep.ef.criteria</field>
        <field name="arch" type="xml">
            <tree>
                <field name="criteria"/>
                <field name="description"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_ef_criteria_form" model="ir.ui.view">
        <field name="name">cep.ef.criteria.form</field>
        <field name="model">cep.ef.criteria</field>
        <field name="arch" type="xml">
            <form string="Criteria">
                <header>
                    <!-- Add a button box on top of the form view -->
                    <!-- <button string="Phases" class="oe_highlight" icon="fa-bolt" type="object"Assembly Info
￼
￼NEW
￼Filters
￼Group By
￼Favorites
￼￼

                    name="action_open_project_phase_form_view"/> -->
                    <!-- <button string="Button 2" class="oe_highlight" icon="fa-check"/> -->
                </header>
                <sheet>
                    <h3>Criteria</h3>
                    <hr/>
                    <!-- <field name="cover_photo" widget="image" class="oe_avatar"/> -->
                    <group>
                        <field name="criteria"/>
                        <field name="phase_id" options="{'no_create': True}"/>
                        <field name="description"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="cep_ef_criteria_action" model="ir.actions.act_window">
        <field name="name">CEP EF Criteria</field>
        <field name="res_model">cep.ef.criteria</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new Criteria
            </p>
        </field>
    </record>

    <menuitem id="cep_ef_criteria" name="Criteria" parent="cep_ef_root" action="cep_ef_criteria_action"/>
</odoo>