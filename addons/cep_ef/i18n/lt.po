# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_ef
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20241017\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-10 12:24+0000\n"
"PO-Revision-Date: 2024-12-10 12:24+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Action"
msgstr "Veiksmas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_needaction
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_needaction
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_needaction
msgid "Action Needed"
msgstr "Reikalingi veiksmai"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_activities
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__activity_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_ids
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Activities"
msgstr "Veiklos"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__activity_id
#: model:ir.ui.menu,name:cep_ef.cep_ef_activity
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Activity"
msgstr "Veikla"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_exception_decoration
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_exception_decoration
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Veiklos išimtis"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_name
msgid "Activity Name"
msgstr "Veiklos pavadinimas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_state
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_state
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_state
msgid "Activity State"
msgstr "Veiklos būklė"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_type_icon
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_type_icon
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_type_icon
msgid "Activity Type Icon"
msgstr "Veiklos tipo piktograma"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__address
msgid "Address"
msgstr "Adresas"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__all
msgid "All"
msgstr "Visi"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Are you sure you want to submit your review ?"
msgstr "Ar tikrai norite pateikti apžvalgą?"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.cep_ef_assembly_info_action
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__assembly_info_id
#: model:ir.ui.menu,name:cep_ef.cep_ef_assembly_info
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Assembly Info"
msgstr "Asamblėjos informacija"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_calendar
msgid "Assembly Info Calendar"
msgstr "Informacijos apie asamblėją kalendorius"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__assembly_title
msgid "Assembly Title"
msgstr "Asamblėjos pavadinimas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_attachment_count
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_attachment_count
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_attachment_count
msgid "Attachment Count"
msgstr "Priedų skaičius"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__background
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__background
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__background
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__background
msgid "Background"
msgstr "Fonas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__budget
msgid "Budget"
msgstr "Biudžetas"

#. module: cep_ef
#: model:ir.ui.menu,name:cep_ef.cep_ef_root
msgid "CA Evaluation and Monitoring"
msgstr "Klimato asamblėjos vertinimas ir stebėsena"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.cep_ef_activities_action
msgid "CEP EF Activities"
msgstr "CEP EF veiklos"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activities_search
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_calendar
msgid "CEP EF Activity"
msgstr "CEP EF veikla"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_assembly_info
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_search
msgid "CEP EF Assembly Info"
msgstr "CEP EF asamblėjos informacija"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.cep_ef_criteria_action
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_criteria_search
msgid "CEP EF Criteria"
msgstr "CEP EF kriterijai"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_organizations
msgid "CEP EF Organizations"
msgstr "CEP EF organizacijos"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_phases
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_phases_search
msgid "CEP EF Phase"
msgstr "CEP EF etapas"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_survey_send_to_wizard_form
msgid "Cancel"
msgstr "Atšaukti"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Comment"
msgstr "Komentaras"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__comments
msgid "Comments"
msgstr "Komentarai"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__commissioners
msgid "Commissioners"
msgstr "Komisijos nariai"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_activities__status__completed
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review__status__completed
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_surveys__status__completed
msgid "Completed"
msgstr "Užbaigta"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__cover_photo
msgid "Cover Photo"
msgstr "Viršelio nuotrauka"

#. module: cep_ef
#: model_terms:ir.actions.act_window,help:cep_ef.cep_ef_assembly_info_action
msgid "Create a new Assembly"
msgstr "Sukurti naują asamblėją"

#. module: cep_ef
#: model_terms:ir.actions.act_window,help:cep_ef.cep_ef_criteria_action
msgid "Create a new Criteria"
msgstr "Sukurti naują kriterijų"

#. module: cep_ef
#: model_terms:ir.actions.act_window,help:cep_ef.cep_ef_phase_action
msgid "Create a new Phase"
msgstr "Sukurti naują etapą"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__create_date
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_criteria
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__criteria_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__criteria
#: model:ir.ui.menu,name:cep_ef.cep_ef_criteria
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_criteria_form
msgid "Criteria"
msgstr "Kriterijai"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__deadline
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Deadline"
msgstr "Terminas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__deliverable_name
msgid "Deliverable Name"
msgstr "Pristatomo objekto pavadinimas"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_deliverables
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__deliverable_ids
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Deliverables"
msgstr "Pristatomi produktai"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__description
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__description
msgid "Description"
msgstr "Aprašymas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__display_name
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__display_name
msgid "Display Name"
msgstr "Pavadinimas"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_doc_review
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Document Review"
msgstr "Dokumento peržiūra"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_doc_review_reviewer
msgid "Document Review - Reviewer Relation"
msgstr "Dokumento peržiūra - recenzento ryšys"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_doc_reviewer
msgid "Document Reviewers"
msgstr "Dokumento recenzentai"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__doc_review_ids
msgid "Document Reviews"
msgstr "Dokumentų peržiūros"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__document_source
msgid "Document Source"
msgstr "Dokumento šaltinis"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__document_title
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__doc_review_id
msgid "Document Title"
msgstr "Dokumento pavadinimas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__download_url
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__download_url
msgid "Document URL"
msgstr "Dokumento URL"

#. module: cep_ef
#: model:ir.actions.server,name:cep_ef.action_download_reviewed_file
msgid "Download Reviewed File"
msgstr "Atsisiųsti peržiūrėtą failą"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__user_email
msgid "Email"
msgstr "El. paštas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__end_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__end_date
msgid "End Date"
msgstr "Pabaigos data"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__assembly_summary_executive
msgid "Executive Summary"
msgstr "Santrauka"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__expert_ids
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__experts
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Experts"
msgstr "Ekspertai"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_facilitators
msgid "Facilitator"
msgstr "Tarpininkas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__facilitator_ids
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__facilitators
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Facilitators"
msgstr "Tarpininkai"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "File"
msgstr "Byla"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__document_filename
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__reviewed_filename
msgid "File Name"
msgstr "Failo pavadinimas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__final_document
msgid "Final Document"
msgstr "Galutinis dokumentas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__final_document_filename
msgid "Final Document File Name"
msgstr "Galutinio dokumento failo pavadinimas"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_final_documents_and_comments
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__final_documnet_comment_ids
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Final Documents and Comments"
msgstr "Galutiniai dokumentai ir pastabos"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_follower_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_follower_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_follower_ids
msgid "Followers"
msgstr "Sekėjai"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_partner_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_partner_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekėjai (partneriai)"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__activity_type_icon
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__activity_type_icon
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Šriftas Nuostabi piktograma, pvz., fa-tasks"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__has_message
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__has_message
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__has_message
msgid "Has Message"
msgstr "Turi pranešimą"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__id
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__id
msgid "ID"
msgstr ""

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_exception_icon
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_exception_icon
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__activity_exception_icon
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__activity_exception_icon
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona, nurodanti išimties veiklą."

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__message_needaction
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__message_needaction
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jei pažymėta, nauji pranešimai reikalauja jūsų dėmesio."

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__message_has_error
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__message_has_error
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Jei pažymėta, kai kurie pranešimai turi pristatymo klaidą."

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_activities__status__in_progress
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review__status__in_progress
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_surveys__status__in_progress
msgid "In Progress"
msgstr "Vyksta"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__interview_format
msgid "Interview Format"
msgstr "Interviu formatas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__interview_title
msgid "Interview Title"
msgstr "Interviu pavadinimas"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_interviews
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__interview_ids
msgid "Interviews"
msgstr "Interviu"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_is_follower
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_is_follower
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_is_follower
msgid "Is Follower"
msgstr "Yra sekėjas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys____last_update
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą pakeista"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__write_date
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__location
msgid "Location"
msgstr "Vieta"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_main_attachment_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_main_attachment_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pagrindinis priedas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_has_error
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_has_error
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_has_error
msgid "Message Delivery error"
msgstr "Pranešimo pristatymo klaida"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_ids
msgid "Messages"
msgstr "Žinutės"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__is_milestone
msgid "Milestone"
msgstr "Etapas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__my_activity_date_deadline
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__my_activity_date_deadline
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mano veiklos terminas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__responsible_member_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__user_name
msgid "Name"
msgstr "Pavadinimas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_date_deadline
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_date_deadline
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Kitos veiklos galutinis terminas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_summary
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_summary
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_summary
msgid "Next Activity Summary"
msgstr "Kitos veiklos santrauka"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_type_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_type_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_type_id
msgid "Next Activity Type"
msgstr "Kitos veiklos tipas"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "No"
msgstr ""

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_activities__status__not_started
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review__status__not_started
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_surveys__status__not_started
msgid "Not Started"
msgstr "Nepradėta"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_needaction_counter
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_needaction_counter
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_needaction_counter
msgid "Number of Actions"
msgstr "Veiksmų skaičius"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_has_error_counter
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_has_error_counter
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_has_error_counter
msgid "Number of errors"
msgstr "Klaidų skaičius"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__message_needaction_counter
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__message_needaction_counter
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Veiksmų reikalaujančių pranešimų skaičius"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__message_has_error_counter
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__message_has_error_counter
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Pranešimų su pristatymo klaida skaičius"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid ""
"Once submitted, it cannot\n"
"                                be edited or changed!"
msgstr ""

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__organization_ids
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__organizations
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Organizations"
msgstr "Organizacijos"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__participants
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Other Participants"
msgstr "Kiti dalyviai"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_participants
msgid "Participant"
msgstr "Dalyvis"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__participant_ids
msgid "Participants"
msgstr "Dalyviai"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review_reviewer__status__pending
msgid "Pending"
msgstr "Laukiama"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.cep_ef_phase_action
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__phase_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__phase_id
#: model:ir.ui.menu,name:cep_ef.cep_ef_phase
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_phase_form
msgid "Phase"
msgstr "Etapas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__description
msgid "Phase Description"
msgstr "Etapo aprašymas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__phase_name
msgid "Phase Name"
msgstr "Etapo pavadinimas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__assembly_summary_public
msgid "Public Summary"
msgstr "Vieša santrauka"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__questionnaire_link
msgid "Questionnaire Link"
msgstr "Klausimyno nuoroda"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__recipient_type
msgid "Recipient Type"
msgstr "Gavėjo tipas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__recording_format
msgid "Recording Format"
msgstr "Įrašymo formatas"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Relevant Authorities:"
msgstr "Atsakingos institucijos:"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_responsible_members
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__responsible_member_ids
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Responsible Members"
msgstr "Atsakingi nariai"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_user_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_user_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_user_id
msgid "Responsible User"
msgstr "Atsakingas naudotojas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__review_deadline
msgid "Review Deadline"
msgstr "Peržiūros terminas"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Review Pending Documents"
msgstr "Peržiūros laukiantys dokumentai"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid ""
"Review submit\n"
"                                confirmation"
msgstr ""

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__reviewed_file
msgid "Reviewed File"
msgstr "Peržiūrėta byla"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__doc_reviewer_id
msgid "Reviewer"
msgstr "Recenzentas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__reviewer_comments
msgid "Reviewer Comments"
msgstr "Recenzento pastabos"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__doc_reviewers_ids
msgid "Reviewers"
msgstr "Recenzentai"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__role
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__role
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__role
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__role
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__role
msgid "Role"
msgstr "Vaidmuo"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_survey_send_to_wizard_form
msgid "Send"
msgstr "Siųsti"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_survey_send_to_wizard
msgid "Send Survey To Participants, Experts, Facilitators, Organizations"
msgstr "Siųsti apklausą dalyviams, ekspertams, tarpininkams, organizacijoms"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.action_survey_send_to_wizard
msgid "Send Survey To Recipients"
msgstr "Siųsti apklausą gavėjams"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Send To"
msgstr "Siųsti"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__start_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__start_date
msgid "Start Date"
msgstr "Pradžios data"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__status
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__status
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__status
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__status
msgid "Status"
msgstr "Statusas"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__activity_state
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__activity_state
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid ""
"Submit\n"
"                                                    Review"
msgstr ""
"Pateikti\n"
"                                                    peržiūrą"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Submit Review"
msgstr "Pateikti peržiūrą"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Submit your review"
msgstr "Pateikite apžvalgą"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Submited reviews"
msgstr "Pateiktos apžvalgos"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review_reviewer__status__submitted
msgid "Submitted"
msgstr "Pateikta"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_submitted_reviews_form
msgid "Submitted Reviews"
msgstr "Pateiktos apžvalgos"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__survey_id
msgid "Survey"
msgstr "Apklausa"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__survey_link
msgid "Survey Link"
msgstr "Apklausos nuoroda"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__survey_name
msgid "Survey Name"
msgstr "Apklausos pavadinimas"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_surveys
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__survey_ids
msgid "Surveys"
msgstr "Apklausos"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Surveys/Interviews"
msgstr "Apklausos / interviu"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__task
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__task
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__task
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__task
msgid "Task"
msgstr "Užduotis"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Title"
msgstr "Pavadinimas"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__activity_exception_decoration
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__activity_exception_decoration
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Įrašytos išimties veiklos tipas."

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__document_file
msgid "Upload Document"
msgstr "Įkelti dokumentą"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Upload Reviewed File"
msgstr "Įkelti peržiūrėtą failą"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__website
msgid "Website"
msgstr "Interneto svetainė"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Yes"
msgstr ""

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_experts
msgid "experts"
msgstr "Ekspertai"
