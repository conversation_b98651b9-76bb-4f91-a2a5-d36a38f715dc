# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_ef
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20241017\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-10 14:55+0000\n"
"PO-Revision-Date: 2024-12-10 14:55+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Action"
msgstr "Aktion"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_needaction
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_needaction
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_needaction
msgid "Action Needed"
msgstr "Handlungsbedarf"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_activities
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__activity_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_ids
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Activities"
msgstr "Aktivitäten"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__activity_id
#: model:ir.ui.menu,name:cep_ef.cep_ef_activity
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Activity"
msgstr "Aktivität"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_exception_decoration
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_exception_decoration
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitäts-Ausnahmedekoration"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_name
msgid "Activity Name"
msgstr "Aktivitätsname"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_state
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_state
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_state
msgid "Activity State"
msgstr "Aktivitätsstatus"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_type_icon
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_type_icon
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivitätstyp-Symbol"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__address
msgid "Address"
msgstr "Adresse"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__all
msgid "All"
msgstr "Alle"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Are you sure you want to submit your review ?"
msgstr "Sind Sie sicher, dass Sie Ihre Bewertung abgeben möchten?"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.cep_ef_assembly_info_action
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__assembly_info_id
#: model:ir.ui.menu,name:cep_ef.cep_ef_assembly_info
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Assembly Info"
msgstr "Montageinformationen"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_calendar
msgid "Assembly Info Calendar"
msgstr "Infokalender zur Versammlung"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__assembly_title
msgid "Assembly Title"
msgstr "Titel der Versammlung"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_attachment_count
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_attachment_count
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl der Anhänge"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__background
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__background
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__background
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__background
msgid "Background"
msgstr "Hintergrund"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__budget
msgid "Budget"
msgstr "Budget"

#. module: cep_ef
#: model:ir.ui.menu,name:cep_ef.cep_ef_root
msgid "CA Evaluation and Monitoring"
msgstr "CA-Bewertung und -Überwachung"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.cep_ef_activities_action
msgid "CEP EF Activities"
msgstr "CEP EF-Aktivitäten"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activities_search
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_calendar
msgid "CEP EF Activity"
msgstr "CEP EF-Aktivität"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_assembly_info
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_search
msgid "CEP EF Assembly Info"
msgstr "Informationen zur CEP EF-Versammlung"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.cep_ef_criteria_action
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_criteria_search
msgid "CEP EF Criteria"
msgstr "CEP EF-Kriterien"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_organizations
msgid "CEP EF Organizations"
msgstr "CEP EF-Organisationen"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_phases
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_phases_search
msgid "CEP EF Phase"
msgstr "CEP EF-Phase"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_survey_send_to_wizard_form
msgid "Cancel"
msgstr "Stornieren"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Comment"
msgstr "Kommentar"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__comments
msgid "Comments"
msgstr "Kommentare"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__commissioners
msgid "Commissioners"
msgstr "Kommissare"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_activities__status__completed
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review__status__completed
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_surveys__status__completed
msgid "Completed"
msgstr "Vollendet"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__cover_photo
msgid "Cover Photo"
msgstr "Titelbild"

#. module: cep_ef
#: model_terms:ir.actions.act_window,help:cep_ef.cep_ef_assembly_info_action
msgid "Create a new Assembly"
msgstr "Erstellen Sie eine neue Baugruppe"

#. module: cep_ef
#: model_terms:ir.actions.act_window,help:cep_ef.cep_ef_criteria_action
msgid "Create a new Criteria"
msgstr "Erstellen Sie ein neues Kriterium"

#. module: cep_ef
#: model_terms:ir.actions.act_window,help:cep_ef.cep_ef_phase_action
msgid "Create a new Phase"
msgstr "Erstellen Sie eine neue Phase"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__create_date
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_criteria
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__criteria_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__criteria
#: model:ir.ui.menu,name:cep_ef.cep_ef_criteria
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_criteria_form
msgid "Criteria"
msgstr "Kriterien"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__deadline
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Deadline"
msgstr "Frist"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__deliverable_name
msgid "Deliverable Name"
msgstr "Lieferbarer Name"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_deliverables
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__deliverable_ids
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Deliverables"
msgstr "Leistungen"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__description
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__description
msgid "Description"
msgstr "Beschreibung"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__display_name
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_doc_review
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Document Review"
msgstr "Dokumentenprüfung"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_doc_review_reviewer
msgid "Document Review - Reviewer Relation"
msgstr "Beziehung zwischen Dokumentenprüfung und Prüfer"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_doc_reviewer
msgid "Document Reviewers"
msgstr "Dokumentprüfer"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__doc_review_ids
msgid "Document Reviews"
msgstr "Dokumentenprüfungen"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__document_source
msgid "Document Source"
msgstr "Dokumentquelle"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__document_title
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__doc_review_id
msgid "Document Title"
msgstr "Dokumenttitel"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__download_url
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__download_url
msgid "Document URL"
msgstr "Dokument-URL"

#. module: cep_ef
#: model:ir.actions.server,name:cep_ef.action_download_reviewed_file
msgid "Download Reviewed File"
msgstr "Laden Sie die überprüfte Datei herunter"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__user_email
msgid "Email"
msgstr "E-Mail"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__end_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__end_date
msgid "End Date"
msgstr "Enddatum"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__assembly_summary_executive
msgid "Executive Summary"
msgstr "Zusammenfassung"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__expert_ids
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__experts
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Experts"
msgstr "Experten"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_facilitators
msgid "Facilitator"
msgstr "Moderator"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__facilitator_ids
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__facilitators
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Facilitators"
msgstr "Moderatoren"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "File"
msgstr "Datei"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__document_filename
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__reviewed_filename
msgid "File Name"
msgstr "Dateiname"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__final_document
msgid "Final Document"
msgstr "Abschlussdokument"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__final_document_filename
msgid "Final Document File Name"
msgstr "Endgültiger Dateiname des Dokuments"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_final_documents_and_comments
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__final_documnet_comment_ids
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Final Documents and Comments"
msgstr "Abschließende Dokumente und Kommentare"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_follower_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_follower_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_follower_ids
msgid "Followers"
msgstr "Anhänger"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_partner_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_partner_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__activity_type_icon
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__activity_type_icon
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Fantastisches Schriftartensymbol, z. B. Fett-Aufgaben"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__has_message
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__has_message
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__has_message
msgid "Has Message"
msgstr "Hat Nachricht"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__id
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__id
msgid "ID"
msgstr ""

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_exception_icon
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_exception_icon
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_exception_icon
msgid "Icon"
msgstr "Symbol"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__activity_exception_icon
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__activity_exception_icon
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Symbol zur Anzeige einer Ausnahmeaktivität."

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__message_needaction
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__message_needaction
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Wenn diese Option aktiviert ist, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__message_has_error
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__message_has_error
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Wenn diese Option aktiviert ist, liegt bei einigen Nachrichten ein Zustellungsfehler vor."

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_activities__status__in_progress
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review__status__in_progress
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_surveys__status__in_progress
msgid "In Progress"
msgstr "Im Gange"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__interview_format
msgid "Interview Format"
msgstr "Interviewformat"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__interview_title
msgid "Interview Title"
msgstr "Interviewtitel"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_interviews
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__interview_ids
msgid "Interviews"
msgstr "Interviews"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_is_follower
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_is_follower
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys____last_update
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__write_date
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__location
msgid "Location"
msgstr "Standort"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_main_attachment_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_main_attachment_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hauptanhang"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_has_error
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_has_error
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_has_error
msgid "Message Delivery error"
msgstr "Fehler bei der Nachrichtenzustellung"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_ids
msgid "Messages"
msgstr "Nachrichten"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__is_milestone
msgid "Milestone"
msgstr "Meilenstein"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__my_activity_date_deadline
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__my_activity_date_deadline
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Meine Aktivitätsfrist"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__responsible_member_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__user_name
msgid "Name"
msgstr "Name"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_date_deadline
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_date_deadline
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Frist für die nächste Aktivität"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_summary
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_summary
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_summary
msgid "Next Activity Summary"
msgstr "Zusammenfassung der nächsten Aktivität"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_type_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_type_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_type_id
msgid "Next Activity Type"
msgstr "Nächster Aktivitätstyp"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "No"
msgstr ""

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_activities__status__not_started
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review__status__not_started
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_surveys__status__not_started
msgid "Not Started"
msgstr "Nicht gestartet"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_needaction_counter
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_needaction_counter
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_has_error_counter
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_has_error_counter
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__message_needaction_counter
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__message_needaction_counter
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Anzahl der Nachrichten, die Maßnahmen erfordern"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__message_has_error_counter
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__message_has_error_counter
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehlern"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid ""
"Once submitted, it cannot\n"
"                                be edited or changed!"
msgstr ""

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__organization_ids
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__organizations
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Organizations"
msgstr "Organisationen"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__participants
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Other Participants"
msgstr "Andere Teilnehmer"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_participants
msgid "Participant"
msgstr "Teilnehmer"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__participant_ids
msgid "Participants"
msgstr "Teilnehmer"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review_reviewer__status__pending
msgid "Pending"
msgstr "Ausstehend"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.cep_ef_phase_action
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__phase_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__phase_id
#: model:ir.ui.menu,name:cep_ef.cep_ef_phase
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_phase_form
msgid "Phase"
msgstr "Phase"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__description
msgid "Phase Description"
msgstr "Phasenbeschreibung"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__phase_name
msgid "Phase Name"
msgstr "Phasenname"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__assembly_summary_public
msgid "Public Summary"
msgstr "Öffentliche Zusammenfassung"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__questionnaire_link
msgid "Questionnaire Link"
msgstr "Link zum Fragebogen"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__recipient_type
msgid "Recipient Type"
msgstr "Empfängertyp"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__recording_format
msgid "Recording Format"
msgstr "Aufnahmeformat"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Relevant Authorities:"
msgstr "Zuständige Behörden:"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_responsible_members
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__responsible_member_ids
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Responsible Members"
msgstr "Verantwortliche Mitglieder"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_user_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_user_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_user_id
msgid "Responsible User"
msgstr "Verantwortlicher Benutzer"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__review_deadline
msgid "Review Deadline"
msgstr "Frist für die Überprüfung"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Review Pending Documents"
msgstr "Überprüfen Sie ausstehende Dokumente"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid ""
"Review submit\n"
"                                confirmation"
msgstr ""

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__reviewed_file
msgid "Reviewed File"
msgstr "Überprüfte Datei"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__doc_reviewer_id
msgid "Reviewer"
msgstr "Rezensent"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__reviewer_comments
msgid "Reviewer Comments"
msgstr "Kommentare des Rezensenten"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__doc_reviewers_ids
msgid "Reviewers"
msgstr "Rezensenten"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__role
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__role
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__role
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__role
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__role
msgid "Role"
msgstr "Rolle"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_survey_send_to_wizard_form
msgid "Send"
msgstr "Schicken"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_survey_send_to_wizard
msgid "Send Survey To Participants, Experts, Facilitators, Organizations"
msgstr "Senden Sie eine Umfrage an Teilnehmer, Experten, Moderatoren und Organisationen"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.action_survey_send_to_wizard
msgid "Send Survey To Recipients"
msgstr "Umfrage an Empfänger senden"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Send To"
msgstr "Senden an"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__start_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__start_date
msgid "Start Date"
msgstr "Startdatum"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__status
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__status
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__status
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__status
msgid "Status"
msgstr "Status"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__activity_state
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__activity_state
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid ""
"Submit\n"
"                                                    Review"
msgstr ""
"Bewertung\n"
"                                                    abgeben"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Submit Review"
msgstr "Bewertung abgeben"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Submit your review"
msgstr "Senden Sie Ihre Bewertung"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Submited reviews"
msgstr "Eingereichte Bewertungen"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review_reviewer__status__submitted
msgid "Submitted"
msgstr "Eingereicht"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_submitted_reviews_form
msgid "Submitted Reviews"
msgstr "Eingereichte Bewertungen"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__survey_id
msgid "Survey"
msgstr "Umfrage"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__survey_link
msgid "Survey Link"
msgstr "Link zur Umfrage"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__survey_name
msgid "Survey Name"
msgstr "Umfragename"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_surveys
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__survey_ids
msgid "Surveys"
msgstr "Umfragen"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Surveys/Interviews"
msgstr "Umfragen/Interviews"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__task
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__task
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__task
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__task
msgid "Task"
msgstr "Aufgabe"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Title"
msgstr "Titel"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__activity_exception_decoration
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__activity_exception_decoration
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ der erfassten Ausnahmeaktivität."

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__document_file
msgid "Upload Document"
msgstr "Dokument hochladen"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Upload Reviewed File"
msgstr "Laden Sie die überprüfte Datei hoch"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__website
msgid "Website"
msgstr "Webseite"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Yes"
msgstr ""

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_experts
msgid "experts"
msgstr "Experten"
