from odoo import http
from odoo.http import request
import json
import requests
import base64

def isAuthenticate():
    return request.env.user != request.website.user_id

def data_exists_in_relation_table(doc_review_id, doc_reviewer_id):
        # Get the database cursor
        cr = request.env.cr
        # Raw SQL query to fetch the pending reviews
        query = """
                SELECT * FROM cep_ef_doc_review_cep_ef_doc_reviewer_rel rel 
                WHERE rel.cep_ef_doc_review_id = %s
                AND rel.cep_ef_doc_reviewer_id = %s
                """
        # Execute the query
        cr.execute(query, (doc_review_id, doc_reviewer_id))

        # Fetch the results
        results = cr.fetchall()
        return bool(results)

class EfController(http.Controller):

    #* Review Pending List page
    @http.route('/review-pending', type='http', auth='public', website=True)
    def list(self, **kwargs):
        # check if user is not logged in redirect to login page
        if not isAuthenticate():
            return request.redirect('/web/login')
        # get review pending doc_review by user email assigned as document reviewer for looged in user
        user_email = request.env.user.email
        # get logged in user's data in doc_reviewers table
        doc_reviewers = request.env['cep.ef.doc.reviewer'].sudo().search([('email', '=', user_email)])
        doc_reviewer_ids = doc_reviewers.ids
        # VALIDATION: check if doc_reviewer_ids is empty
        if not doc_reviewer_ids:
            print('----------------There is no document to review for this user-----------------')
            return request.render('cep_ef.review_pending', {'pending_reviews_data': []})
        # Get the database cursor
        cr = request.env.cr
        # Raw SQL query to fetch the pending reviews
        query = """
                SELECT * FROM cep_ef_doc_review_cep_ef_doc_reviewer_rel rel WHERE rel.cep_ef_doc_reviewer_id in %s
                AND NOT EXISTS (
                    SELECT 1 
                    FROM cep_ef_doc_review_reviewer reviewer 
                    WHERE reviewer.doc_reviewer_id = rel.cep_ef_doc_reviewer_id
                    AND reviewer.doc_review_id = rel.cep_ef_doc_review_id
                )
                """

        # Execute the query
        cr.execute(query, (tuple(doc_reviewer_ids),))

        # Fetch the results
        results = cr.fetchall()

        # Process the results
        pending_reviews = []
        print("-" * 100)  # Separator for clarity
        for row in results:
            # Print all records and their fields 
            print('doc_review_id - ', row[0])
            print('doc_reviewer_id - ', row[1])
            # print('reviewer_comments - ', row[4])
            # print('status - ', row[5])
            print("-" * 100)  # Separator for clarity

            pending_reviews.append({
                'doc_review_id': row[0],
                'doc_reviewer_id': row[1],
            })

        pending_reviews_data = []
        for review in pending_reviews:
            doc_review = request.env['cep.ef.doc_review'].sudo().browse(review['doc_review_id'])
            pending_reviews_data.append({
                'doc_review': doc_review,
                'reviewer_id': review['doc_reviewer_id'],
            })
        
        
        context = {'pending_reviews_data': pending_reviews_data}
        
        return request.render('cep_ef.review_pending',context)
    
    @http.route('/review-pending/count', type='http', auth='public', methods=['GET'], website=True)
    def get_pending_review_count(self):
       
     

        if not isAuthenticate():
            return http.Response(json.dumps({'review_count': 0}), headers={'Content-Type': 'application/json'}) 

        user_email = request.env.user.email

        doc_reviewers = request.env['cep.ef.doc.reviewer'].sudo().search([('email', '=', user_email)])
        
        doc_reviewer_ids = doc_reviewers.ids
       
      
        if not doc_reviewer_ids:
            return http.Response(json.dumps({'review_count': 0}), headers={'Content-Type': 'application/json'}) 
        
       
        cr = request.env.cr
        query = """
                SELECT COUNT(*) FROM cep_ef_doc_review_cep_ef_doc_reviewer_rel rel 
                WHERE rel.cep_ef_doc_reviewer_id in %s
                AND NOT EXISTS (
                    SELECT 1 
                    FROM cep_ef_doc_review_reviewer reviewer 
                    WHERE reviewer.doc_reviewer_id = rel.cep_ef_doc_reviewer_id
                    AND reviewer.doc_review_id = rel.cep_ef_doc_review_id
                )
                """

        cr.execute(query, (tuple(doc_reviewer_ids),))
        pending_reviews_count = cr.fetchone()[0]

        return http.Response(json.dumps( {'review_count': pending_reviews_count}), headers={'Content-Type': 'application/json'}) 

    #* Submit review view page
    @http.route('/submit-review/<int:doc_review_id>/<int:doc_reviewer_id>/view', type='http', auth='public', website=True)
    def submit_review(self, doc_review_id, doc_reviewer_id, **kwargs):
        # VALIDATION: check if user is not logged in redirect to login page
        if not isAuthenticate():
            return request.redirect('/web/login')
        
        # VALIDATION: Check is the reviewer id is same as the logged in user id
        doc_reviewer_info = request.env['cep.ef.doc.reviewer'].sudo().search([('id', '=', int(doc_reviewer_id))])
        if doc_reviewer_info.email != request.env.user.email:
            print('-----------------User is not authorized to access this review-----------------')
            return request.redirect('/review-pending')
        
        # VALIDATION: Check if the doc_review_id and doc_reviewer_id exists in the relation table
        if not data_exists_in_relation_table(doc_review_id, doc_reviewer_id):
            print('-----------------Document Review not found-----------------')
            return request.redirect('/review-pending')
        
        context = {
            'doc_review_id': doc_review_id,
            'doc_reviewer_id': doc_reviewer_id
        }
        return request.render('cep_ef.submit_review', context)
    
    #* Submit review form action
    @http.route('/submit-review/<int:doc_review_id>/<int:doc_reviewer_id>/submit', type='http', auth='public', website=True)
    def submit_review_action(self, doc_review_id, doc_reviewer_id, **kwargs):
        # VALIDATION: check if user is not logged in redirect to login page
        if not isAuthenticate():
            return request.redirect('/web/login')
        
        print('-'*100)
        print('doc_review_id - ', doc_review_id)
        print('doc_reviewer_id - ', doc_reviewer_id)
        # VALIDATION: Check is the reviewer id is same as the logged in user id
        doc_reviewer_info = request.env['cep.ef.doc.reviewer'].sudo().search([('id', '=', int(doc_reviewer_id))])
        if doc_reviewer_info.email != request.env.user.email:
            print('-----------------User is not authorized to submit the review-----------------')
            return request.redirect('/review-pending')
        
        # VALIDATION: Check if the doc_review_id and doc_reviewer_id exists in the relation table
        if not data_exists_in_relation_table(doc_review_id, doc_reviewer_id):
            print('-----------------Document Review not found-----------------')
            return request.redirect('/review-pending')


        # VALIDATION: Check if the reviewer has already submitted the review
        results = request.env['cep.ef.doc_review_reviewer'].sudo().search([
            ('doc_review_id', '=', doc_review_id),
            ('doc_reviewer_id', '=', doc_reviewer_id)
        ])

        # check if result is not empty
        if results:
            print('----------------Reviewer already submitted the review-----------------')
            return request.redirect('/review-pending')

        # process the form data
        kwargs['reviewed_file'] = base64.b64encode(kwargs['reviewed_file'].read())
        kwargs['status'] = 'submitted'

        # create record in doc_review_reviewer table
        request.env['cep.ef.doc_review_reviewer'].sudo().create({
            'doc_review_id': doc_review_id,
            'doc_reviewer_id': doc_reviewer_id,
            **kwargs
        })
        return request.redirect('/review-pending')