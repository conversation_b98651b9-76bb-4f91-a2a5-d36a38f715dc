from odoo import models, fields, api
from odoo.exceptions import UserError
from datetime import datetime, timedelta

class Activity(models.Model):
    _name = 'cep.ccf.activity'
    _description = 'CEP CCF Activity'
    _rec_name = 'activity_name'

    activity_name = fields.Char('Activity Name', required=True)
    description = fields.Text('Activity Description', required=True)
    start_date = fields.Date(string='Start Date', default=lambda self: (datetime.now()).strftime('%Y-%m-%d'), required=True)
    end_date = fields.Date(string='End Date', default=lambda self: (datetime.now()).strftime('%Y-%m-%d'), required=True)
    phase_id = fields.Many2one('cep.ccf.phase', required=True)
    status = fields.Selection([
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
    ], string='Status', default='not_started', required=True)
    is_milestone = fields.Boolean('Milestone')

    # use phase_name to show selected text in the activity form view instead of selected value i.e. "Know" instead of "phase1"
    phase_name = fields.Selection(related='phase_id.phase_name', string='Phase Name', readonly=True)
    deliverable_ids = fields.One2many('cep.ccf.deliverable', 'activity_id', string='Deliverables')
    assign_ids = fields.Many2many('res.users', string='Assign', index=True, tracking=True, required=True)
    activity_steps = fields.Selection([
        ('step1', 'Implementation'),
        ('step2', 'Need Analysis'),
    ], string='Activity Step', required=True)
    survey_ids = fields.One2many('cep.ccf.survey', 'activity_id', string='Surveys')
    focus_group_ids = fields.One2many('cep.ccf.focus_group', 'activity_id', string='Focus Groups')
    activity_data_ids = fields.One2many('cep.ccf.activity_data', 'activity_id', string='Activity Data')
    project_id = fields.Many2one(
        related='phase_id.project_id', 
        string='Project', 
        store=True, 
        readonly=True
    )