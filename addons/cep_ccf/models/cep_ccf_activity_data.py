from odoo import models, fields, api

class ActivityData(models.Model):
    _name = 'cep.ccf.activity_data'
    _description = 'CEP CCF Activity Data'
    _rec_name = 'criteria'

    criteria = fields.Char('Indicator/Criteria', required=True)
    data = fields.Binary('Data', required=True)
    data_source = fields.Char('Data Source', required=True)
    data_overview = fields.Text('Data Overview', required=True)
    data_availability = fields.Char('Data Availability', required=True)
    region = fields.Char('Region', required=True)
    activity_id = fields.Many2one('cep.ccf.activity', required=True)
    