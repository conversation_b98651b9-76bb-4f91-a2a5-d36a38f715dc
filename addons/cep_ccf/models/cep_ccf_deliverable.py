from odoo import models, fields, api
from odoo.exceptions import UserError
from datetime import datetime, timedelta


class Deliverable(models.Model):
    _name = 'cep.ccf.deliverable'
    _description = 'CEP CCF Deliverable'
    _rec_name = 'deliverable_name'

    deliverable_name = fields.Char('Deliverable Name', required=True)
    deliverable_description = fields.Text('Deliverable Description', required=True)
    end_date = fields.Date(string='End Date', default=lambda self: (datetime.now()).strftime('%Y-%m-%d'), required=True)
    activity_id = fields.Many2one('cep.ccf.activity', required=True)    