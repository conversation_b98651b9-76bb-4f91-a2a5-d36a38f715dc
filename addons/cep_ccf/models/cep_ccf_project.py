from odoo import fields, models, api
from odoo.exceptions import ValidationError

from datetime import datetime, timedelta

class Project(models.Model):
    _name = "cep.ccf.project"
    _description = "CEP CCF Project"
    _rec_name = 'title'

    title = fields.Char('Title', required=True)
    description_public = fields.Html('Public Description', required=True)
    description_private = fields.Html('Private Description')
    cover_photo = fields.Image("Cover Photo")
    location = fields.Char(string='Location', required=True)
    start_date = fields.Date(string='Start Date', default=lambda self: (datetime.now()).strftime('%Y-%m-%d'), required=True)
    end_date = fields.Date(string='End Date', default=lambda self: (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d'), required=True)
    phase_ids = fields.One2many('cep.ccf.phase', 'project_id', string='Phases')

    def action_open_project_phase_form_view(self):
        # Assuming there's a one-to-many field named 'phase_ids' on 'cep.ccf.project'
        phase = self.env['cep.ccf.phase'].new({
            #'project_id': self.id,
            # Add other required fields for your phase
        })

        view_id = self.env.ref('cep_ccf.cep_ccf_project_phase_form').id
        return {
            'name': 'Phases',
            'view_mode': 'form',
            'view_id': view_id,
            'res_model': 'cep.ccf.phase',
            'type': 'ir.actions.act_window',
            #'res_id': phase.id,
            'target': 'current',
            'context': {
                'default_project_id': self.id,  # Set the default value for project_id
            },
        }