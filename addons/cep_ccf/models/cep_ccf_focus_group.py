from odoo import models, fields, api

class FocusGroup(models.Model):
    _name = 'cep.ccf.focus_group'
    _description = 'CEP CCF Focus Group'
    _rec_name = 'focus_group_name'

    focus_group_name = fields.Char('Focus Group Name', required=True)
    participant_info = fields.Text('Participant Information', required=True)
    recording_format = fields.Char('Recording Format', required=True)
    recording_time = fields.Char('Recording Time', required=True)
    transcription_notes = fields.Text('Transcription Notes', required=True)

    activity_id = fields.Many2one('cep.ccf.activity', required=True)