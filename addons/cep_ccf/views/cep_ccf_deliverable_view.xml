<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_ccf_deliverable_search" model="ir.ui.view">
        <field name="name">cep.ccf.deliverable.search</field>
        <field name="model">cep.ccf.deliverable</field>
        <field name="arch" type="xml">
            <search string="Deliverables">
                <field name="deliverable_name"/>
                <field name="deliverable_description"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_ccf_deliverable_tree" model="ir.ui.view">
        <field name="name">cep.ccf.deliverable.tree</field>
        <field name="model">cep.ccf.deliverable</field>
        <field name="arch" type="xml">
            <tree>
                <field name="deliverable_name"/>
                <field name="deliverable_description"/>
                <field name="end_date"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_ccf_deliverable_form" model="ir.ui.view">
        <field name="name">cep.ccf.deliverable.form</field>
        <field name="model">cep.ccf.deliverable</field>
        <field name="arch" type="xml">
            <form string="CCF Deliverable">
                <sheet>
                     <group>
                        <field name="deliverable_name"/>
                        <field name="deliverable_description"/>
                        <field name="end_date"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_cep_ccf_deliverable" model="ir.actions.act_window">
        <field name="name">CCF Deliverable</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.ccf.deliverable</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new deliverable
            </p>
        </field>
    </record>
</odoo>