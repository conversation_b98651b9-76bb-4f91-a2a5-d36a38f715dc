# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_proposal
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20241017\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-10 18:55+0000\n"
"PO-Revision-Date: 2024-12-10 18:55+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "&amp;times;"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid ""
"<i class=\"fa fa-check\"/>\n"
"                                                    <span>Vote</span>"
msgstr ""
"<i class=\"fa fa-check\"/>\n"
"                                                    <span>Voto</span>"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Descargar"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "<span class=\"close-btn\">&amp;times;</span>"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid ""
"<span id=\"description-char-numbers\">00</span>\n"
"                                                /600"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "<span id=\"description-char-numbers\">00</span>/600"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "<span>Cancel Vote</span>"
msgstr "<span>Cancelar voto</span>"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Add an Official Update"
msgstr "Agregar una actualización oficial"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "All"
msgstr "Todo"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__all_tags
msgid "All Tags"
msgstr "Todas las etiquetas"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__attachment_ids
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Attachments"
msgstr "Adjuntos"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Attachments<span class=\"fw-400 opacity-80\"> (optional)</span>"
msgstr "Archivos adjuntos <span class=\"fw-400 opacity-80\"> (opcional)</span>"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.custom_snippet
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_search
msgid "CEP Proposal"
msgstr "Actualización oficial de la propuesta del CEP"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_comment
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_comment_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_comment_search
msgid "CEP Proposal Comment"
msgstr "Actualización oficial de la propuesta del CEP"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_official_update
msgid "CEP Proposal Official Update"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_official_update_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_official_update_search
msgid "CEP Proposal Official update"
msgstr "Reacción a la propuesta del CEP"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_reaction
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_reaction_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_reaction_search
msgid "CEP Proposal Reaction"
msgstr ""

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_tag
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_tag_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_tag_search
msgid "CEP Proposal Tag"
msgstr "Etiqueta de propuesta del CEP"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_vote
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_vote_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_vote_search
msgid "CEP Proposal Vote"
msgstr "Votación de la propuesta del CEP"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Click to select a file"
msgstr "Haga clic para seleccionar un archivo"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__comment_id
msgid "Comment"
msgstr "Comentario"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_comment_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__comment_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_comment
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Comments"
msgstr "Comentarios"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Comments ("
msgstr "Comentarios ("

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__cover_photo
msgid "Cover Photo"
msgstr "Foto de portada"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Cover Photo<span class=\"fw-400 opacity-80\"> (optional)</span>"
msgstr "Foto de portada<span class=\"fw-400 opacity-80\"> (opcional)</span>"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__cover_properties
msgid "Cover Properties"
msgstr "Propiedades de la cubierta"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__create_date
msgid "Create Date"
msgstr "Crear fecha"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_comment_action
msgid "Create a new comment"
msgstr "Crear un nuevo comentario"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_official_update_action
msgid "Create a new official update"
msgstr "Crear una nueva actualización oficial"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_proposal_action
msgid "Create a new proposal"
msgstr "Crear una nueva propuesta"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_reaction_action
msgid "Create a new reaction"
msgstr "Crea una nueva reacción"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_tag_action
msgid "Create a new tag"
msgstr "Crear una nueva etiqueta"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_vote_action
msgid "Create a new vote"
msgstr "Crear un nuevo voto"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__create_date
msgid "Created on"
msgstr "Creado el"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__description
msgid "Description"
msgstr "Descripción"

#. module: cep_proposal
#: model:ir.model.fields.selection,name:cep_proposal.selection__cep_proposal_reaction__type__dislike
msgid "Dislike"
msgstr "No me gusta"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Dislike("
msgstr "No me gusta("

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__display_name
msgid "Display Name"
msgstr "Nombre para mostrar"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Download Attachments"
msgstr "Descargar archivos adjuntos"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "Download existing attachment"
msgstr "Descargar archivo adjunto existente"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__end_date
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "End Date"
msgstr "Fecha de finalización"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Go Back"
msgstr "Volver"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__id
msgid "ID"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Image and Attachment"
msgstr "Imagen y archivo adjunto"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__write_uid
msgid "Last Updated by"
msgstr "Actualizado por última vez por"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: cep_proposal
#: model:ir.model.fields.selection,name:cep_proposal.selection__cep_proposal_reaction__type__like
msgid "Like"
msgstr "Como"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Like("
msgstr "Como("

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__location
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Location"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Location, End Date and Minimum Vote"
msgstr "Ubicación, fecha de finalización y voto mínimo"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid ""
"Make your proposal stand out. This image will be shown\n"
"                                                    at the top of the content."
msgstr ""
"Haz que tu propuesta destaque.\n"
"                                           Esta imagen se mostrará en la parte superior del contenido."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid ""
"Make your proposal stand out. This image will be shown at the top of the "
"content."
msgstr ""
"Haz que tu propuesta destaque. Esta imagen se mostrará en la parte superior del  "
"contenido."

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__message
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__message
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Message"
msgstr "Mensaje"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__min_votes
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Minimum Vote"
msgstr "Voto Mínimo"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Newest"
msgstr "El más nuevo"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Official Update"
msgstr "Actualización oficial"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__official_update_ids
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Official Updates"
msgstr "Actualizaciones oficiales"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_official_update_action
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_official_update
msgid "Official updates"
msgstr "Actualizaciones oficiales"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Oldest"
msgstr "más antiguo"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Other Info"
msgstr "Otra información"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__owner_id
msgid "Owner"
msgstr "Dueño"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__parent_id
msgid "Parent Comment"
msgstr "Comentario de los padres"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Post"
msgstr "Correo"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Post your Proposal"
msgstr "Publica tu propuesta"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Posted on -"
msgstr "Publicado el -"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__proposal_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__proposal_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__proposal_id
msgid "Proposal"
msgstr "Propuesta"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Proposal Description"
msgstr "Descripción de la propuesta"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Proposal Details"
msgstr "Detalles de la propuesta"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_list
msgid "Proposal List"
msgstr "Lista de propuestas"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Proposal Submit"
msgstr "Enviar propuesta"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_proposal_action
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_proposal
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_root
msgid "Proposals"
msgstr "Propuestas"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposal_list
msgid "Proposals (<span class=\"proposal-count\">0</span>)"
msgstr "Propuestas (<span class=\"proposal-count\">0</span>)"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Proposed by"
msgstr "Propuesto por"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Publish"
msgstr "Publicar"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Publish your Proposal"
msgstr "Publica tu propuesta"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_reaction_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__reaction_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_reaction
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_comment_form
msgid "Reactions"
msgstr "Reacciones"

#. module: cep_proposal
#: model:ir.model.fields,help:cep_proposal.field_cep_proposal_proposal__website_id
msgid "Restrict publishing to this website."
msgstr "Restringir la publicación a este sitio web."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposal_list
msgid "Search ..."
msgstr "Buscar ..."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Select an image (max. 10MB)"
msgstr "Seleccione una imagen (máx. 10 MB)"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Share"
msgstr "Compartir"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Status"
msgstr "Estado"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__name
msgid "Tag"
msgstr "etiqueta"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_tag_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__tag_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_tag
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Tags"
msgstr "etiquetas"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Tags <span class=\"fw-400\">(optional)</span>"
msgstr "etiquetas <span class=\"fw-400\">(opcional)</span>"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "This proposal expired as it didn't reach minimum votes in time."
msgstr "Esta propuesta expiró porque no alcanzó el mínimo de votos a tiempo."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "This proposal is open for vote."
msgstr "Esta propuesta está abierta a votación."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "This proposal passed as it reached minimum votes in time."
msgstr "Esta propuesta fue aprobada porque alcanzó el mínimo de votos a tiempo."

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__title
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Title"
msgstr "Título"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__type
msgid "Type"
msgstr "Tipo"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Type something and press enter"
msgstr "Escribe algo y presiona enter"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "Update Proposal"
msgstr "Propuesta de actualización"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid ""
"Upload files to give others more information and\n"
"                                                    context"
msgstr ""
"Cargue archivos para brindar a otros más información y \n"
"                                                    contexto."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Upload files to give others more information and context"
msgstr "Cargue archivos para brindar a otros más información y contexto."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Use Tags"
msgstr "Usar etiquetas"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_vote_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__vote_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_vote
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Votes"
msgstr "Votos"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__website_id
msgid "Website"
msgstr "Sitio web"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "What is your Proposal?"
msgstr "¿Cuál es tu propuesta?"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Write your comment here ..."
msgstr "Escribe tu comentario aquí..."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Write your reply here ..."
msgstr "Escribe tu respuesta aquí..."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "proposal cover"
msgstr "portada de propuesta"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.custom_snippet
msgid "proposal, List"
msgstr "propuesta, lista"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "votes"
msgstr "Votos"
