# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_proposal
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20241017\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-11 18:38+0000\n"
"PO-Revision-Date: 2024-12-11 18:38+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "&amp;times;"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid ""
"<i class=\"fa fa-check\"/>\n"
"                                                    <span>Vote</span>"
msgstr ""
"<i class=\"fa fa-check\"/>\n"
"                                                    <span>ψήφος</span>"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Λήψη"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "<span class=\"close-btn\">&amp;times;</span>"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid ""
"<span id=\"description-char-numbers\">00</span>\n"
"                                                /600"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "<span id=\"description-char-numbers\">00</span>/600"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "<span>Cancel Vote</span>"
msgstr "<span>Ακύρωση ψήφου</span>"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Add an Official Update"
msgstr "Προσθέστε μία επίσημη ενημέρωση"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "All"
msgstr "Όλα"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__all_tags
msgid "All Tags"
msgstr "Όλες οι ετικέτες"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__attachment_ids
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Attachments"
msgstr "Συνημμένα"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Attachments<span class=\"fw-400 opacity-80\"> (optional)</span>"
msgstr "Συνημμένα <span class=\"fw-400 opacity-80\"> (προαιρετικά)</span>"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.custom_snippet
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_search
msgid "CEP Proposal"
msgstr "Πρόταση CEP"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_comment
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_comment_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_comment_search
msgid "CEP Proposal Comment"
msgstr "Σχόλιο πρότασης CEP"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_official_update
msgid "CEP Proposal Official Update"
msgstr "Επίσημη ενημέρωση πρότασης CEP"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_official_update_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_official_update_search
msgid "CEP Proposal Official update"
msgstr "Επίσημη ενημέρωση της πρότασης CEP"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_reaction
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_reaction_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_reaction_search
msgid "CEP Proposal Reaction"
msgstr "Αντίδραση πρότασης CEP"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_tag
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_tag_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_tag_search
msgid "CEP Proposal Tag"
msgstr "Ετικέτα πρότασης CEP"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_vote
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_vote_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_vote_search
msgid "CEP Proposal Vote"
msgstr "Ψηφοφορία πρότασης CEP"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Click to select a file"
msgstr "Κάντε κλικ για να επιλέξετε ένα αρχείο"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__comment_id
msgid "Comment"
msgstr "Σχόλιο"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_comment_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__comment_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_comment
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Comments"
msgstr "Σχόλια"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Comments ("
msgstr "Σχόλια ("

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__cover_photo
msgid "Cover Photo"
msgstr "Φωτογραφία εξωφύλλου"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Cover Photo<span class=\"fw-400 opacity-80\"> (optional)</span>"
msgstr "Φωτογραφία εξωφύλλου<span class=\"fw-400 opacity-80\"> (προαιρετικά)</span>"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__cover_properties
msgid "Cover Properties"
msgstr "Ιδιότητες εξωφύλλου"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__create_date
msgid "Create Date"
msgstr "Δημιουργία ημερομηνίας"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_comment_action
msgid "Create a new comment"
msgstr "Δημιουργία νέου σχολίου"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_official_update_action
msgid "Create a new official update"
msgstr "Δημιουργία νέας επίσημης ενημέρωσης"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_proposal_action
msgid "Create a new proposal"
msgstr "Δημιουργία νέας πρότασης"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_reaction_action
msgid "Create a new reaction"
msgstr "Δημιουργία νέας αντίδρασης"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_tag_action
msgid "Create a new tag"
msgstr "Δημιουργία νέας ετικέτας"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_vote_action
msgid "Create a new vote"
msgstr "Δημιουργία νέας ψήφου"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε την"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__description
msgid "Description"
msgstr "Περιγραφή"

#. module: cep_proposal
#: model:ir.model.fields.selection,name:cep_proposal.selection__cep_proposal_reaction__type__dislike
msgid "Dislike"
msgstr "Δε μου αρέσει"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Dislike("
msgstr "Δε μου αρέσει("

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__display_name
msgid "Display Name"
msgstr "Εμφανιζόμενο όνομα"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Download Attachments"
msgstr "Λήψη συνημμένων"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "Download existing attachment"
msgstr "Λήψη υπάρχοντος συνημμένου"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__end_date
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "End Date"
msgstr "Ημερομηνία λήξης"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Go Back"
msgstr "Επιστροφή"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__id
msgid "ID"
msgstr "Ταυτότητα"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Image and Attachment"
msgstr "Εικόνα και συνημμένο"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__write_uid
msgid "Last Updated by"
msgstr "Τελευταία ενημέρωση από"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__write_date
msgid "Last Updated on"
msgstr "Τελευταία ενημέρωση στις"

#. module: cep_proposal
#: model:ir.model.fields.selection,name:cep_proposal.selection__cep_proposal_reaction__type__like
msgid "Like"
msgstr "Μου αρέσει"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Like("
msgstr "Μου αρέσει("

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__location
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Location"
msgstr "Τοποθεσία"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Location, End Date and Minimum Vote"
msgstr "Τοποθεσία, Ημερομηνία λήξης και Ελάχιστη ψήφος"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid ""
"Make your proposal stand out. This image will be shown\n"
"                                                    at the top of the content."
msgstr ""
"Κάντε την πρότασή σας να ξεχωρίζει. Αυτή η εικόνα θα\n"
"                                                   εμφανίζεται στην κορυφή του περιεχομένου."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid ""
"Make your proposal stand out. This image will be shown at the top of the "
"content."
msgstr ""
"Κάντε την πρότασή σας να ξεχωρίζει. Αυτή η εικόνα θα εμφανίζεται στην κορυφή του "
"περιεχομένου"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__message
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__message
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Message"
msgstr "Μήνυμα"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__min_votes
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Minimum Vote"
msgstr "Ελάχιστη ψήφος"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Newest"
msgstr "Νεότερο"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Official Update"
msgstr "Επίσημη ενημέρωση"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__official_update_ids
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Official Updates"
msgstr "Επίσημες ενημερώσεις"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_official_update_action
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_official_update
msgid "Official updates"
msgstr "Επίσημες ενημερώσεις"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Oldest"
msgstr "Το πιο παλιό"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Other Info"
msgstr "Άλλες πληροφορίες"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__owner_id
msgid "Owner"
msgstr "Ιδιοκτήτης"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__parent_id
msgid "Parent Comment"
msgstr "Γονικό σχόλιο"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Post"
msgstr "Δημοσίευση"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Post your Proposal"
msgstr "Δημοσιεύστε την πρότασή σας"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Posted on -"
msgstr "Δημοσιεύτηκε στο -"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__proposal_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__proposal_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__proposal_id
msgid "Proposal"
msgstr "Πρόταση"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Proposal Description"
msgstr "Περιγραφή πρότασης"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Proposal Details"
msgstr "Λεπτομέρειες πρότασης"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_list
msgid "Proposal List"
msgstr "Λίστα προτάσεων"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Proposal Submit"
msgstr "Υποβολή πρότασης"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_proposal_action
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_proposal
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_root
msgid "Proposals"
msgstr "Προτάσεις"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposal_list
msgid "Proposals (<span class=\"proposal-count\">0</span>)"
msgstr "Προτάσεις (<span class=\"proposal-count\">0</span>)"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Proposed by"
msgstr "Προτείνεται από"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Publish"
msgstr "Δημοσίευση"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Publish your Proposal"
msgstr "Δημοσιεύστε την πρότασή σας"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_reaction_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__reaction_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_reaction
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_comment_form
msgid "Reactions"
msgstr "Αντιδράσεις"

#. module: cep_proposal
#: model:ir.model.fields,help:cep_proposal.field_cep_proposal_proposal__website_id
msgid "Restrict publishing to this website."
msgstr "Περιορίστε τη δημοσίευση σε αυτόν τον ιστότοπο."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposal_list
msgid "Search ..."
msgstr "Αναζήτηση ...."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Select an image (max. 10MB)"
msgstr "Επιλέξτε μια εικόνα (max 10MB)"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Share"
msgstr "Διαμοιρασμός"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Status"
msgstr "Κατάσταση"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__name
msgid "Tag"
msgstr "ετικέτα"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_tag_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__tag_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_tag
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Tags"
msgstr "ετικέτες"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Tags <span class=\"fw-400\">(optional)</span>"
msgstr "ετικέτες <span class=\"fw-400\">(προαιρετικά)</span>"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "This proposal expired as it didn't reach minimum votes in time."
msgstr "Αυτή η πρόταση έληξε επειδή δεν συγκέντρωσε εγκαίρως τις ελάχιστες ψήφους."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "This proposal is open for vote."
msgstr "Αυτή η πρόταση είναι ανοικτή για ψηφοφορία."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "This proposal passed as it reached minimum votes in time."
msgstr "Αυτή η πρόταση πέρασε διότι πήρε εγκαίρως τις ελάχιστες ψήφους."

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__title
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Title"
msgstr "Τίτλος"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__type
msgid "Type"
msgstr "Τύπος"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Type something and press enter"
msgstr "Πληκτρολογήστε κάτι και πατήστε enter"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "Update Proposal"
msgstr "Ενημέρωση πρότασης"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid ""
"Upload files to give others more information and\n"
"                                                    context"
msgstr ""
"Ανεβάστε αρχεία για να δώσετε στους άλλους περισσότερες πληροφορίες και\n"
"                                                       περιεχόμενο"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Upload files to give others more information and context"
msgstr "Ανεβάστε αρχεία για να δώσετε στους άλλους περισσότερες πληροφορίες και περιεχόμενο"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Use Tags"
msgstr "Χρήση ετικετών"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_vote_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__vote_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_vote
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Votes"
msgstr "ψήφους"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__website_id
msgid "Website"
msgstr "Ιστότοπος"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "What is your Proposal?"
msgstr "Ποια είναι η Πρότασή σας;"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Write your comment here ..."
msgstr "Γράψτε το σχόλιό σας εδώ..."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Write your reply here ..."
msgstr "Γράψτε την απάντησή σας εδώ..."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "proposal cover"
msgstr "κάλυψη πρότασης"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.custom_snippet
msgid "proposal, List"
msgstr "πρόταση, Λίστα"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "votes"
msgstr "ψήφους"
