# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_proposal
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20241017\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-11 13:13+0000\n"
"PO-Revision-Date: 2024-12-11 13:13+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "&amp;times;"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid ""
"<i class=\"fa fa-check\"/>\n"
"                                                    <span>Vote</span>"
msgstr ""
"<i class=\"fa fa-check\"/>\n"
"                                                    <span>Voto</span>"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Scarica"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "<span class=\"close-btn\">&amp;times;</span>"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid ""
"<span id=\"description-char-numbers\">00</span>\n"
"                                                /600"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "<span id=\"description-char-numbers\">00</span>/600"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "<span>Cancel Vote</span>"
msgstr "<span>Annulla voto</span>"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Add an Official Update"
msgstr "Aggiungi un aggiornamento ufficiale"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "All"
msgstr "Tutti"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__all_tags
msgid "All Tags"
msgstr "Tutti i tag"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__attachment_ids
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Attachments"
msgstr "Allegati"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Attachments<span class=\"fw-400 opacity-80\"> (optional)</span>"
msgstr "Allegati<span class=\"fw-400 opacity-80\"> (facoltativi)</span>"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.custom_snippet
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_search
msgid "CEP Proposal"
msgstr "Proposta CEP"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_comment
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_comment_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_comment_search
msgid "CEP Proposal Comment"
msgstr "Commento alla proposta CEP"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_official_update
msgid "CEP Proposal Official Update"
msgstr "Aggiornamento ufficiale della proposta CEP"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_official_update_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_official_update_search
msgid "CEP Proposal Official update"
msgstr "Proposta CEP Aggiornamento ufficiale"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_reaction
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_reaction_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_reaction_search
msgid "CEP Proposal Reaction"
msgstr "Reazione alla proposta CEP"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_tag
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_tag_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_tag_search
msgid "CEP Proposal Tag"
msgstr "Etichetta della proposta CEP"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_vote
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_vote_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_vote_search
msgid "CEP Proposal Vote"
msgstr "Votazione della proposta CEP"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Click to select a file"
msgstr "Clicca per selezionare un file"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__comment_id
msgid "Comment"
msgstr "Commenta"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_comment_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__comment_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_comment
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Comments"
msgstr "Commenti"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Comments ("
msgstr "Commenti ("

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__cover_photo
msgid "Cover Photo"
msgstr "Foto di copertina"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Cover Photo<span class=\"fw-400 opacity-80\"> (optional)</span>"
msgstr "Foto di copertina<span class=\"fw-400 opacity-80\"> (facoltativi)</span>"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__cover_properties
msgid "Cover Properties"
msgstr "Proprietà della copertina"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__create_date
msgid "Create Date"
msgstr "Data di creazione"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_comment_action
msgid "Create a new comment"
msgstr "Crea un nuovo commento"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_official_update_action
msgid "Create a new official update"
msgstr "Crea un nuovo aggiornamento ufficiale"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_proposal_action
msgid "Create a new proposal"
msgstr "Crea una nuova proposta"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_reaction_action
msgid "Create a new reaction"
msgstr "Crea una nuova reazione"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_tag_action
msgid "Create a new tag"
msgstr "Crea un nuovo tag"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_vote_action
msgid "Create a new vote"
msgstr "Crea un nuovo voto"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__create_date
msgid "Created on"
msgstr "Creato su"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__description
msgid "Description"
msgstr "Descrizione"

#. module: cep_proposal
#: model:ir.model.fields.selection,name:cep_proposal.selection__cep_proposal_reaction__type__dislike
msgid "Dislike"
msgstr "Non mi piace"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Dislike("
msgstr "Non mi piace("

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__display_name
msgid "Display Name"
msgstr "Visualizza nome"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Download Attachments"
msgstr "Scarica gli allegati"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "Download existing attachment"
msgstr "Scarica l'allegato esistente"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__end_date
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "End Date"
msgstr "Data di fine"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Go Back"
msgstr "Torna indietro"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__id
msgid "ID"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Image and Attachment"
msgstr "Immagine e allegato"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento da"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: cep_proposal
#: model:ir.model.fields.selection,name:cep_proposal.selection__cep_proposal_reaction__type__like
msgid "Like"
msgstr "Mi piace"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Like("
msgstr "Mi piace("

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__location
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Location"
msgstr "Posizione"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Location, End Date and Minimum Vote"
msgstr "Luogo, data di fine e voto minimo"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid ""
"Make your proposal stand out. This image will be shown\n"
"                                                    at the top of the content."
msgstr ""
"Fai risaltare la tua proposta. Questa immagine\n"
"                                                    verrà mostrata in cima al contenuto."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid ""
"Make your proposal stand out. This image will be shown at the top of the "
"content."
msgstr ""
"Fai risaltare la tua proposta. Questa immagine verrà mostrata in cima al"
"contenuto."

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__message
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__message
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Message"
msgstr "Messaggio"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__min_votes
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Minimum Vote"
msgstr "Voto minimo"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Newest"
msgstr "Più recente"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Official Update"
msgstr "Aggiornamento ufficiale"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__official_update_ids
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Official Updates"
msgstr "Aggiornamenti ufficiali"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_official_update_action
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_official_update
msgid "Official updates"
msgstr "Aggiornamenti ufficiali"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Oldest"
msgstr "Il più antico"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Other Info"
msgstr "Altre informazioni"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__owner_id
msgid "Owner"
msgstr "Proprietario"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__parent_id
msgid "Parent Comment"
msgstr "Commento del genitore"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Post"
msgstr "Pubblica"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Post your Proposal"
msgstr "Pubblica la tua proposta"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Posted on -"
msgstr "Postato il -"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__proposal_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__proposal_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__proposal_id
msgid "Proposal"
msgstr "Proposta"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Proposal Description"
msgstr "Descrizione della proposta"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Proposal Details"
msgstr "Dettagli della proposta"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_list
msgid "Proposal List"
msgstr "Elenco delle proposte"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Proposal Submit"
msgstr "Invio della proposta"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_proposal_action
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_proposal
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_root
msgid "Proposals"
msgstr "Proposte"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposal_list
msgid "Proposals (<span class=\"proposal-count\">0</span>)"
msgstr "Proposte (<span class=\"proposal-count\">0</span>)"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Proposed by"
msgstr "Proposto da"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Publish"
msgstr "Pubblica"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Publish your Proposal"
msgstr "Pubblica la tua proposta"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_reaction_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__reaction_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_reaction
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_comment_form
msgid "Reactions"
msgstr "Reazioni"

#. module: cep_proposal
#: model:ir.model.fields,help:cep_proposal.field_cep_proposal_proposal__website_id
msgid "Restrict publishing to this website."
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposal_list
msgid "Search ..."
msgstr "Ricerca"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Select an image (max. 10MB)"
msgstr "Seleziona un'immagine (max. 10MB)"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Share"
msgstr "Condividi"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Status"
msgstr "Stato"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__name
msgid "Tag"
msgstr "etichetta"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_tag_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__tag_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_tag
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Tags"
msgstr "Tag"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Tags <span class=\"fw-400\">(optional)</span>"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "This proposal expired as it didn't reach minimum votes in time."
msgstr "Questa proposta è scaduta perché non ha raggiunto i voti minimi in tempo."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "This proposal is open for vote."
msgstr "Questa proposta è aperta al voto."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "This proposal passed as it reached minimum votes in time."
msgstr "Questa proposta è stata approvata perché ha raggiunto il numero minimo di voti in tempo."

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__title
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Title"
msgstr "Titolo"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__type
msgid "Type"
msgstr "Tipo"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Type something and press enter"
msgstr "Digita qualcosa e premi Invio"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "Update Proposal"
msgstr "Proposta di aggiornamento"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid ""
"Upload files to give others more information and\n"
"                                                    context"
msgstr ""
"Carica i file per fornire agli altri maggiori informazioni e\n"
"                                                     contesto"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Upload files to give others more information and context"
msgstr "Carica i file per fornire agli altri maggiori informazioni e contesto"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Use Tags"
msgstr "Usa i tag"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_vote_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__vote_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_vote
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Votes"
msgstr "Voti"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__website_id
msgid "Website"
msgstr "Sito web"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "What is your Proposal?"
msgstr "Qual è la tua proposta?"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Write your comment here ..."
msgstr "Scrivi qui il tuo commento ..."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Write your reply here ..."
msgstr "Scrivi la tua risposta qui ..."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "proposal cover"
msgstr "copertina della proposta"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.custom_snippet
msgid "proposal, List"
msgstr "proposta, Elenco"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "votes"
msgstr "Voti"
