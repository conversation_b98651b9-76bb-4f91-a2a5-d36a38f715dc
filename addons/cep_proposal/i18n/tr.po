# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_proposal
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20241017\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-11 14:56+0000\n"
"PO-Revision-Date: 2024-12-11 14:56+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "&amp;times;"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid ""
"<i class=\"fa fa-check\"/>\n"
"                                                    <span>Vote</span>"
msgstr ""
"<i class=\"fa fa-check\"/>\n"
"                                                    <span>Oy</span>"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> İndirmek"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "<span class=\"close-btn\">&amp;times;</span>"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid ""
"<span id=\"description-char-numbers\">00</span>\n"
"                                                /600"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "<span id=\"description-char-numbers\">00</span>/600"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "<span>Cancel Vote</span>"
msgstr "<span>Oyu İptal Et</span>"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Add an Official Update"
msgstr "Resmi Güncelleme Ekle"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "All"
msgstr "Tüm"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__all_tags
msgid "All Tags"
msgstr "Tüm Etiketler"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__attachment_ids
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Attachments"
msgstr "Ekler"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Attachments<span class=\"fw-400 opacity-80\"> (optional)</span>"
msgstr "Ekler <span class=\"fw-400 opacity-80\"> (isteğe bağlı)</span>"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.custom_snippet
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_search
msgid "CEP Proposal"
msgstr "YSÖP Önerisi"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_comment
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_comment_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_comment_search
msgid "CEP Proposal Comment"
msgstr "YSÖP Öneri Yorumu"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_official_update
msgid "CEP Proposal Official Update"
msgstr "YSÖP Teklifi Resmi Güncellemesi"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_official_update_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_official_update_search
msgid "CEP Proposal Official update"
msgstr "YSÖP Teklifi Resmi Güncellemesi"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_reaction
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_reaction_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_reaction_search
msgid "CEP Proposal Reaction"
msgstr "YSÖP Öneri Tepkisi"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_tag
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_tag_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_tag_search
msgid "CEP Proposal Tag"
msgstr "YSÖP Teklif Etiketi"

#. module: cep_proposal
#: model:ir.model,name:cep_proposal.model_cep_proposal_vote
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_vote_form
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_vote_search
msgid "CEP Proposal Vote"
msgstr "YSÖP Öneri Oyu"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Click to select a file"
msgstr "Bir dosya seçmek için tıklayın"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__comment_id
msgid "Comment"
msgstr "Yorum"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_comment_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__comment_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_comment
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Comments"
msgstr "Yorumlar"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Comments ("
msgstr "Yorumlar ("

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__cover_photo
msgid "Cover Photo"
msgstr "Kapak Fotoğrafı"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Cover Photo<span class=\"fw-400 opacity-80\"> (optional)</span>"
msgstr "Kapak Fotoğrafı<span class=\"fw-400 opacity-80\"> (isteğe bağlı)</span>"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__cover_properties
msgid "Cover Properties"
msgstr "Kapak Özellikleri"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__create_date
msgid "Create Date"
msgstr "Tarih Oluştur"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_comment_action
msgid "Create a new comment"
msgstr "Yeni bir yorum oluştur"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_official_update_action
msgid "Create a new official update"
msgstr "Yeni bir resmi güncelleme oluştur"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_proposal_action
msgid "Create a new proposal"
msgstr "Yeni bir teklif oluştur"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_reaction_action
msgid "Create a new reaction"
msgstr "Yeni bir tepki oluştur"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_tag_action
msgid "Create a new tag"
msgstr "Yeni bir etiket oluştur"

#. module: cep_proposal
#: model_terms:ir.actions.act_window,help:cep_proposal.cep_proposal_vote_action
msgid "Create a new vote"
msgstr "Yeni bir oy oluştur"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__create_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__create_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__create_date
msgid "Created on"
msgstr "Oluşturulma tarihi"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__description
msgid "Description"
msgstr "Tanım"

#. module: cep_proposal
#: model:ir.model.fields.selection,name:cep_proposal.selection__cep_proposal_reaction__type__dislike
msgid "Dislike"
msgstr "Beğenmedim"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Dislike("
msgstr "Beğenmedim("

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__display_name
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__display_name
msgid "Display Name"
msgstr "Ekran adı"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Download Attachments"
msgstr "Ekleri İndir"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "Download existing attachment"
msgstr "Mevcut eki indir"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__end_date
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "End Date"
msgstr "Bitiş Tarihi"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Go Back"
msgstr "Geri gitmek"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__id
msgid "ID"
msgstr ""

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Image and Attachment"
msgstr "Resim ve Ek"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag____last_update
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote____last_update
msgid "Last Modified on"
msgstr "Son Değiştirilme tarihi"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__write_uid
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleme:"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__write_date
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme tarihi"

#. module: cep_proposal
#: model:ir.model.fields.selection,name:cep_proposal.selection__cep_proposal_reaction__type__like
msgid "Like"
msgstr "Beğenmek"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Like("
msgstr "Beğenmek("

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__location
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Location"
msgstr "Konum"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Location, End Date and Minimum Vote"
msgstr "Yer, Bitiş Tarihi ve Minimum Oy"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid ""
"Make your proposal stand out. This image will be shown\n"
"                                                    at the top of the content."
msgstr ""
"Teklifinizin öne çıkmasını sağlayın.\n"
"                                                    Bu resim içeriğin en üstünde gösterilecektir."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid ""
"Make your proposal stand out. This image will be shown at the top of the "
"content."
msgstr ""
"Teklifinizin öne çıkmasını sağlayın. Bu resim içeriğin en üstünde "
"gösterilecek."

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__message
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__message
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Message"
msgstr "Mesaj"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__min_votes
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Minimum Vote"
msgstr "Asgari Oy"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Newest"
msgstr "En yeni"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Official Update"
msgstr "Resmi Güncelleme"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__official_update_ids
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Official Updates"
msgstr "Resmi Güncellemeler"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_official_update_action
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_official_update
msgid "Official updates"
msgstr "Resmi Güncellemeler"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Oldest"
msgstr "En eski"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Other Info"
msgstr "Diğer Bilgiler"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__owner_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__owner_id
msgid "Owner"
msgstr "Mal sahibi"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__parent_id
msgid "Parent Comment"
msgstr "Ebeveyn Yorumu"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Post"
msgstr "Postalamak"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Post your Proposal"
msgstr "Teklifinizi yayınlayın"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Posted on -"
msgstr "Yayınlandığı tarih -"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__proposal_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_official_update__proposal_id
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_vote__proposal_id
msgid "Proposal"
msgstr "Teklif"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Proposal Description"
msgstr "Teklif Açıklaması"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Proposal Details"
msgstr "Teklif Ayrıntıları"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_list
msgid "Proposal List"
msgstr "Teklif Listesi"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Proposal Submit"
msgstr "Teklif Gönder"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_proposal_action
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_proposal
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_root
msgid "Proposals"
msgstr "Teklifler"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposal_list
msgid "Proposals (<span class=\"proposal-count\">0</span>)"
msgstr "Teklifler (<span class=\"proposal-count\">0</span>)"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Proposed by"
msgstr "Tarafından önerilen"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Publish"
msgstr "Yayınla"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Publish your Proposal"
msgstr "Teklifinizi Yayınlayın"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_reaction_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_comment__reaction_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_reaction
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_comment_form
msgid "Reactions"
msgstr "Tepkiler"

#. module: cep_proposal
#: model:ir.model.fields,help:cep_proposal.field_cep_proposal_proposal__website_id
msgid "Restrict publishing to this website."
msgstr "Bu web sitesinde yayınlamayı kısıtlayın."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposal_list
msgid "Search ..."
msgstr "Aramak ..."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Select an image (max. 10MB)"
msgstr "Bir resim seçin (maks. 10MB)"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Share"
msgstr "Paylaşmak"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Status"
msgstr "Durum"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_tag__name
msgid "Tag"
msgstr "Etiket"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_tag_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__tag_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_tag
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Tags"
msgstr "Etiketler"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Tags <span class=\"fw-400\">(optional)</span>"
msgstr "Etiketler <span class=\"fw-400\">(isteğe bağlı)</span>"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "This proposal expired as it didn't reach minimum votes in time."
msgstr "Bu teklifin süresi minimum oy sayısına zamanında ulaşamadığı için sona erdi."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "This proposal is open for vote."
msgstr "Bu öneri oylamaya açıktır."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "This proposal passed as it reached minimum votes in time."
msgstr "Bu öneri, zaman içinde minimum oylara ulaştığı için kabul edildi."

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__title
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Title"
msgstr "Başlık"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_reaction__type
msgid "Type"
msgstr "Tip"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Type something and press enter"
msgstr "Bir şey yazın ve enter tuşuna basın"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid "Update Proposal"
msgstr "Teklifi Güncelle"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
msgid ""
"Upload files to give others more information and\n"
"                                                    context"
msgstr ""
"Başkalarına daha fazla bilgi ve bağlam sağlamak \n"
"                                                    için dosya yükleyin"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Upload files to give others more information and context"
msgstr "Başkalarına daha fazla bilgi ve bağlam sağlamak için dosya yükleyin"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "Use Tags"
msgstr "Etiketleri Kullan"

#. module: cep_proposal
#: model:ir.actions.act_window,name:cep_proposal.cep_proposal_vote_action
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__vote_ids
#: model:ir.ui.menu,name:cep_proposal.cep_proposal_vote
#: model_terms:ir.ui.view,arch_db:cep_proposal.view_cep_proposal_proposal_form
msgid "Votes"
msgstr "Oylar"

#. module: cep_proposal
#: model:ir.model.fields,field_description:cep_proposal.field_cep_proposal_proposal__website_id
msgid "Website"
msgstr "Web sitesi"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_edit
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_new
msgid "What is your Proposal?"
msgstr "Teklifiniz nedir?"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Write your comment here ..."
msgstr "Yorumunuzu buraya yazın..."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "Write your reply here ..."
msgstr "Cevabınızı buraya yazın..."

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "proposal cover"
msgstr "teklif kapağı"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.custom_snippet
msgid "proposal, List"
msgstr "teklif, Liste"

#. module: cep_proposal
#: model_terms:ir.ui.view,arch_db:cep_proposal.proposals_view
msgid "votes"
msgstr "Oylar"
