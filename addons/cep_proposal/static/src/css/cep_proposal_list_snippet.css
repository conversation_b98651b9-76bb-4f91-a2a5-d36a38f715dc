.s_proposals .btn-filter {
    border-radius: 5px;
    border: 1px solid #D8E2EF;
}

.s_proposals .card {
    border-radius: 16px;
    background: #FFF;
    box-shadow: 0px 8px 30px 0px rgba(24, 28, 50, 0.16);
    height: 320px;
}

.s_proposals .card-proposal .card-body {
    padding: 1.25rem;
}

.s_proposals .card-proposal .link {
    color: rgba(13, 12, 34, 0.90);
    font-family: Nuni<PERSON>;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    height: 49.6px;
}

.s_proposals .pagination .page-item {
    margin-right: 4px;
    margin-left: 4px;

    border-radius: 4px;
    border: 1px solid #D8E2EF;
    background: #FFF;
}

.s_proposals .pagination .page-item.prev, .s_proposals .pagination .page-item.next {
    border-radius: 0;
    border: none;
    background: none;
}

.s_proposals .dropdown-toggle {
    color: #181C32;
    font-family: '<PERSON><PERSON><PERSON>';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.s_proposals .search.input-group {
    border-radius: 5px;
    border-top-right-radius: 5px !important;
    border-bottom-right-radius: 5px !important;
    border: 1px solid #D8E2EF;
    background: rgba(255, 255, 255, 0.50);
    padding: 2px !important;
}

.s_proposals .search .form-control, .s_proposals .search .input-group-append {
    border-radius: 0px;
    border: none;
    background: rgba(255, 255, 255, 0.50);
}

.s_proposals .pagination .page-item .page-link {
    border-radius: 4px;
    border: 1px solid #D8E2EF;
    background: #FFF;

    color: #181C32;
    text-align: center;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.s_proposals .pagination .page-item.active .page-link {
    background: #C9D6F1;
}

.s_proposals .pagination .page-item.prev .page-link, .s_proposals .pagination .page-item.next .page-link {
    border: 0;
    border-radius: 0;
    background: none;
}

.s_proposals .card-proposal .featured-image {
    border-radius: 16px;
    min-height: 149px;
    max-height: 149px;
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) { 
    .s_proposals .card-proposal .featured-image {
        border-radius: 16px;
    }
}

.s_proposals #suggestion-list .dropdown-item {
    white-space: normal;
}