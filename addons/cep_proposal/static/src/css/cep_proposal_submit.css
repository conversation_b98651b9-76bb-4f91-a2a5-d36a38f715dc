.s_proposal_submit .back-btn {
    background-color: #ACEBE8;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 30px;
}

.s_proposal_submit .card {
    border-radius: 16px;
    background: #FFF;
    box-shadow: 0px 12px 40px 0px rgba(9, 23, 107, 0.12);
}

.s_proposal_submit #proposal_submit_form, .s_proposal_submit #proposal_update_form {
    max-width: 100%;
}

.s_proposal_submit .ql-toolbar {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
}

.s_proposal_submit .summary {
    color: #181C32;
    font-family: Nunito;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
} 

.s_proposal_submit .btn-submit {
    border-radius: 8px;
    background: var(--1-fccc-6, #1FCCC6);
    color: #FFF;
    text-align: center;
    font-family: Nunito;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}

.s_proposal_submit .dropdown-toggle {
    border-radius: 5px;
    border: 1px solid #D8E2EF;
    background: #FFF;

    color: #181C32;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.s_proposal_submit .form-control {
    background: #FFF;
    color: #181C32;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.s_proposal_submit .upload-image {
    cursor: pointer;
    border-radius: 8px;
    border: 1px dashed #ACB7C6;
    background: #FFF;
    font-size: 34px;
}

/* CSS by Atiq */
.s_proposal_submit .badge-tag:hover {
    background-color: #b4c3d3;
    cursor: pointer;
}

/* CSS by Tanvir */
/* For tags */
.s_proposal_submit .tags-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    border: 1px solid #ced4da;
    padding: 4px;
    border-radius: 0.375rem;
    min-height: 38px;
}
.s_proposal_submit .tag {
    display: inline-flex;
    align-items: center;
    padding: 0.25em 0.5em;
    margin: 0.25em;
    background-color: #f1f1f1;
    border-radius: 5px;
    opacity: 0.8;
    background: #EEF3FE;
    color: #181C32;
    font-family: 'Nunito';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}
.s_proposal_submit .tag .close-btn {
    margin-left: 0.5em;
    font-weight: bold;
    cursor: pointer;
}
.s_proposal_submit .tags-container input {
    border: none;
    outline: none;
    flex-grow: 1;
    min-width: 120px;
}
.s_proposal_submit .tags-container input:focus {
    box-shadow: none;
}