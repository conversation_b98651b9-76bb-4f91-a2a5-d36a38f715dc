# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import models, fields


class Vote(models.Model):
    _name = 'cep.proposal.vote'
    _description = 'CEP Proposal Vote'
    _rec_name = 'owner_id'

    owner_id = fields.Many2one('res.users', string='Owner', index=True, tracking=True, default=lambda self: self.env.user)
    proposal_id = fields.Many2one('cep.proposal.proposal', string='Proposal')