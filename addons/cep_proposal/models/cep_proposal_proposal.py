# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import fields, models, api
from odoo.exceptions import ValidationError

from datetime import datetime, timedelta


class Proposal(models.Model):
    _name = "cep.proposal.proposal"
    _description = "CEP Proposal"
    _rec_name = 'title'
    _inherit = [
        'website.multi.mixin',
        'website.cover_properties.mixin',
        'website.searchable.mixin',
    ]

    title = fields.Char('Title', required=True, translate=True)
    description = fields.Html('Description', required=True, translate=True)
    cover_photo = fields.Binary(string="Cover Photo", attachment=False) 
    attachment_ids = fields.One2many(
        'ir.attachment', 'res_id', domain=[('res_model', '=', 'cep.proposal.proposal')],
        string='Attachments', ondelete='cascade')
        
    create_date = fields.Datetime(
        string='Create Date', default=lambda self: datetime.now(), readonly=True)
    end_date = fields.Date(string='End Date', default=lambda self: (
        datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d'), required=True)
    location = fields.Char(string='Location', required=True)
    min_votes = fields.Integer(default=150, string='Minimum Vote')
    owner_id = fields.Many2one('res.users', string='Owner', index=True,
                               tracking=True, default=lambda self: self.env.user)
    tag_ids = fields.Many2many('cep.proposal.tag', string='Tags')
    comment_ids = fields.One2many(
        'cep.proposal.comment', 'proposal_id', string='Comments')
    vote_ids = fields.One2many(
        'cep.proposal.vote', 'proposal_id', string='Votes')
    official_update_ids = fields.One2many(
        'cep.proposal.official.update', 'proposal_id', string='Official Updates')
    all_tags = fields.Char('All Tags', store=False)

    def get_parent_comments(self, filter='newest'):
        parent_comments = self.comment_ids.search(
            [('parent_id', '=', False), ('proposal_id', '=', self.id)]
        )

        if filter == 'newest':
            parent_comments = parent_comments.sorted(
                key=lambda c: c.create_date, reverse=True)[:5]
        elif filter == 'oldest':
            parent_comments = parent_comments.sorted(
                key=lambda c: c.create_date)[:5]
        elif filter == 'all':
            parent_comments = parent_comments

        return parent_comments

    def get_child_comments(self, parent_id):
        child_comments = self.comment_ids.search(
            [('parent_id', '=', parent_id), ('proposal_id', '=', self.id)])
        return child_comments

    def get_status(self):
        if datetime.now().date() < self.end_date:
            return "ongoing"
        elif datetime.now().date() > self.end_date and self.min_votes <= len(self.vote_ids):
            return "passed"
        else:
            return "expired"

    def format_create_date(self):
        format_date = self.create_date.strftime('%B %d, %Y')
        return format_date

    def count_vote(self):
        vote = self.vote_ids.search([('proposal_id', '=', self.id)])
        return len(vote)

    def last_official_update(self):
        last_official_update = self.official_update_ids.search(
            [('proposal_id', '=', self.id)], order='id desc', limit=1)
        return last_official_update

    @api.constrains('min_votes')
    def _check_min_votes(self):
        for proposal in self:
            if proposal.min_votes < 150:
                raise ValidationError(
                    "A proposal minimum votes must have at least 150 votes.")

    @api.constrains('end_date')
    def _check_end_date(self):
        for proposal in self:
            if proposal.end_date < proposal.create_date.date() + timedelta(days=30):
                raise ValidationError(
                    "A proposal end date must be at least 30 days after the create date.")

    @api.model
    def _search_get_detail(self, website, order, options):
        with_description = options['displayDescription']
        search_fields = ['title']
        fetch_fields = ['id', 'title', 'description']
        mapping = {
            'name': {'name': 'title', 'type': 'text', 'match': True},
            'website_url': {'name': 'url', 'type': 'text', 'truncate': False},
        }
        if with_description:
            search_fields.append('description')
            fetch_fields.append('description')
            mapping['description'] = {
                'name': 'description', 'type': 'text', 'match': True}
        return {
            'model': 'cep.proposal.proposal',
            'requires_sudo': True,
            'base_domain': [website.website_domain()],
            'search_fields': search_fields,
            'fetch_fields': fetch_fields,
            'mapping': mapping,
            'icon': 'fa-sticky-note-o',
            'order': 'title desc, id desc' if 'title desc' in order else 'title asc, id desc',
        }

    def _search_render_results(self, fetch_fields, mapping, icon, limit):
        results_data = super()._search_render_results(
            fetch_fields, mapping, icon, limit)
        for data in results_data:
            data['url'] = '/proposals/%s/view' % data['id']
        return results_data
