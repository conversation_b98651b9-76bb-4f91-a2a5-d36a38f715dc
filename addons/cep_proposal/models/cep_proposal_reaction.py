# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import models, fields

class Reaction(models.Model):
    _name = 'cep.proposal.reaction'
    _description = 'CEP Proposal Reaction'
    _rec_name = 'type'

    type = fields.Selection([
        ('like', 'Like'),
        ('dislike', 'Dislike'),
    ], string='Type', required=True)
    owner_id = fields.Many2one('res.users', string='Owner', index=True, tracking=True, default=lambda self: self.env.user)
    comment_id = fields.Many2one('cep.proposal.comment', string='Comment')