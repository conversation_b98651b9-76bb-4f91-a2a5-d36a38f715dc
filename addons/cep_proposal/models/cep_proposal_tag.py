# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import models, fields


class Tag(models.Model):
    _name = 'cep.proposal.tag'
    _description = 'CEP Proposal Tag'
    _rec_name = 'name'

    name = fields.Char('Tag', required=True)
    owner_id = fields.Many2one('res.users', string='Owner', index=True, tracking=True, default=lambda self: self.env.user)
