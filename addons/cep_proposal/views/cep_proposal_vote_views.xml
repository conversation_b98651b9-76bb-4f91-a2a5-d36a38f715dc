<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_proposal_vote_search" model="ir.ui.view">
        <field name="name">cep.proposal.vote.search</field>
        <field name="model">cep.proposal.vote</field>
        <field name="arch" type="xml">
            <search string="CEP Proposal Vote">
                <field name="owner_id"/>
                <field name="proposal_id"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_proposal_vote_tree" model="ir.ui.view">
        <field name="name">cep.proposal.vote.tree</field>
        <field name="model">cep.proposal.vote</field>
        <field name="arch" type="xml">
            <tree>
                <field name="owner_id"/>
                <field name="proposal_id"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_proposal_vote_form" model="ir.ui.view">
        <field name="name">cep.proposal.vote.form</field>
        <field name="model">cep.proposal.vote</field>
        <field name="arch" type="xml">
            <form string="CEP Proposal Vote">
                <sheet>
                    <group>
                        <field name="owner_id"/>
                        <field name="proposal_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id='cep_proposal_vote_action' model='ir.actions.act_window'>
        <field name="name">Votes</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.proposal.vote</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new vote
            </p>
        </field>
    </record>

    <menuitem id="cep_proposal_vote" name="Votes" parent="cep_proposal_root" action="cep_proposal_vote_action"/>
</odoo>