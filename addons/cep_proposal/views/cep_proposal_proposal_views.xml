<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_proposal_proposal_search" model="ir.ui.view">
        <field name="name">cep.proposal.proposal.search</field>
        <field name="model">cep.proposal.proposal</field>
        <field name="arch" type="xml">
            <search string="CEP Proposal">
                <field name="title"/>
                <field name="description"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_proposal_proposal_tree" model="ir.ui.view">
        <field name="name">cep.proposal.proposal.tree</field>
        <field name="model">cep.proposal.proposal</field>
        <field name="arch" type="xml">
            <tree>
                <field name="title"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_proposal_proposal_form" model="ir.ui.view">
        <field name="name">cep.proposal.proposal.form</field>
        <field name="model">cep.proposal.proposal</field>
        <field name="arch" type="xml">
            <form string="CEP Proposal">
                <sheet>
                    <field name="cover_photo" widget="image" class="oe_avatar"/>
                    <group>
                        <field name="title"/>
                        <field name="description"/>
                        <field name="tag_ids" widget="many2many_tags"/>
                        <field name="location"/>
                        <field name="min_votes"/>
                        <field name="create_date"/>
                        <field name="end_date"/>
                    </group>
                    <notebook>
                        <page name="image_and_attachment" string="Image and Attachment">

                            <field name="attachment_ids" widget="one2many_list"
                                options="{'no_create': True, 'no_open': True}" readonly="1">
                                <tree editable="top">
                                    <field name="name"/>
                                    <field name="create_date"/>
                                </tree>
                                <form>

                                    <sheet>

                                        <group>
                                            <field name="type"/>
                                            <field name="datas" filename="name"
                                                attrs="{'invisible':[('type','=','url')]}"/>
                                            <field name="url" widget="url" attrs="{'invisible':[('type','=','binary')]}"/>
                                            <field name="mimetype" groups="base.group_no_one"/>
                                        </group>

                                    </sheet>

                                </form>
                            </field>

                        </page>
                        <page name="comments" string="Comments">
                            <field name="comment_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="message"/>
                                        <field name="parent_id"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page name="official_updates" string="Official Updates">
                            <field name="official_update_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="message"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page name="votes" string="Votes">
                            <field name="vote_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="owner_id"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page name="other_info" string="Other Info">
                            <group>
                                <field name="owner_id"/>
                                <field name="create_date"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id='cep_proposal_proposal_action' model='ir.actions.act_window'>
        <field name="name">Proposals</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.proposal.proposal</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new proposal
            </p>
        </field>
    </record>

    <menuitem id="cep_proposal_root" name="Proposals" sequence="0">
        <menuitem id="cep_proposal_proposal" name="Proposals" action="cep_proposal_proposal_action"/>
    </menuitem>
</odoo>