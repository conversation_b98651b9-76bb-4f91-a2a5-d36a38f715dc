<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_proposal_tag_search" model="ir.ui.view">
        <field name="name">cep.proposal.tag.search</field>
        <field name="model">cep.proposal.tag</field>
        <field name="arch" type="xml">
            <search string="CEP Proposal Tag">
                <field name="name"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_proposal_tag_tree" model="ir.ui.view">
        <field name="name">cep.proposal.tag.tree</field>
        <field name="model">cep.proposal.tag</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_proposal_tag_form" model="ir.ui.view">
        <field name="name">cep.proposal.tag.form</field>
        <field name="model">cep.proposal.tag</field>
        <field name="arch" type="xml">
            <form string="CEP Proposal Tag">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="owner_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id='cep_proposal_tag_action' model='ir.actions.act_window'>
        <field name="name">Tags</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.proposal.tag</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new tag
            </p>
        </field>
    </record>

    <menuitem id="cep_proposal_tag" name="Tags" parent="cep_proposal_root" action="cep_proposal_tag_action"/>
</odoo>