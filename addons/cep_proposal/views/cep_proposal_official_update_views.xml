<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_proposal_official_update_search" model="ir.ui.view">
        <field name="name">cep.proposal.official.update.search</field>
        <field name="model">cep.proposal.official.update</field>
        <field name="arch" type="xml">
            <search string="CEP Proposal Official update">
                <field name="message"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_proposal_official_update_tree" model="ir.ui.view">
        <field name="name">cep.proposal.official.update.tree</field>
        <field name="model">cep.proposal.official.update</field>
        <field name="arch" type="xml">
            <tree>
                <field name="message"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_proposal_official_update_form" model="ir.ui.view">
        <field name="name">cep.proposal.official.update.form</field>
        <field name="model">cep.proposal.official.update</field>
        <field name="arch" type="xml">
            <form string="CEP Proposal Official update">
                <sheet>
                    <group>
                        <field name="message"/>
                        <field name="proposal_id"/>
                        <field name="owner_id"/>
                        <field name="create_date"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id='cep_proposal_official_update_action' model='ir.actions.act_window'>
        <field name="name">Official updates</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.proposal.official.update</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new official update
            </p>
        </field>
    </record>

    <menuitem id="cep_proposal_official_update" name="Official updates" parent="cep_proposal_root" action="cep_proposal_official_update_action"/>
</odoo>