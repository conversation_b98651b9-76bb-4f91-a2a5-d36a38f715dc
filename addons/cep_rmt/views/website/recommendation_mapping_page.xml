<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Main Template -->
        <template id="recommendation_mapping_page" name="Recommendation Mapping Tool">
            <t t-call="website.layout">
                <t t-set="head">
                    <!-- Include custom assets -->
                    <t t-call-assets="web.assets_frontend_minimal"/>
                    <t t-call-assets="cep_rmt.assets"/>
                </t>
                <div id="wrap">
                    <div class="container">
                        <!-- Header Section -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="text-center mb-4">
                                    <h1 class="display-4 text-primary">Recommendation Mapping Tool</h1>
                                    <p class="lead">
                                        The COOF-Based Recommendation Mapping Tool is an advanced decision-support
                                        system designed to align national adaptation strategies with
                                        the Context-Operations-Outcomes-Feedbacks (COOF) framework for polycentric
                                        governance. This tool enables users to systematically analyze
                                        and refine adaptation recommendations by integrating them with their respective
                                        countries' National Adaptation Plan (NAP) or other national
                                        climate policy documents.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Key Features Section -->
                        <div class="row mb-1">
                            <div class="col-12">
                                <h3 class="text-primary mb-3">Key Features:</h3>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>Automated COOF Parameter Extraction:</strong> The tool processes
                                        national adaptation policy documents (PDF format) to identify key governance
                                        parameters that align with the COOF framework. </li>
                                    <li class="mb-2">
                                        <strong>Recommendation Mapping:</strong> Users input their climate adaptation
                                        recommendations, and the tool cross-references them with the relevant national
                                        policy provisions to ensure coherence with existing governance structures. </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Visualization Section -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="text-center">
                                    <div id="network" class="mb-3">
                                        <img src="/cep_rmt/static/src/img/flowchart-image.png" alt="flowchart"
                                            class="img-fluid flowchart-image"/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Multi-Step Recommendation Form -->
                        <div class="row mb-5">
                            <div class="col-12">

                                <div class="form-header">
                                    <div class="text-center mb-1">
                                        <h2 class="fw-bold text-primary form-header-title">Output
                                            Generation</h2>
                                    </div>

                                    <!-- Progress Steps -->
                                    <div id="progress-steps"
                                        class="d-flex justify-content-center align-items-center mb-5">
                                        <div class="step-line step-line-inactive" data-step="1"></div>
                                        <div class="step-line step-line-active" data-step="2"></div>
                                        <div class="step-line step-line-inactive" data-step="3"></div>
                                        <div class="step-line step-line-inactive" data-step="4"></div>
                                        <div class="step-line step-line-inactive" data-step="5"></div>
                                        <div class="step-line step-line-inactive" data-step="6"></div>
                                        <div class="step-line step-line-inactive" data-step="7"></div>
                                        <div class="step-line step-line-inactive" data-step="8"></div>
                                        <div class="step-line step-line-inactive" data-step="9"></div>
                                        <div class="step-line step-line-inactive" data-step="10"></div>
                                        <div class="step-line step-line-inactive" data-step="11"></div>
                                        <div class="step-line step-line-inactive" data-step="12"></div>
                                    </div>
                                </div>

                                <div class="card shadow-sm form-card">
                                    <div class="card-body p-5">
                                        <div id="recommendation-form-container">

                                            <t t-call="cep_rmt.form_step_one"/>
                                            <t t-call="cep_rmt.form_step_two"/>
                                            <t t-call="cep_rmt.form_step_three"/>
                                            <t t-call="cep_rmt.form_step_four"/>
                                            <t t-call="cep_rmt.form_step_five"/>
                                            <t t-call="cep_rmt.form_step_six"/>
                                            <t t-call="cep_rmt.form_step_seven"/>
                                            <t t-call="cep_rmt.form_step_eight"/>
                                            <t t-call="cep_rmt.form_step_nine"/>
                                            <t t-call="cep_rmt.form_step_ten"/>
                                            <t t-call="cep_rmt.form_step_eleven"/>
                                            <t t-call="cep_rmt.form_step_twelve"/>


                                            <!-- Navigation Buttons -->
                                            <div class="d-flex justify-content-between mt-5">
                                                <button type="button" id="prev-btn"
                                                    class="btn btn-outline-primary px-4 py-2 d-none btn-prev">
                                                    Previous
                                                </button>
                                                <div></div>
                                                <button type="button" id="next-btn"
                                                    class="btn btn-primary px-4 py-2 btn-next">
                                                    Next
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- References Modal -->
                <div class="modal fade" id="referencesModal" tabindex="-1" aria-labelledby="referencesModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="referencesModalLabel">References</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div id="references-content">
                                    <p class="text-muted">No references available for this step.</p>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
    </data>
</odoo>