# Consolidated Steps Component

This file (`steps_consolidated.js`) combines all the individual step files into a single, simplified structure for easier maintenance and understanding.

## What was consolidated

The following individual step files have been consolidated:
- `step_1.js` - Recommendation Input Component
- `step_2.js` - Stakeholders Component  
- `step_3.js` - Implementation Stakeholders Component
- `step_4.js` - Governance Rules Component
- `step_5.js` - Socio-ecological Challenges Component
- `step_6.js` - Coordination Mechanisms Component
- `step_8.js` - Implementation Processes Component
- `step_9.js` - Expected Outcomes Component
- `step_10.js` - Monitoring Indicators Component
- `step_11.js` - Feedback Mechanisms Component

## Key Simplifications

1. **Unified Configuration**: All step configurations are defined in a single `STEP_CONFIG` object
2. **Shared Event Binding**: Common event binding patterns are abstracted into reusable functions
3. **Consistent Validation**: All validation logic follows the same pattern
4. **Simplified Data Collection**: Data collection is standardized across all steps
5. **Backward Compatibility**: The original step objects (`Step1`, `Step2`, etc.) are still available

## Usage

### Initialize all steps
```javascript
window.CEP_RMT.Steps.Manager.init();
```

### Initialize a specific step
```javascript
window.CEP_RMT.Steps.Manager.init(1); // Initialize step 1
```

### Use individual step objects (backward compatible)
```javascript
window.CEP_RMT.Steps.Step1.init();
window.CEP_RMT.Steps.Step1.validate();
window.CEP_RMT.Steps.Step1.submit();
```

### Direct access to manager functions
```javascript
// Validate a specific step
window.CEP_RMT.Steps.Manager.validateStep(2);

// Submit a specific step
await window.CEP_RMT.Steps.Manager.submitStep(3);

// Load data into a step
window.CEP_RMT.Steps.Manager.loadStepData(4, data);
```

## Dependencies

This consolidated file still depends on:
- `window.CEP_RMT.FormBase` - For form state management and utility functions
- `window.CEP_RMT.SuggestionsHandler` - For handling automated suggestions
- jQuery - For DOM manipulation and event handling

## Benefits

1. **Easier Maintenance**: All step logic is in one place
2. **Reduced Duplication**: Common patterns are abstracted
3. **Better Organization**: Clear separation of concerns
4. **Simplified Debugging**: Easier to trace issues across steps
5. **Consistent Behavior**: All steps follow the same patterns

## Migration

To use the consolidated version:

1. Replace all individual step file includes with `steps_consolidated.js`
2. Update any direct references to step files
3. The existing API remains the same, so no code changes are required

## File Structure

```
addons/cep_rmt/static/src/js/
├── steps_consolidated.js          # New consolidated file
├── common/
│   ├── form_base.js              # Still needed
│   └── suggestions_handler.js    # Still needed
└── steps/                        # Original files (can be archived)
    ├── step_1.js
    ├── step_2.js
    └── ...
```
