// Consolidated Steps Component for CEP RMT
// This file combines all step functionality into a single, simplified structure

window.CEP_RMT = window.CEP_RMT || {};
window.CEP_RMT.Steps = window.CEP_RMT.Steps || {};

// Consolidated Steps Manager
window.CEP_RMT.Steps.Manager = (function() {
  'use strict';

  // Step configuration with their specific settings
  const STEP_CONFIG = {
    1: {
      name: 'recommendation',
      fields: ['recommendation', 'outputCount', 'selectedFile', 'fileName'],
      hasFileUpload: true,
      hasSuggestions: false,
      validation: (formState) => {
        if (!formState.data.recommendation.trim()) {
          return 'Recommendation text is required';
        }
        if (!formState.data.selectedFile) {
          return 'NAP file is required';
        }
        return null;
      }
    },
    2: {
      name: 'stakeholders_involved',
      fields: ['stakeholders', 'selectedStakeholders'],
      hasFileUpload: false,
      hasSuggestions: true,
      suggestionCategories: ['Top Stakeholders'],
      validation: (formState) => {
        if (!formState.data.stakeholders.trim() && !formState.data.selectedStakeholders.length) {
          return 'Please enter stakeholders information';
        }
        return null;
      }
    },
    3: {
      name: 'stakeholders',
      fields: ['implementationStakeholder', 'ownershipStakeholder', 'progressTracker', 'feedbackHandler', 'outcomeEvaluator'],
      hasFileUpload: false,
      hasSuggestions: false,
      validation: (formState) => {
        if (!formState.data.implementationStakeholder.trim()) {
          return 'Implementation stakeholder is required';
        }
        return null;
      }
    },
    4: {
      name: 'governance_rules',
      fields: ['governance', 'selectedGovernance'],
      hasFileUpload: false,
      hasSuggestions: true,
      suggestionCategories: ['Top Governance Rules'],
      validation: (formState) => {
        if (!formState.data.governance.trim()) {
          return 'Please enter governance rules information';
        }
        return null;
      }
    },
    5: {
      name: 'identified_challenges',
      fields: ['challenges', 'selectedChallenges'],
      hasFileUpload: false,
      hasSuggestions: true,
      suggestionCategories: ['Top Socio-ecological Challenges'],
      validation: (formState) => {
        if (!formState.data.challenges.trim()) {
          return 'Please enter challenges information';
        }
        return null;
      }
    },
    6: {
      name: 'coordination_mechanisms',
      fields: ['coordination', 'selectedCoordination'],
      hasFileUpload: false,
      hasSuggestions: true,
      suggestionCategories: ['Top Coordination Mechanisms'],
      validation: (formState) => {
        if (!formState.data.coordination.trim()) {
          return 'Please enter coordination information';
        }
        return null;
      }
    },
    8: {
      name: 'implementation_processes',
      fields: ['implementation', 'selectedImplementation'],
      hasFileUpload: false,
      hasSuggestions: true,
      suggestionCategories: ['Top Processes'],
      validation: (formState) => {
        if (!formState.data.implementation.trim()) {
          return 'Please enter implementation information';
        }
        return null;
      }
    },
    9: {
      name: 'expected_outcomes',
      fields: ['outcomes', 'selectedOutcomes'],
      hasFileUpload: false,
      hasSuggestions: true,
      suggestionCategories: ['Top Improved Project Resilience Factors', 'Top Community Satisfaction Factors'],
      validation: (formState) => {
        if (!formState.data.outcomes.trim()) {
          return 'Please enter outcomes information';
        }
        return null;
      }
    },
    10: {
      name: 'monitoring_indicators',
      fields: ['monitoring', 'selectedMonitoring'],
      hasFileUpload: false,
      hasSuggestions: true,
      suggestionCategories: ['Top Monitoring Indicators'],
      validation: (formState) => {
        if (!formState.data.monitoring.trim()) {
          return 'Please enter monitoring information';
        }
        return null;
      }
    },
    11: {
      name: 'feedback_mechanisms',
      fields: ['feedback', 'selectedFeedback'],
      hasFileUpload: false,
      hasSuggestions: true,
      suggestionCategories: ['Top Feedback Mechanisms'],
      validation: (formState) => {
        if (!formState.data.feedback.trim()) {
          return 'Please enter feedback information';
        }
        return null;
      }
    }
  };

  // Initialize a specific step
  function initStep(stepNumber) {
    const config = STEP_CONFIG[stepNumber];
    if (!config) {
      console.warn(`Step ${stepNumber} configuration not found`);
      return;
    }

    bindStepEvents(stepNumber, config);
    
    if (config.hasSuggestions) {
      populateStepSuggestions(stepNumber, config);
    }
  }

  // Bind events for a specific step
  function bindStepEvents(stepNumber, config) {
    const formState = window.CEP_RMT.FormBase.getFormState();

    // Handle file upload for step 1
    if (config.hasFileUpload && stepNumber === 1) {
      bindFileUploadEvents();
    }

    // Bind input field events based on step configuration
    bindInputEvents(stepNumber, config);

    // Bind suggestion events if step has suggestions
    if (config.hasSuggestions) {
      bindSuggestionEvents(stepNumber);
    }
  }

  // Bind file upload events (Step 1 specific)
  function bindFileUploadEvents() {
    // File upload input
    $('#step1_file_upload').off('change').on('change', function () {
      const file = this.files[0];
      if (file) {
        processFile(file);
      }
    });

    // Drag and drop events
    $('#drop-zone').off('dragover dragleave drop click')
      .on('dragover', function (ev) {
        ev.preventDefault();
        ev.stopPropagation();
        ev.originalEvent.dataTransfer.dropEffect = 'copy';
        $(this).addClass('drag-over');
      })
      .on('dragleave', function (ev) {
        ev.preventDefault();
        ev.stopPropagation();
        $(this).removeClass('drag-over');
      })
      .on('drop', function (ev) {
        ev.preventDefault();
        ev.stopPropagation();
        $(this).removeClass('drag-over');
        const files = ev.originalEvent.dataTransfer.files;
        if (files.length > 0) {
          processFile(files[0]);
        }
      })
      .on('click', function () {
        $('#step1_file_upload').click();
      });
  }

  // Process uploaded file
  function processFile(file) {
    const formState = window.CEP_RMT.FormBase.getFormState();
    
    // Validate file type
    const allowedTypes = ['application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      window.CEP_RMT.FormBase.showErrorMessage('Please upload a PDF document');
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      window.CEP_RMT.FormBase.showErrorMessage('File size must be less than 10MB');
      return;
    }

    // Store file in form state
    formState.data.selectedFile = file;
    formState.data.fileName = file.name;

    // Update UI
    $('#file-name').text(file.name);
    $('#file-size').text((file.size / 1024 / 1024).toFixed(2) + ' MB');
    $('#file-info').removeClass('d-none');
    $('#drop-zone-text').addClass('d-none');
  }

  // Bind input events for each step
  function bindInputEvents(stepNumber, config) {
    const formState = window.CEP_RMT.FormBase.getFormState();

    // Step-specific input bindings
    switch(stepNumber) {
      case 1:
        $('#step1_recommendation_input').off('input').on('input', function () {
          formState.data.recommendation = $(this).val();
        });
        $('#step1_output_count').off('change').on('change', function () {
          formState.data.outputCount = parseInt($(this).val());
        });
        break;
      
      case 2:
        $('#step2_stakeholders_input').off('input').on('input', function () {
          formState.data.stakeholders = $(this).val();
        });
        break;
      
      case 3:
        $('#step3_implementation_stakeholder').off('input').on('input', function () {
          formState.data.implementationStakeholder = $(this).val();
        });
        $('#step3_ownership_stakeholder').off('input').on('input', function () {
          formState.data.ownershipStakeholder = $(this).val();
        });
        $('#step3_progress_tracker').off('input').on('input', function () {
          formState.data.progressTracker = $(this).val();
        });
        $('#step3_feedback_handler').off('input').on('input', function () {
          formState.data.feedbackHandler = $(this).val();
        });
        $('#step3_outcome_evaluator').off('input').on('input', function () {
          formState.data.outcomeEvaluator = $(this).val();
        });
        break;
      
      case 4:
        $('#step4_governance_input').off('input').on('input', function () {
          formState.data.governance = $(this).val();
        });
        break;
      
      case 5:
        $('#step5_challenges_input').off('input').on('input', function () {
          formState.data.challenges = $(this).val();
        });
        break;
      
      case 6:
        $('#step6_coordination_input').off('input').on('input', function () {
          formState.data.coordination = $(this).val();
        });
        break;
      
      case 8:
        $('#step8_implementation_input').off('input').on('input', function () {
          formState.data.implementation = $(this).val();
        });
        break;
      
      case 9:
        $('#step9_outcomes_input').off('input').on('input', function () {
          formState.data.outcomes = $(this).val();
        });
        break;
      
      case 10:
        $('#step10_monitoring_input').off('input').on('input', function () {
          formState.data.monitoring = $(this).val();
        });
        break;
      
      case 11:
        $('#step11_feedback_input').off('input').on('input', function () {
          formState.data.feedback = $(this).val();
        });
        break;
    }
  }

  // Bind suggestion events for steps with suggestions
  function bindSuggestionEvents(stepNumber) {
    // Suggestion checkboxes
    $(document).off('change', `input[id^="step${stepNumber}_suggestion"]`)
               .on('change', `input[id^="step${stepNumber}_suggestion"]`, function () {
      updateSelectedSuggestions(stepNumber);
    });

    // Reference link
    $(`#step${stepNumber}_reference_link`).off('click').on('click', function (e) {
      e.preventDefault();
      showReferencesModal(stepNumber);
    });
  }

  // Populate suggestions for a step
  function populateStepSuggestions(stepNumber, config) {
    if (!window.CEP_RMT.SuggestionsHandler) {
      console.warn('SuggestionsHandler not available');
      return;
    }

    window.CEP_RMT.SuggestionsHandler.populateAutomatedSuggestions(stepNumber);
  }

  // Update selected suggestions for a step
  function updateSelectedSuggestions(stepNumber) {
    const formState = window.CEP_RMT.FormBase.getFormState();
    const selectedSuggestions = [];

    $(`input[id^="step${stepNumber}_suggestion"]:checked`).each(function () {
      const label = $(this).next('label').text().trim();
      selectedSuggestions.push(label);
    });

    // Store in formState based on step
    switch(stepNumber) {
      case 2:
        formState.data.selectedStakeholders = selectedSuggestions;
        break;
      case 4:
        formState.data.selectedGovernance = selectedSuggestions;
        break;
      case 5:
        formState.data.selectedChallenges = selectedSuggestions;
        break;
      case 6:
        formState.data.selectedCoordination = selectedSuggestions;
        break;
      case 8:
        formState.data.selectedImplementation = selectedSuggestions;
        break;
      case 9:
        formState.data.selectedOutcomes = selectedSuggestions;
        break;
      case 10:
        formState.data.selectedMonitoring = selectedSuggestions;
        break;
      case 11:
        formState.data.selectedFeedback = selectedSuggestions;
        break;
    }
  }

  // Show references modal
  function showReferencesModal(stepNumber) {
    if (window.CEP_RMT.SuggestionsHandler) {
      window.CEP_RMT.SuggestionsHandler.showReferencesModal(stepNumber);
    }
  }

  // Validate a specific step
  function validateStep(stepNumber) {
    const config = STEP_CONFIG[stepNumber];
    if (!config) {
      return false;
    }

    const formState = window.CEP_RMT.FormBase.getFormState();
    const errorMessage = config.validation(formState);

    if (errorMessage) {
      window.CEP_RMT.FormBase.showErrorMessage(errorMessage);
      return false;
    }

    return true;
  }

  // Collect data for a specific step
  function collectStepData(stepNumber) {
    const config = STEP_CONFIG[stepNumber];
    if (!config) {
      return {};
    }

    const formState = window.CEP_RMT.FormBase.getFormState();

    // Collect data based on step
    switch(stepNumber) {
      case 1:
        return {
          recommendation_text: formState.data.recommendation,
          output_result_count: formState.data.outputCount,
          file_name: formState.data.fileName
        };

      case 2:
        return {
          stakeholders_text: formState.data.stakeholders,
          selected_stakeholders: formState.data.selectedStakeholders
        };

      case 3:
        return {
          implementation_stakeholder: formState.data.implementationStakeholder,
          ownership_stakeholder: formState.data.ownershipStakeholder,
          progress_tracker: formState.data.progressTracker,
          feedback_handler: formState.data.feedbackHandler,
          outcome_evaluator: formState.data.outcomeEvaluator
        };

      case 4:
        return {
          governance_text: formState.data.governance,
          selected_governance: formState.data.selectedGovernance
        };

      case 5:
        return {
          challenges_text: formState.data.challenges,
          selected_challenges: formState.data.selectedChallenges
        };

      case 6:
        return {
          coordination_text: formState.data.coordination,
          selected_coordination: formState.data.selectedCoordination
        };

      case 8:
        return {
          implementation_text: formState.data.implementation,
          selected_implementation: formState.data.selectedImplementation
        };

      case 9:
        return {
          outcomes_text: formState.data.outcomes,
          selected_outcomes: formState.data.selectedOutcomes
        };

      case 10:
        return {
          monitoring_text: formState.data.monitoring,
          selected_monitoring: formState.data.selectedMonitoring
        };

      case 11:
        return {
          feedback_text: formState.data.feedback,
          selected_feedback: formState.data.selectedFeedback
        };
    }

    return {};
  }

  // Submit step data
  async function submitStep(stepNumber) {
    if (!validateStep(stepNumber)) {
      return false;
    }

    const config = STEP_CONFIG[stepNumber];

    try {
      // Step 1 has special submission logic
      if (stepNumber === 1) {
        return await submitStep1();
      }

      // Other steps use the suggestions handler
      const inputData = collectStepData(stepNumber);
      const result = await window.CEP_RMT.SuggestionsHandler.submitStepData(
        stepNumber,
        config.name,
        inputData
      );

      return result;
    } catch (error) {
      console.error(`Step ${stepNumber} submission error:`, error);
      return false;
    }
  }

  // Special submission logic for Step 1
  async function submitStep1() {
    const formState = window.CEP_RMT.FormBase.getFormState();

    try {
      // Convert file to base64
      const fileData = await window.CEP_RMT.FormBase.fileToBase64(formState.data.selectedFile);

      const payload = {
        recommendation_text: formState.data.recommendation,
        file_data: fileData,
        file_name: formState.data.fileName,
        output_result_count: formState.data.outputCount,
      };

      const response = await fetch('/recommendation_mapping/api/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload)
      });

      const { result } = await response.json();

      if (result.success) {
        formState.recordId = result.record_id;

        // Start polling for results if processing
        if (result.status === 'running') {
          window.CEP_RMT.FormBase.showSuccessMessage('Processing in progress...');
          pollForResults(result.record_id);
        }

        return result;
      } else {
        throw new Error(result.message || 'Failed to process recommendations');
      }
    } catch (error) {
      window.CEP_RMT.FormBase.showErrorMessage('Error processing recommendations: ' + error.message);
      return false;
    }
  }

  // Poll for processing results (Step 1 specific)
  function pollForResults(recordId) {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/recommendation_mapping/api/suggestions/${recordId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
          },
        });

        const result = await response.json();

        if (result.success && result.status === 'completed') {
          clearInterval(pollInterval);
          if (result.data.data && result.data.data.results) {
            window.CEP_RMT.FormBase.setResultsState(result.data.results);
          }

          window.CEP_RMT.FormBase.showSuccessMessage('Processing completed successfully!');
        } else if (result.status === 'failed') {
          clearInterval(pollInterval);
          window.CEP_RMT.FormBase.showErrorMessage(
            'Processing failed: ' + (result.data.error_message || 'Unknown error')
          );
        }
      } catch (error) {
        console.error('Polling error:', error);
      }
    }, 5000);
  }

  // Load existing data into a step
  function loadStepData(stepNumber, data) {
    if (!data) return;

    const formState = window.CEP_RMT.FormBase.getFormState();

    // Load data based on step
    switch(stepNumber) {
      case 1:
        if (data.recommendation_text) {
          formState.data.recommendation = data.recommendation_text;
          $('#step1_recommendation_input').val(data.recommendation_text);
        }
        if (data.output_result_count) {
          formState.data.outputCount = data.output_result_count;
          $('#step1_output_count').val(data.output_result_count);
        }
        break;

      case 2:
        if (data.stakeholders_text) {
          formState.data.stakeholders = data.stakeholders_text;
          $('#step2_stakeholders_input').val(data.stakeholders_text);
        }
        if (data.selected_stakeholders) {
          formState.data.selectedStakeholders = data.selected_stakeholders;
          updateCheckboxesFromData(stepNumber, data.selected_stakeholders);
        }
        break;

      case 3:
        if (data.implementation_stakeholder) {
          formState.data.implementationStakeholder = data.implementation_stakeholder;
          $('#step3_implementation_stakeholder').val(data.implementation_stakeholder);
        }
        if (data.ownership_stakeholder) {
          formState.data.ownershipStakeholder = data.ownership_stakeholder;
          $('#step3_ownership_stakeholder').val(data.ownership_stakeholder);
        }
        if (data.progress_tracker) {
          formState.data.progressTracker = data.progress_tracker;
          $('#step3_progress_tracker').val(data.progress_tracker);
        }
        if (data.feedback_handler) {
          formState.data.feedbackHandler = data.feedback_handler;
          $('#step3_feedback_handler').val(data.feedback_handler);
        }
        if (data.outcome_evaluator) {
          formState.data.outcomeEvaluator = data.outcome_evaluator;
          $('#step3_outcome_evaluator').val(data.outcome_evaluator);
        }
        break;

      // Add similar cases for other steps...
      case 4:
        if (data.governance_text) {
          formState.data.governance = data.governance_text;
          $('#step4_governance_input').val(data.governance_text);
        }
        if (data.selected_governance) {
          formState.data.selectedGovernance = data.selected_governance;
          updateCheckboxesFromData(stepNumber, data.selected_governance);
        }
        break;
    }
  }

  // Update checkboxes based on loaded data
  function updateCheckboxesFromData(stepNumber, selectedItems) {
    $(`input[id^="step${stepNumber}_suggestion"]`).each(function() {
      const label = $(this).next('label').text().trim();
      const isSelected = selectedItems.includes(label);
      $(this).prop('checked', isSelected);
    });
  }

  // Create individual step objects for backward compatibility
  function createStepObject(stepNumber) {
    const config = STEP_CONFIG[stepNumber];
    if (!config) {
      return null;
    }

    return {
      init: () => initStep(stepNumber),
      validate: () => validateStep(stepNumber),
      submit: () => submitStep(stepNumber),
      loadData: (data) => loadStepData(stepNumber, data),
      getStepNumber: () => stepNumber,
      getStepName: () => config.name
    };
  }

  // Public API
  return {
    // Initialize all steps or a specific step
    init: (stepNumber) => {
      if (stepNumber) {
        initStep(stepNumber);
      } else {
        // Initialize all configured steps
        Object.keys(STEP_CONFIG).forEach(step => {
          initStep(parseInt(step));
        });
      }
    },

    // Get a specific step object for backward compatibility
    getStep: (stepNumber) => createStepObject(stepNumber),

    // Direct access to step functions
    initStep,
    validateStep,
    submitStep,
    loadStepData,
    collectStepData,

    // Utility functions
    getStepConfig: (stepNumber) => STEP_CONFIG[stepNumber],
    getAllSteps: () => Object.keys(STEP_CONFIG).map(step => parseInt(step))
  };
})();

// Create individual step objects for backward compatibility
Object.keys(window.CEP_RMT.Steps.Manager.getAllSteps ?
  window.CEP_RMT.Steps.Manager.getAllSteps().reduce((acc, step) => {
    acc[step] = true;
    return acc;
  }, {}) :
  {1: true, 2: true, 3: true, 4: true, 5: true, 6: true, 8: true, 9: true, 10: true, 11: true}
).forEach(stepNumber => {
  const step = parseInt(stepNumber);
  window.CEP_RMT.Steps[`Step${step}`] = window.CEP_RMT.Steps.Manager.getStep(step);
});
