// Step 4: Governance Rules Component
window.CEP_RMT = window.CEP_RMT || {};
window.CEP_RMT.Steps = window.CEP_RMT.Steps || {};

window.CEP_RMT.Steps.Step4 = (function() {
  'use strict';

  const STEP_NUMBER = 4;
  const STEP_NAME = 'governance_rules';

  // Initialize step-specific functionality
  function init() {
    bindEvents();
    window.CEP_RMT.SuggestionsHandler.populateAutomatedSuggestions(STEP_NUMBER);
  }

  // Bind step-specific events
  function bindEvents() {
    // Input field event
    $('#step4_governance_input').off('input').on('input', function () {
      const formState = window.CEP_RMT.FormBase.getFormState();
      formState.data.governance = $(this).val();
    });

    // Suggestion checkboxes
    $(document).off('change', `input[id^="step${STEP_NUMBER}_suggestion"]`)
               .on('change', `input[id^="step${STEP_NUMBER}_suggestion"]`, function () {
      window.CEP_RMT.SuggestionsHandler.updateSelectedSuggestions(STEP_NUMBER);
    });

    // Reference link
    $('#step4_reference_link').off('click').on('click', function (e) {
      e.preventDefault();
      window.CEP_RMT.SuggestionsHandler.showReferencesModal(STEP_NUMBER);
    });
  }

  // Validate step data
  function validate() {
    const formState = window.CEP_RMT.FormBase.getFormState();
    
    if (!formState.data.governance.trim()) {
      window.CEP_RMT.FormBase.showErrorMessage('Please enter governance rules information');
      return false;
    }
    
    return true;
  }

  // Collect step data for API submission
  function collectData() {
    const formState = window.CEP_RMT.FormBase.getFormState();
    
    return {
      governance_text: formState.data.governance,
      selected_governance: formState.data.selectedGovernance
    };
  }

  // Submit step data
  async function submit() {
    if (!validate()) {
      return false;
    }

    try {
      const inputData = collectData();
      const result = await window.CEP_RMT.SuggestionsHandler.submitStepData(
        STEP_NUMBER, 
        STEP_NAME, 
        inputData
      );
      
      return result;
    } catch (error) {
      console.error('Step 4 submission error:', error);
      return false;
    }
  }

  // Load existing data into the form
  function loadData(data) {
    if (data) {
      const formState = window.CEP_RMT.FormBase.getFormState();
      
      if (data.governance_text) {
        formState.data.governance = data.governance_text;
        $('#step4_governance_input').val(data.governance_text);
      }
      
      if (data.selected_governance) {
        formState.data.selectedGovernance = data.selected_governance;
        updateCheckboxesFromData(data.selected_governance);
      }
    }
  }

  // Update checkboxes based on loaded data
  function updateCheckboxesFromData(selectedItems) {
    $(`input[id^="step${STEP_NUMBER}_suggestion"]`).each(function() {
      const label = $(this).next('label').text().trim();
      const isSelected = selectedItems.includes(label);
      $(this).prop('checked', isSelected);
    });
  }

  // Public API
  return {
    init,
    validate,
    submit,
    loadData,
    getStepNumber: () => STEP_NUMBER,
    getStepName: () => STEP_NAME
  };
})();
