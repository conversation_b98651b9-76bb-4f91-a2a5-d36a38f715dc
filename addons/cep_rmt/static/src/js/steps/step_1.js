// Step 1: Recommendation Input Component
window.CEP_RMT = window.CEP_RMT || {};
window.CEP_RMT.Steps = window.CEP_RMT.Steps || {};

window.CEP_RMT.Steps.Step1 = (function() {
  'use strict';

  const STEP_NUMBER = 1;
  const STEP_NAME = 'recommendation';

  // Initialize step-specific functionality
  function init() {
    bindEvents();
  }

  // Bind step-specific events
  function bindEvents() {
    // Recommendation input
    $('#step1_recommendation_input').off('input').on('input', function () {
      const formState = window.CEP_RMT.FormBase.getFormState();
      formState.data.recommendation = $(this).val();
    });

    // Output count
    $('#step1_output_count').off('change').on('change', function () {
      const formState = window.CEP_RMT.FormBase.getFormState();
      formState.data.outputCount = parseInt($(this).val());
    });

    // File upload events
    $('#step1_file_upload').off('change').on('change', function () {
      var file = this.files[0];
      if (file) {
        processFile(file);
      }
    });

    // Drag and drop events
    $('#drop-zone').off('dragover').on('dragover', function (ev) {
      ev.preventDefault();
      ev.stopPropagation();
      ev.originalEvent.dataTransfer.dropEffect = 'copy';
      $(this).addClass('drag-over');
    });

    $('#drop-zone').off('dragleave').on('dragleave', function (ev) {
      ev.preventDefault();
      ev.stopPropagation();
      $(this).removeClass('drag-over');
    });

    $('#drop-zone').off('drop').on('drop', function (ev) {
      ev.preventDefault();
      ev.stopPropagation();
      $(this).removeClass('drag-over');

      var files = ev.originalEvent.dataTransfer.files;
      if (files.length > 0) {
        processFile(files[0]);
      }
    });

    $('#drop-zone').off('click').on('click', function () {
      $('#step1_file_upload').click();
    });
  }

  // Process uploaded file
  function processFile(file) {
    const formState = window.CEP_RMT.FormBase.getFormState();
    
    // Validate file type
    var allowedTypes = ['application/pdf'];
    
    if (!allowedTypes.includes(file.type)) {
      window.CEP_RMT.FormBase.showErrorMessage('Please upload a PDF or Word document');
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      window.CEP_RMT.FormBase.showErrorMessage('File size must be less than 10MB');
      return;
    }

    // Store file in form state
    formState.data.selectedFile = file;
    formState.data.fileName = file.name;

    // Update UI
    $('#file-name').text(file.name);
    $('#file-size').text((file.size / 1024 / 1024).toFixed(2) + ' MB');
    $('#file-info').removeClass('d-none');
    $('#drop-zone-text').addClass('d-none');
  }

  // Validate step data
  function validate() {
    const formState = window.CEP_RMT.FormBase.getFormState();
    
    if (!formState.data.recommendation.trim()) {
      window.CEP_RMT.FormBase.showErrorMessage('Recommendation text is required');
      return false;
    }

    if (!formState.data.selectedFile) {
      window.CEP_RMT.FormBase.showErrorMessage('NAP file is required');
      return false;
    }
    
    return true;
  }

  
  async function submit() {
    if (!validate()) {
      return false;
    }

    try {
      const formState = window.CEP_RMT.FormBase.getFormState();
      
      // Convert file to base64
      const fileData = await window.CEP_RMT.FormBase.fileToBase64(formState.data.selectedFile);
      
      const payload = {
        recommendation_text: formState.data.recommendation,
        file_data: fileData,
        file_name: formState.data.fileName,
        output_result_count: formState.data.outputCount,
      };

      const response = await fetch('/recommendation_mapping/api/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload)
      });

      const { result } = await response.json();
      
      if (result.success) {
        formState.recordId = result.record_id;
        
        // Start polling for results if processing
        if (result.status === 'running') {
          window.CEP_RMT.FormBase.showSuccessMessage('Processing in progress...');
          pollForResults(result.record_id);
        }
        
        return result;
      } else {
        throw new Error(result.message || 'Failed to process recommendations');
      }
    } catch (error) {
      window.CEP_RMT.FormBase.showErrorMessage('Error processing recommendations: ' + error.message);
      return false;
    }
  }

  // Poll for processing results
  function pollForResults(recordId) {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/recommendation_mapping/api/suggestions/${recordId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
          },
        });

        const result = await response.json();

        if (result.success && result.status === 'completed') {
            clearInterval(pollInterval);
            if (result.data.data && result.data.data.results) {
              window.CEP_RMT.FormBase.setResultsState(result.data.results);
            }
            
            window.CEP_RMT.FormBase.showSuccessMessage('Processing completed successfully!');
          } else if (result.status === 'failed') {
            clearInterval(pollInterval);
            window.CEP_RMT.FormBase.showErrorMessage(
              'Processing failed: ' + (result.data.error_message || 'Unknown error')
            );
          }

        
      } catch (error) {
        console.error('Polling error:', error);
      }
    }, 5000);
  }

  // Load existing data into the form
  function loadData(data) {
    if (data) {
      const formState = window.CEP_RMT.FormBase.getFormState();
      
      if (data.recommendation_text) {
        formState.data.recommendation = data.recommendation_text;
        $('#step1_recommendation_input').val(data.recommendation_text);
      }
      
      if (data.output_result_count) {
        formState.data.outputCount = data.output_result_count;
        $('#step1_output_count').val(data.output_result_count);
      }
    }
  }

  // Public API
  return {
    init,
    validate,
    submit,
    loadData,
    getStepNumber: () => STEP_NUMBER,
    getStepName: () => STEP_NAME
  };
})();
