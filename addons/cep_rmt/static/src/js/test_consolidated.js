// Test file for the consolidated steps component
// This file can be used to verify that the consolidated steps work correctly

(function() {
  'use strict';

  // Test function to verify the consolidated steps
  function testConsolidatedSteps() {
    console.log('Testing Consolidated Steps Component...');

    // Check if the manager exists
    if (!window.CEP_RMT || !window.CEP_RMT.Steps || !window.CEP_RMT.Steps.Manager) {
      console.error('CEP_RMT.Steps.Manager not found');
      return false;
    }

    const manager = window.CEP_RMT.Steps.Manager;

    // Test 1: Check if all expected steps are configured
    const expectedSteps = [1, 2, 3, 4, 5, 6, 8, 9, 10, 11];
    const configuredSteps = manager.getAllSteps();
    
    console.log('Expected steps:', expectedSteps);
    console.log('Configured steps:', configuredSteps);

    const missingSteps = expectedSteps.filter(step => !configuredSteps.includes(step));
    if (missingSteps.length > 0) {
      console.error('Missing steps:', missingSteps);
      return false;
    }

    // Test 2: Check if step configurations are valid
    expectedSteps.forEach(stepNumber => {
      const config = manager.getStepConfig(stepNumber);
      if (!config) {
        console.error(`Configuration missing for step ${stepNumber}`);
        return false;
      }

      // Check required properties
      const requiredProps = ['name', 'fields', 'hasFileUpload', 'hasSuggestions', 'validation'];
      const missingProps = requiredProps.filter(prop => !(prop in config));
      if (missingProps.length > 0) {
        console.error(`Step ${stepNumber} missing properties:`, missingProps);
        return false;
      }

      console.log(`Step ${stepNumber} (${config.name}): OK`);
    });

    // Test 3: Check if individual step objects are created for backward compatibility
    expectedSteps.forEach(stepNumber => {
      const stepObj = window.CEP_RMT.Steps[`Step${stepNumber}`];
      if (!stepObj) {
        console.error(`Step${stepNumber} object not found`);
        return false;
      }

      // Check required methods
      const requiredMethods = ['init', 'validate', 'submit', 'loadData', 'getStepNumber', 'getStepName'];
      const missingMethods = requiredMethods.filter(method => typeof stepObj[method] !== 'function');
      if (missingMethods.length > 0) {
        console.error(`Step${stepNumber} missing methods:`, missingMethods);
        return false;
      }

      console.log(`Step${stepNumber} object: OK`);
    });

    // Test 4: Test step object methods
    const step1 = window.CEP_RMT.Steps.Step1;
    if (step1.getStepNumber() !== 1) {
      console.error('Step1 getStepNumber() failed');
      return false;
    }

    if (step1.getStepName() !== 'recommendation') {
      console.error('Step1 getStepName() failed');
      return false;
    }

    console.log('Step object methods: OK');

    // Test 5: Test manager methods
    try {
      // These should not throw errors
      manager.getStep(1);
      manager.getStepConfig(2);
      console.log('Manager methods: OK');
    } catch (error) {
      console.error('Manager methods failed:', error);
      return false;
    }

    console.log('✅ All tests passed! Consolidated steps component is working correctly.');
    return true;
  }

  // Test function for form state integration
  function testFormStateIntegration() {
    console.log('Testing Form State Integration...');

    if (!window.CEP_RMT || !window.CEP_RMT.FormBase) {
      console.error('CEP_RMT.FormBase not found');
      return false;
    }

    const formState = window.CEP_RMT.FormBase.getFormState();
    if (!formState || !formState.data) {
      console.error('Form state not properly initialized');
      return false;
    }

    console.log('Form state structure:', Object.keys(formState.data));
    console.log('✅ Form state integration test passed!');
    return true;
  }

  // Test function for suggestions handler integration
  function testSuggestionsIntegration() {
    console.log('Testing Suggestions Handler Integration...');

    if (!window.CEP_RMT || !window.CEP_RMT.SuggestionsHandler) {
      console.warn('CEP_RMT.SuggestionsHandler not found - this is optional');
      return true;
    }

    const handler = window.CEP_RMT.SuggestionsHandler;
    const requiredMethods = ['populateAutomatedSuggestions', 'updateSelectedSuggestions', 'submitStepData'];
    
    const missingMethods = requiredMethods.filter(method => typeof handler[method] !== 'function');
    if (missingMethods.length > 0) {
      console.error('SuggestionsHandler missing methods:', missingMethods);
      return false;
    }

    console.log('✅ Suggestions handler integration test passed!');
    return true;
  }

  // Run all tests
  function runAllTests() {
    console.log('=== Running Consolidated Steps Tests ===');
    
    const tests = [
      testConsolidatedSteps,
      testFormStateIntegration,
      testSuggestionsIntegration
    ];

    let allPassed = true;
    tests.forEach((test, index) => {
      console.log(`\n--- Test ${index + 1}: ${test.name} ---`);
      const result = test();
      if (!result) {
        allPassed = false;
      }
    });

    console.log('\n=== Test Results ===');
    if (allPassed) {
      console.log('🎉 All tests passed! The consolidated steps component is ready to use.');
    } else {
      console.log('❌ Some tests failed. Please check the errors above.');
    }

    return allPassed;
  }

  // Export test functions for manual testing
  window.CEP_RMT = window.CEP_RMT || {};
  window.CEP_RMT.Tests = {
    runAllTests,
    testConsolidatedSteps,
    testFormStateIntegration,
    testSuggestionsIntegration
  };

  // Auto-run tests when this file is loaded (optional)
  // Uncomment the line below to run tests automatically
  // setTimeout(runAllTests, 1000);

})();
