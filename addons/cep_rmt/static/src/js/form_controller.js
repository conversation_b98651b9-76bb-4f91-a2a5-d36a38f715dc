// Main Form Controller for CEP RMT
$(document).ready(function () {
  'use strict';

  // Form Controller
  const FormController = (function() {
    
   
    const stepComponents = {};
    
    // Initialize the multi-step form
    function initializeForm() {
      var $container = $('#recommendation-form-container');
      if ($container.length === 0) {
        return; // Exit if container not found
      }

      // Register step components
      registerStepComponents();
      
      // Bind common events
      bindCommonEvents();
      
      // Initialize form state
      initializeFormState();
      
      // Update display
      updateStepDisplay();
      updateNavigationButtons();
    }

    // Register all step components
    function registerStepComponents() {
      // Register steps that have components
      if (window.CEP_RMT.Steps.Step1) {
        stepComponents[1] = window.CEP_RMT.Steps.Step1;
      }
      if (window.CEP_RMT.Steps.Step2) {
        stepComponents[2] = window.CEP_RMT.Steps.Step2;
      }
      if (window.CEP_RMT.Steps.Step3) {
        stepComponents[3] = window.CEP_RMT.Steps.Step3;
      }
      if (window.CEP_RMT.Steps.Step4) {
        stepComponents[4] = window.CEP_RMT.Steps.Step4;
      }
      if (window.CEP_RMT.Steps.Step5) {
        stepComponents[5] = window.CEP_RMT.Steps.Step5;
      }
      if (window.CEP_RMT.Steps.Step6) {
        stepComponents[6] = window.CEP_RMT.Steps.Step6;
      }
      if (window.CEP_RMT.Steps.Step8) {
        stepComponents[8] = window.CEP_RMT.Steps.Step8;
      }
      if (window.CEP_RMT.Steps.Step9) {
        stepComponents[9] = window.CEP_RMT.Steps.Step9;
      }
      if (window.CEP_RMT.Steps.Step10) {
        stepComponents[10] = window.CEP_RMT.Steps.Step10;
      }
      if (window.CEP_RMT.Steps.Step11) {
        stepComponents[11] = window.CEP_RMT.Steps.Step11;
      }
    }

    // Bind common navigation and form events
    function bindCommonEvents() {
      // Navigation buttons
      $('#next-btn').on('click', function () {
        nextStep();
      });

      $('#prev-btn').on('click', function () {
        previousStep();
      });

      // Common reference link handler (fallback for steps without specific components)
      $(document).on('click', 'a.text-primary', function (e) {
        const $link = $(this);
        
        if ($link.text().trim() === 'Reference from NAP') {
          e.preventDefault();
          const formState = window.CEP_RMT.FormBase.getFormState();
          window.CEP_RMT.SuggestionsHandler.showReferencesModal(formState.currentStep);
        }
      });
    }

    // Initialize form state from existing HTML
    function initializeFormState() {
      const formState = window.CEP_RMT.FormBase.getFormState();
      
      // Set initial output count
      formState.data.outputCount = parseInt($('#step1_output_count').val()) || 3;
    }

    // Navigate to next step
    async function nextStep() {
      const formState = window.CEP_RMT.FormBase.getFormState();
      
      // Validate and submit current step if it has a component
      if (stepComponents[formState.currentStep]) {
        const stepComponent = stepComponents[formState.currentStep];
        
        // Validate current step
        if (!stepComponent.validate()) {
          return;
        }
        
        // Submit step data for steps 2-11
        if (formState.currentStep >= 2 && formState.currentStep <= 11) {
          try {
            const result = await stepComponent.submit();
            if (!result) {
              return; // Don't proceed if submission failed
            }
          } catch (error) {
            console.error('Step submission failed:', error);
            return;
          }
        }
      }

      // Move to next step
      if (formState.currentStep < formState.totalSteps) {
        formState.currentStep++;
        updateStepDisplay();
        updateNavigationButtons();
        
        // Initialize the new step if it has a component
        if (stepComponents[formState.currentStep]) {
          stepComponents[formState.currentStep].init();
        }
      }
    }

    // Navigate to previous step
    function previousStep() {
      const formState = window.CEP_RMT.FormBase.getFormState();
      
      if (formState.currentStep > 1) {
        formState.currentStep--;
        updateStepDisplay();
        updateNavigationButtons();
        
        // Initialize the step if it has a component
        if (stepComponents[formState.currentStep]) {
          stepComponents[formState.currentStep].init();
        }
      }
    }

    // Update step display and progress
    function updateStepDisplay() {
      const formState = window.CEP_RMT.FormBase.getFormState();
      
      // Hide all steps
      $('.form-step').addClass('d-none');

      // Show current step
      $('#step-' + formState.currentStep).removeClass('d-none');

      // Update progress steps
      $('.step-line').each(function (index) {
        var stepNumber = index + 1;
        if (stepNumber <= formState.currentStep) {
          $(this).removeClass('step-line-inactive').addClass('step-line-active');
        } else {
          $(this).removeClass('step-line-active').addClass('step-line-inactive');
        }
      });

      // Initialize current step component if it exists
      if (stepComponents[formState.currentStep]) {
        stepComponents[formState.currentStep].init();
      }
    }

    // Update navigation buttons
    function updateNavigationButtons() {
      const formState = window.CEP_RMT.FormBase.getFormState();
      var $prevBtn = $('#prev-btn');
      var $nextBtn = $('#next-btn');

      // Show/hide previous button
      if (formState.currentStep === 1) {
        $prevBtn.addClass('d-none');
      } else {
        $prevBtn.removeClass('d-none');
      }

      // Update next button text
      if (formState.currentStep === formState.totalSteps) {
        $nextBtn.text('Submit');
      } else {
        $nextBtn.text('Next');
      }
    }

    
    return {
      init: initializeForm,
    };
  })();


  FormController.init();

});
