from odoo import http
from odoo.http import request
import json
import base64
import logging

_logger = logging.getLogger(__name__)


def isAuthenticate():
    """Check if user is authenticated"""
    # For JSON endpoints, check if user is not the public user
    try:
        # Check if user has a real login (not public user)
        return (
            request.env.user.login
            and request.env.user.login != "public"
            and request.env.user.id != 4
        )
    except:
        # Fallback: assume not authenticated
        return False


def isRecordOwner(record, user_id=None):
    """Check if the current user owns the record"""
    if user_id is None:
        user_id = request.env.user.id
    return record.user_id.id == user_id if record else False


class RecommendationMappingController(http.Controller):

    _root_url = "/recommendation_mapping"

    @http.route(f"{_root_url}", type="http", auth="user", website=True)
    def recommendation_mapping_page(self, **kwargs):
        """Render the recommendation mapping tool page"""
        # Check authentication
        if not isAuthenticate():
            return request.redirect("/web/login")

        return request.render("cep_rmt.recommendation_mapping_page")

    @http.route(
        f"{_root_url}/api/process",
        type="json",
        auth="user",
        methods=["POST"],
        csrf=False,
    )
    def process_recommendations_api(self, **kwargs):
        """
        API endpoint to process recommendations with RabbitMQ integration
        Requires authentication and handles user ownership
        """
        try:

            data = json.loads(request.httprequest.data)

            recommendation_text = data.get("recommendation_text", "")
            file_data = data.get("file_data", "")
            file_name = data.get("file_name", "")
            output_result_count = int(data.get("output_result_count", 3))

            # Validate inputs
            if not recommendation_text.strip():
                return {
                    "success": False,
                    "error": "Recommendation text is required",
                    "error_code": "MISSING_RECOMMENDATION",
                }

            if not file_data:
                return {
                    "success": False,
                    "error": "Attachment file is required",
                    "error_code": "MISSING_FILE",
                }

            if output_result_count < 1 or output_result_count > 10:
                return {
                    "success": False,
                    "error": "Output result count must be between 1 and 10",
                    "error_code": "INVALID_COUNT",
                }

            # Create recommendation record with user ownership
            recommendation_model = request.env["cep_rmt.recommendation_data"]

            data = {
                "title": f"{recommendation_text[:50]}",
                "step": "recommendation",
                "status": "running",
                "user_id": request.env.user.id,
                "recommendation": recommendation_text,  # Set the JSON data for the specific step field
            }

            record = recommendation_model.create(data)

            # Handle file attachment
            attachment_id = None
            if file_data and file_name:
                try:
                    # Validate file type (PDF only for NAP files)
                    if not file_name.lower().endswith(".pdf"):
                        return {
                            "success": False,
                            "error": "Only PDF files are allowed for NAP documents",
                            "error_code": "INVALID_FILE_TYPE",
                        }

                    # Validate file size (max 10MB)
                    try:
                        file_content = base64.b64decode(file_data)
                        file_size = len(file_content)
                        max_size = 10 * 1024 * 1024  # 10MB

                        if file_size > max_size:
                            return {
                                "success": False,
                                "error": "File size must be less than 10MB",
                                "error_code": "FILE_TOO_LARGE",
                            }
                    except Exception:
                        return {
                            "success": False,
                            "error": "Invalid file data",
                            "error_code": "INVALID_FILE_DATA",
                        }

                    # Create attachment
                    attachment = (
                        request.env["ir.attachment"]
                        .sudo()
                        .create(
                            {
                                "name": file_name,
                                "datas": file_data,
                                "res_model": "cep_rmt.recommendation_data",
                                "res_id": record.id,
                                "type": "binary",
                                "mimetype": "application/pdf",
                                "public": True,
                            }
                        )
                    )

                    attachment_id = attachment.id

                except Exception as e:
                    _logger.error(f"Error creating attachment: {str(e)}")
                    return {
                        "success": False,
                        "error": "Failed to process attachment",
                        "error_code": "ATTACHMENT_ERROR",
                    }

            # Send RabbitMQ message
            try:
                rabbitmq_server = (
                    request.env["rabbitmq.server"].sudo().search([], limit=1)
                )

                if not rabbitmq_server:
                    return {
                        "success": False,
                        "error": "Message queue service not available",
                        "error_code": "QUEUE_NOT_FOUND",
                    }

                # base_url = "http://host.docker.internal:8069"
                base_url = (
                    request.env["ir.config_parameter"].sudo().get_param("web.base.url")
                )

                # Prepare message payload
                message_payload = {
                    "event": "PROCESS_RECOMMENDATIONS",
                    "project_id": record.id,
                    "user_id": request.env.user.id,
                    "user_name": request.env.user.name,
                    "recommendation_text": recommendation_text,
                    "output_result_count": output_result_count,
                    "file_name": file_name,
                    "attachment": f"{base_url}/web/content/{attachment_id}?download=true",
                }

                # Publish message
                rabbitmq_server.publish_message(message_payload, "RMT_SERVICE")

                return {
                    "success": True,
                    "message": "Recommendation processing started successfully",
                    "record_id": record.id,
                    "step": record.step_number,
                    "status": "running",
                }

            except Exception as e:
                # Update record status to failed
                record.write(
                    {
                        "status": "failed",
                        "error_message": f"Failed to send processing request",
                    }
                )
                return {
                    "success": False,
                    "error": "Failed to start processing",
                    "error_code": "QUEUE_ERROR",
                }

        except Exception as e:
            return {
                "success": False,
                "error": "An unexpected error occurred",
                "error_code": "INTERNAL_ERROR",
            }

    @http.route(
        f"{_root_url}/api/suggestions/<string:id>",
        type="http",
        auth="user",
        methods=["GET"],
        csrf=False,
    )
    def get_suggestions_data_api(self, id):
        """API endpoint to get recommendation data"""
        try:
            if not id:
                return request.make_response(
                    json.dumps(
                        {
                            "success": False,
                            "error": "Record ID is required",
                            "error_code": "MISSING_RECORD_ID",
                        }
                    ),
                    headers=[("Content-Type", "application/json")],
                    status=400,
                )

            record = request.env["cep_rmt.recommendation_data"].browse(int(id)).sudo()

            if not record.exists():
                return request.make_response(
                    json.dumps(
                        {
                            "success": False,
                            "error": "Record not found",
                            "error_code": "RECORD_NOT_FOUND",
                        }
                    ),
                    headers=[("Content-Type", "application/json")],
                    status=404,
                )

            # Check if user owns the record
            if not isRecordOwner(record):
                return request.make_response(
                    json.dumps(
                        {
                            "success": False,
                            "error": "Access denied",
                            "error_code": "ACCESS_DENIED",
                        }
                    ),
                    headers=[("Content-Type", "application/json")],
                    status=403,
                )

            return request.make_response(
                json.dumps(
                    {
                        "success": True,
                        "status": record.status,
                        "step": record.step_number,
                        "data": record.suggestions,
                    }
                ),
                headers=[("Content-Type", "application/json")],
            )

        except Exception as e:
            _logger.error(f"Error in get_suggestions_data_api: {str(e)}")
            return request.make_response(
                json.dumps(
                    {
                        "success": False,
                        "error": "An unexpected error occurred",
                        "error_code": "INTERNAL_ERROR",
                    }
                ),
                headers=[("Content-Type", "application/json")],
                status=500,
            )

    @http.route(
        f"{_root_url}/api/steps_data/<string:id>",
        type="http",
        auth="user",
        methods=["GET"],
        csrf=False,
    )
    def get_steps_data_api(self, id):
        """API endpoint to get steps data"""
        try:
            if not id:
                return request.make_response(
                    json.dumps(
                        {
                            "success": False,
                            "error": "Record ID is required",
                            "error_code": "MISSING_RECORD_ID",
                        }
                    ),
                    headers=[("Content-Type", "application/json")],
                    status=400,
                )

            record = request.env["cep_rmt.recommendation_data"].browse(int(id)).sudo()

            if not record.exists():
                return request.make_response(
                    json.dumps(
                        {
                            "success": False,
                            "error": "Record not found",
                            "error_code": "RECORD_NOT_FOUND",
                        }
                    ),
                    headers=[("Content-Type", "application/json")],
                    status=404,
                )

            # Check if user owns the record
            if not isRecordOwner(record):
                return request.make_response(
                    json.dumps(
                        {
                            "success": False,
                            "error": "Access denied",
                            "error_code": "ACCESS_DENIED",
                        }
                    ),
                    headers=[("Content-Type", "application/json")],
                    status=403,
                )

            return request.make_response(
                json.dumps(
                    {
                        "success": True,
                        "data": {
                            "step": record.step_number,
                            "recommendation": record.recommendation,
                            "stakeholders_involved": record.stakeholders_involved,
                            "stakeholders": record.stakeholders,
                            "governance_rules": record.governance_rules,
                            "identified_challenges": record.identified_challenges,
                            "coordination_plan": record.coordination_plan,
                            "team_info": record.team_info,
                            "implementation_steps": record.implementation_steps,
                            "expected_outcomes": record.expected_outcomes,
                            "monitoring_indicators": record.monitoring_indicators,
                            "feedback_mechanisms": record.feedback_mechanisms,
                            "supporting_docs": record.supporting_docs,
                            "suggestions": record.suggestions,
                        },
                    }
                ),
                headers=[("Content-Type", "application/json")],
            )

        except Exception as e:
            _logger.error(f"Error in get_steps_data_api: {str(e)}")
            return request.make_response(
                json.dumps(
                    {
                        "success": False,
                        "error": "An unexpected error occurred",
                        "error_code": "INTERNAL_ERROR",
                    }
                ),
                headers=[("Content-Type", "application/json")],
                status=500,
            )

    @http.route(
        "/recommendation-mapping/api/step-data",
        type="json",
        auth="user",
        methods=["POST"],
        csrf=False,
    )
    def submit_step_data_api(self, **kwargs):
        """
        Common API endpoint for steps 2-11 data submission
        """
        try:
            data = json.loads(request.httprequest.data)

            step_number = data.get("step_number")
            step_name = data.get("step_name")
            input_data = data.get("input_data", {})
            record_id = data.get("record_id")

            # Validate inputs
            if not step_number or not step_name:
                return {
                    "success": False,
                    "error": "Step number and step name are required",
                    "error_code": "MISSING_STEP_INFO",
                }

            if not record_id:
                return {
                    "success": False,
                    "error": "Record ID is required",
                    "error_code": "MISSING_RECORD_ID",
                }

            # Get the record
            record = request.env["cep_rmt.recommendation_data"].browse(int(record_id))

            if not record.exists():
                return {
                    "success": False,
                    "error": "Record not found",
                    "error_code": "RECORD_NOT_FOUND",
                }

            # Check if user owns the record
            if not isRecordOwner(record):
                return {
                    "success": False,
                    "error": "Access denied",
                    "error_code": "ACCESS_DENIED",
                }

            # Update the record with step data
            update_data = {
                "step": step_name,
            }

            # Map step data to the appropriate field
            step_field_mapping = {
                "stakeholders_involved": "stakeholders_involved",
                "stakeholders": "stakeholders",
                "governance_rules": "governance_rules",
                "identified_challenges": "identified_challenges",
                "coordination_plan": "coordination_plan",
                "implementation_steps": "implementation_steps",
                "expected_outcomes": "expected_outcomes",
                "monitoring_indicators": "monitoring_indicators",
                "feedback_mechanisms": "feedback_mechanisms",
            }

            if step_name in step_field_mapping:
                field_name = step_field_mapping[step_name]
                update_data[field_name] = input_data

            # Update the record
            record.write(update_data)

            return {
                "success": True,
                "message": f"Step {step_number} data saved successfully",
                "record_id": record.id,
                "step": record.step_number,
            }

        except Exception as e:
            _logger.error(f"Error in submit_step_data_api: {str(e)}")
            return {
                "success": False,
                "error": "An unexpected error occurred",
                "error_code": "INTERNAL_ERROR",
            }

    @http.route(
        "/recommendation-mapping/api/process-recommendations",
        type="json",
        auth="user",
        methods=["POST"],
        csrf=False,
    )
    def process_recommendations_new_api(self, **kwargs):
        """
        New API endpoint for step 1 recommendation processing
        """
        # This is essentially the same as the existing process_recommendations_api
        # but with the new endpoint path expected by the component
        return self.process_recommendations_api(**kwargs)

    @http.route(
        "/recommendation-mapping/api/user-data",
        type="json",
        auth="user",
        methods=["GET"],
        csrf=False,
    )
    def get_user_data_api(self, **kwargs):
        """
        API endpoint to get existing user data for form initialization
        """
        try:
            # Get the most recent record for the current user
            record = request.env["cep_rmt.recommendation_data"].search(
                [("user_id", "=", request.env.user.id)],
                order="create_date desc",
                limit=1,
            )

            if not record:
                return {
                    "success": True,
                    "data": None,
                    "message": "No existing data found",
                }

            # Return the record data
            return {
                "success": True,
                "data": {
                    "recommendation": record.recommendation,
                    "stakeholders_involved": record.stakeholders_involved,
                    "stakeholders": record.stakeholders,
                    "governance_rules": record.governance_rules,
                    "identified_challenges": record.identified_challenges,
                    "coordination_plan": record.coordination_plan,
                    "team_info": record.team_info,
                    "implementation_steps": record.implementation_steps,
                    "expected_outcomes": record.expected_outcomes,
                    "monitoring_indicators": record.monitoring_indicators,
                    "feedback_mechanisms": record.feedback_mechanisms,
                    "supporting_docs": record.supporting_docs,
                },
                "record_id": record.id,
                "status": record.status,
            }

        except Exception as e:
            _logger.error(f"Error in get_user_data_api: {str(e)}")
            return {
                "success": False,
                "error": "An unexpected error occurred",
                "error_code": "INTERNAL_ERROR",
            }

    @http.route(
        "/recommendation-mapping/api/poll-results/<int:record_id>",
        type="json",
        auth="user",
        methods=["GET"],
        csrf=False,
    )
    def poll_results_api(self, record_id, **kwargs):
        """
        API endpoint to poll for processing results
        """
        try:
            record = request.env["cep_rmt.recommendation_data"].browse(record_id)

            if not record.exists():
                return {
                    "success": False,
                    "error": "Record not found",
                    "error_code": "RECORD_NOT_FOUND",
                }

            # Check if user owns the record
            if not isRecordOwner(record):
                return {
                    "success": False,
                    "error": "Access denied",
                    "error_code": "ACCESS_DENIED",
                }

            return {
                "success": True,
                "status": record.status,
                "data": {
                    "data": {"results": record.suggestions},
                    "error_message": record.error_message,
                },
            }

        except Exception as e:
            _logger.error(f"Error in poll_results_api: {str(e)}")
            return {
                "success": False,
                "error": "An unexpected error occurred",
                "error_code": "INTERNAL_ERROR",
            }
