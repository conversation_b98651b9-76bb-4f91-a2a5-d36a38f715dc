# Copyright 2015 <PERSON><PERSON>, S.L.
# License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl.html).

import logging
import requests
from email_validator import EmailSyntaxError, EmailUndeliverableError, validate_email

from odoo import _
from odoo.http import request, route

from odoo.addons.auth_signup.controllers.main import AuthSignupHome

_logger = logging.getLogger(__name__)


class SignupVerifyEmail(AuthSignupHome):
    @route()
    def web_auth_signup(self, *args, **kw):
        if request.params.get("login") and not request.params.get("password"):
            return self.passwordless_signup()
        return super().web_auth_signup(*args, **kw)

    def passwordless_signup(self):

        values = request.params
        
        recaptcha_error = self._verify_recaptcha(values.get("g-recaptcha-response"))
        if recaptcha_error:
            return recaptcha_error
        values.pop('g-recaptcha-response', None)

       
        qcontext = self.get_auth_signup_qcontext()

        try:
            validate_email(values.get("login", ""))
        except EmailSyntaxError as error:
            qcontext["error"] = getattr(
                error,
                "message",
                _("That does not seem to be an email address."),
            )
            return request.render("auth_signup.signup", qcontext)
        except EmailUndeliverableError as error:
            qcontext["error"] = str(error)
            return request.render("auth_signup.signup", qcontext)
        except Exception as error:
            qcontext["error"] = str(error)
            return request.render("auth_signup.signup", qcontext)
        if not values.get("email"):
            values["email"] = values.get("login")

        values.pop("redirect", "")
        values.pop("token", "")

        values["password"] = ""
        sudo_users = request.env["res.users"].with_context(create_user=True).sudo()

        try:
            with request.cr.savepoint():
                sudo_users.signup(values, qcontext.get("token"))
                sudo_users.reset_password(values.get("login"))
        except Exception as error:
            _logger.exception(error)
            if (
                request.env["res.users"]
                .sudo()
                .search([("login", "=", qcontext.get("login"))])
            ):
                qcontext["error"] = _(
                    "Another user is already registered using this email" " address."
                )
            else:
                qcontext["error"] = _(
                    "Something went wrong, please try again later or" " contact us."
                )
            return request.render("auth_signup.signup", qcontext)

        qcontext["message"] = _("Check your email to activate your account!")
        return request.render("auth_signup.reset_password", qcontext)
    
    def _verify_recaptcha(self, recaptcha_response):
        if not recaptcha_response:
            return request.render("auth_signup.signup", {
                'error': _("Please complete the reCAPTCHA validation.")
            })

        secret_key = request.env['ir.config_parameter'].sudo().get_param('cep_auth_captcha_private_key', default='') 
        verify_url = "https://www.google.com/recaptcha/api/siteverify"
        payload = {
            'secret': secret_key,
            'response': recaptcha_response,
            'remoteip': request.httprequest.remote_addr
        }

        try:
            recaptcha_verification = requests.post(verify_url, data=payload)
            recaptcha_result = recaptcha_verification.json()

            if not recaptcha_result.get('success'):
                _logger.warning(f"reCAPTCHA failed: {recaptcha_result.get('error-codes')}")
                return request.render("auth_signup.signup", {
                    'error': _("Invalid reCAPTCHA. Please try again.")
                })
        except requests.exceptions.RequestException as e:
            _logger.error(f"Error verifying reCAPTCHA: {e}")
            return request.render("auth_signup.signup", {
                'error': _("There was an error verifying the reCAPTCHA. Please try again.")
            })
        return None

