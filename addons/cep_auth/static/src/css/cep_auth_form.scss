// Variables
$form-width: 424px;
$form-padding: 56px 40px;
$form-border-radius: 16px;
$form-bg-color: #FFFFFF;
$form-box-shadow-color: rgba(9, 23, 107, 0.12);
$form-box-shadow-hover-color: rgba(9, 23, 107, 0.15);

$input-height: 48px;
$input-border-radius: 8px;
$input-border-color: #ACB7C6;
$input-focus-border-color: #1FCCC6;
$input-focus-box-shadow-color: rgba(31, 204, 198, 0.5);
$input-text-color: #181C32;

$button-height: 56px;
$button-border-radius: 8px;
$button-bg-color: #1FCCC6;
$button-text-color: #FFFFFF;
$button-hover-bg-color: #17A9A3;
$button-active-bg-color: #138782;
$button-focus-box-shadow-color: rgba(31, 204, 198, 0.4);

// Mixins for reusability
@mixin box-shadow($color) {
  box-shadow: 0px 12px 50px 0px $color;
}

@mixin transition($props...) {
  transition: $props 0.3s ease;
}

// Heading Styles
@mixin heading-container() {
  margin-top: 60px;

  .heading {
    font-family: var(--font-sans-serif);
    font-weight: 800;
    font-size: 32px;
    text-align: center;
    color: $button-bg-color;
  }

  a {
    color: $button-bg-color;
    text-decoration: underline;
  }
}

// Login Heading
.login-heading-container {
  @include heading-container();
}

.signup-heading-container {
  @include heading-container();
}

// Form Styles
.form-container {
  width: 100%;
  max-width: $form-width;
  margin: 40px auto 60px;
  padding: $form-padding;
  border-radius: $form-border-radius;
  background-color: $form-bg-color;
  @include box-shadow($form-box-shadow-color);
  @include transition(box-shadow);

  &:hover {
    @include box-shadow($form-box-shadow-hover-color);
  }

  label {
    display: block;
    color: $input-text-color;
    font-size: 18px;
    font-weight: 400;
    margin-bottom: 8px;

    &.form-check-label {
      margin: 2px 4px;
      line-height: 0%;
    }
  }

  input.form-control {
    width: 100%;
    height: $input-height;
    border-radius: $input-border-radius;
    border: 1px solid $input-border-color;
    padding: 0 16px;
    margin-bottom: 24px;
    color: $input-text-color;
    font-size: 18px;
    @include transition(border-color);

    &:hover {
      border-color: $input-focus-border-color;
    }

    &:focus {
      border-color: $input-focus-border-color;
      outline: none;
      box-shadow: 0 0 5px $input-focus-box-shadow-color;
    }
  }

  .reset-password,
  .forgot-password {
    font-weight: 600;
    font-size: 16px;
  }

  .form-check {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
  }

  input.form-check-input {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid $input-border-color;
  }

  button[type="submit"] {
    width: 100%;
    height: $button-height;
    border-radius: $button-border-radius;
    background-color: $button-bg-color;
    color: $button-text-color;
    font-size: 18px;
    font-weight: 700;
    border: none;
    @include transition(background-color, transform);

    &:hover {
      background-color: $button-hover-bg-color;
      transform: translateY(-2px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px $button-focus-box-shadow-color;
    }

    &:active {
      transform: translateY(0);
      box-shadow: none;
      background-color: $button-active-bg-color;
    }
  }

  .g-recaptcha>div {
    width: 100% !important;
  }
}

// Login Form
.custom-login-form {
  @extend .form-container;
}

// Signup Form
.custom-signup-form {
  @extend .form-container;
}

// Reset Password Form 
.oe_reset_password_form {
  @extend .form-container;
  margin-top: 60px;
  margin-bottom: 0px;
}