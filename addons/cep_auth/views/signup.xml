<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="signup_fields" inherit_id="auth_signup.fields">
        <xpath expr="//div[hasclass('field-password')]" position="attributes">
            <attribute name="t-if">only_passwords</attribute>
        </xpath>
        <xpath expr="//div[hasclass('field-confirm_password')]" position="attributes">
            <attribute name="t-if">only_passwords</attribute>
        </xpath>
        <xpath expr="//input[@name='login']" position="attributes">
            <attribute name="type">email</attribute>
        </xpath>
    </template>

    <!-- Recaptcha -->
    <template id="signup" inherit_id="auth_signup.fields" name="Signup Inherit">
        <div class="mb-3 field-confirm_password" position="after">
            <script src="https://www.google.com/recaptcha/api.js?"></script>

            <div class="g-recaptcha" id="g-recaptcha"
                t-att-data-sitekey="request.env['ir.config_parameter'].sudo().get_param('cep_auth_captcha_public_key', default='')">
            </div>
        </div>
    </template>

    <!-- Customize Signup Form -->
    <template id="custom_signup_form" inherit_id="auth_signup.signup">
        <!-- Add Sign In Link + (already have account text) on top -->
        <xpath expr="//form" position="before">
            <div class="signup-heading-container text-center">
                <h2 class="text-center heading">Citizen Registration</h2>
                <p>Already have an account? <a href="/web/login">Sign in</a> to your account</p>
            </div>
        </xpath>

        <!-- Hide Already Have Account text -->
        <xpath expr="//a[@role='button']" position="attributes">
            <attribute name="t-if">False</attribute>
        </xpath>

        <!-- Add classes -->
        <xpath expr="//form" position="attributes">
            <attribute name="class">custom-signup-form</attribute>
        </xpath>
        <xpath expr="//button[@type='submit']" position="attributes">
            <attribute name="class">custom-signup-button btn btn-primary</attribute>
        </xpath>
    </template>
</odoo>