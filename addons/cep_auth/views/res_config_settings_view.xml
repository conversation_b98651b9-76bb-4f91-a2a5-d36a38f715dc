<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Inherit the existing settings view -->
        <record id="view_res_config_settings_inherit_captcha" model="ir.ui.view">
            <field name="name">res.config.settings.inherit.captcha</field>
            <field name="model">res.config.settings</field>
            <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='integration']//div[@id='recaptcha']" position="after">
                    <div class="col-12 col-lg-6 o_setting_box" id="cep_auth_captcha_keys">
                        <div class="o_setting_right_pane">
                            <div class="mt16 row">
                                <h6>CEP Auth reCAPTCHA</h6>
                                <div class="text-muted">
                                    Enter your reCAPTCHA Site Key and Secret Key to enable protection for your Sign up
                                    form.
                                </div>
                            </div>
                            <div class="mt16 row">
                                <label for="cep_auth_captcha_public_key" class="col-3 o_light_label"/>
                                <field name="cep_auth_captcha_public_key"/>
                            </div>
                            <div class="mt16 row">
                                <label for="cep_auth_captcha_private_key" class="col-3 o_light_label"/>
                                <field name="cep_auth_captcha_private_key"/>
                            </div>
                            <div class="content-group mt16">
                                <div class="text-warning">
                                    <strong>Note:</strong> Only Google Recaptcha v2 will work. </div>
                                <a href="https://www.google.com/recaptcha/admin/create" class="oe_link" target="_blank">
                                    <i class="fa fa-arrow-right"/> Generate reCAPTCHA v2 keys </a>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>
    </data>
</odoo>