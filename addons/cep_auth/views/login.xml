<?xml version="1.0" encoding="utf-8"?>
<odoo>

  <!-- Add class to Login Form -->
  <template id="custom_login_form" inherit_id="web.login">
    <xpath expr="//form" position="before">
      <div class="login-heading-container text-center">
        <h2 class="text-center heading">Sign In</h2>
        <p>Don't have an account yet? <a href="/web/signup">Register</a></p>
      </div>
    </xpath>

    <xpath expr="//form" position="attributes">
      <attribute name="class">custom-login-form</attribute>
    </xpath>
    <xpath expr="//button[@type='submit']" position="attributes">
      <attribute name="class">custom-login-button btn btn-primary mt-3</attribute>
    </xpath>
  </template>

  <!-- Reset password and remember me -->
  <template id="auth_signup.login" inherit_id="web.login" name="Sign up - Reset Password">
    <xpath expr="//div[@class='mb-3'][input[@name='password']]" position="after">
      <div class="d-flex justify-content-between align-items-center mt-0">
        <div class="form-check">
          <input class="form-check-input" type="checkbox" name="remember_me" id="remember_me" />
          <label class="form-check-label mb-0" for="remember_me">
            Remember me
          </label>
        </div>
        <a t-if="reset_password_enabled" t-attf-href="/web/reset_password?{{ keep_query() }}"
          class="reset-password">Reset Password</a>
      </div>
    </xpath>
  </template>

</odoo>