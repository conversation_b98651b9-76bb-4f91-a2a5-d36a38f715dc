.s_problem_details .author {
    color: #181C32;
    font-family: 'Nuni<PERSON>';
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
}

.s_problem_details .date {
    color: #181C32;
    font-family: 'Nuni<PERSON>';
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
}

.s_problem_details .location {
    color: #181C32;
    font-family: Nunito;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.s_problem_details .featured-image {
    height: 300px;
    border-radius: 16px;
    background-repeat: no-repeat;
    background-size: cover;
}

.s_problem_details .card {
    border-radius: 16px;
    background: #FFF;
    box-shadow: 0px 12px 40px 0px rgba(9, 23, 107, 0.12);
}

.s_problem_details .card-header {
    border-radius: 16px 16px 0px 0px;
    background: #09176B;

    color: #FFF;
    font-family: 'Nunito';
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.s_problem_details .summary {
    color: #181C32;
    font-family: <PERSON>uni<PERSON>;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.s_problem_details .status {
    color: #181C32;
    font-family: Nunito;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
} 

.s_problem_details .btn-submit-idea {
    border-radius: 8px;
    background: var(--1-fccc-6, #1FCCC6);
    color: #FFF;
    text-align: center;
    font-family: Nunito;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}

.s_problem_ideas .btn-filter {
    border-radius: 5px;
    border: 1px solid #D8E2EF;
}

.s_problem_ideas .card {
    border-radius: 16px;
    background: #FFF;
    box-shadow: 0px 8px 30px 0px rgba(24, 28, 50, 0.16);
}

.s_problem_ideas .card-idea .card-body {
    padding: 1.25rem;
}

.s_problem_ideas .card-idea .link {
    color: rgba(13, 12, 34, 0.90);
    font-family: Nunito;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.s_problem_ideas .pagination .page-item {
    margin-right: 4px;
    margin-left: 4px;

    border-radius: 4px;
    border: 1px solid #D8E2EF;
    background: #FFF;
}

.s_problem_ideas .pagination .page-item.prev, .s_problem_ideas .pagination .page-item.next {
    border-radius: 0;
    border: none;
    background: none;
}

.s_problem_phases .phase {
    color: #FFF;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;

    text-decoration: none;
    margin-right: 30px;
    color: #181C32;
    height: 50px;
    text-align: center;
    background: #D8E2EF;
    position: relative;

    display: flex; 
    flex-flow: column nowrap; 
    justify-content: center;
}

.s_problem_phases .phase::after {
    content: '';
    position: absolute;
    left: 0; bottom: 0; width: 0; height: 0;
    border-left: 25px solid #EEF3FE;
    border-top: 25px solid transparent;
    border-bottom: 25px solid transparent;
}

.s_problem_phases .phase::before {
    content: '';
    position: absolute;
    right: -25px;
    bottom: 0;
    width: 0;
    height: 0;
    border-left: 25px solid #D8E2EF;
    border-top: 25px solid transparent;
    border-bottom: 25px solid transparent;
}

.s_problem_phases .phase.active {
    background-color: #09176B;
    color: #FFF;
}

.s_problem_phases .phase.active::before {
    content: '';
    position: absolute;
    right: -25px;
    bottom: 0;
    width: 0;
    height: 0;
    border-left: 25px solid #09176B;
    border-top: 25px solid transparent;
    border-bottom: 25px solid transparent;
}

.s_problem_phases .phase:first-child::after, .s_problem_phases .phase:last-child::before {
    content: none;
    border:0;
}

.s_problem_phases .phase:last-child {
    margin-right: 0;
}

.s_problem_phases .phase-details {
    border-radius: 4px;
    background: #FFF;
}

.s_problem_phases .phase-name, .s_problem_phases .phase-number {
    color: #181C32;
    font-family: 'Nunito';
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.s_problem_phases .phase-timeline {
    color: #181C32;
    font-family: 'Nunito';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    opacity: 0.8;
}

.s_problem_phases .phase-description {
    color: #181C32;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.s_problem_ideas .dropdown-toggle {
    color: #181C32;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.s_problem_ideas .search.input-group {
    border-radius: 5px;
    border-top-right-radius: 5px !important;
    border-bottom-right-radius: 5px !important;
    border: 1px solid #D8E2EF;
    background: rgba(255, 255, 255, 0.50);
    padding: 2px !important;
}

.s_problem_ideas .search .form-control, .s_problem_ideas .search .input-group-append {
    border-radius: 0px;
    border: none;
    background: rgba(255, 255, 255, 0.50);
}

.s_problem_ideas .pagination .page-item .page-link {
    border-radius: 4px;
    border: 1px solid #D8E2EF;
    background: #FFF;

    color: #181C32;
    text-align: center;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.s_problem_ideas .pagination .page-item.active .page-link {
    background: #C9D6F1;
}

.s_problem_ideas .pagination .page-item.prev .page-link, .s_problem_ideas .pagination .page-item.next .page-link {
    border: 0;
    border-radius: 0;
    background: none;
}

.s_problem_ideas .card-idea .featured-image {
    height: 160px;
    width: 100%;
    border-radius: 16px;
    object-fit: cover;
}

 /* `sm` applies to x-small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {

    .s_problem_phases .phase span {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) { 
    .s_problem_ideas .card-idea .featured-image {
        height: 160px;
        width: 160px;
        border-radius: 16px;
        object-fit: cover;
    }
}
