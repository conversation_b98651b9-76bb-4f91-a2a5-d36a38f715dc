.s_idea_submit .back-btn {
  background-color: #acebe8;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30px;
}

.s_idea_submit .card {
  border-radius: 16px;
  background: #fff;
  box-shadow: 0px 12px 40px 0px rgba(9, 23, 107, 0.12);
}

.s_idea_submit .summary {
  color: #181c32;
  font-family: Nunito;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.s_idea_submit .btn-submit {
  border-radius: 8px;
  background: var(--1-fccc-6, #1fccc6);
  color: #fff;
  text-align: center;
  font-family: Nunito;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.s_idea_submit .dropdown-toggle {
  border-radius: 5px;
  border: 1px solid #d8e2ef;
  background: #fff;

  color: #181c32;
  font-family: 'Nuni<PERSON>';
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.s_idea_submit .form-control {
  background: #fff;
  color: #181c32;
  font-family: 'Nunito';
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.s_idea_submit .upload-image {
  cursor: pointer;
  border-radius: 8px;
  border: 1px dashed #acb7c6;
  background: #fff;
  font-size: 34px;
}

/* CSS by Atiq */
.s_idea_submit .badge-tag:hover {
  background-color: #b4c3d3;
  cursor: pointer;
}

/* CSS by Tanvir */
/* For tags */
.s_idea_submit .tags-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border: 1px solid #ced4da;
  padding: 4px;
  border-radius: 0.375rem;
  min-height: 38px;
}
/* .s_idea_submit .tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25em 0.5em;
  margin: 0.25em;
  background-color: #f1f1f1;
  border-radius: 5px;
  opacity: 0.8;
  background: #eef3fe;
  color: #181c32;
  font-family: "Nunito";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.s_idea_submit .tag .close-btn {
  margin-left: 0.5em;
  font-weight: bold;
  cursor: pointer;
}
.s_idea_submit .tags-container input {
  border: none;
  outline: none;
  flex-grow: 1;
  min-width: 120px;
}
.s_idea_submit .tags-container input:focus {
  box-shadow: none;
} */

.s_idea_submit .tagsinput,
.s_idea_submit .tagsinput * {
  box-sizing: border-box;
}
.s_idea_submit .tagsinput {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  background: #fff;
  font-family: sans-serif;
  font-size: 14px;
  line-height: 20px;
  color: #556270;
  padding: 5px 5px;
  border: 1px solid #e6e6e6;
  border-radius: 2px;
  width: 100%;
  min-height: auto;
  height: auto;
}
.s_idea_submit .tagsinput.focus {
  border-color: #ccc;
}

.s_idea_submit .tagsinput .tag {
  display: inline-block;
  padding: 0.5em 0.6em;
  font-size: 1.15em;
  font-weight: normal;
  margin: 0.25em;
  font-family: 'Nunito';
  cursor: pointer;
}

.s_idea_submit .tagsinput .tag.badge-tag-selected {
  background: #1fccc6;
}
