/* .s_problems .btn-filter {
    border-radius: 5px;
    border: 1px solid #D8E2EF;
}

.s_problems .card {
    border-radius: 16px;
    background: #FFF;
    box-shadow: 0px 8px 30px 0px rgba(24, 28, 50, 0.16);
}

.s_problems .card-problem .card-body {
    padding: 1.25rem;
}

.s_problems .card-problem .link {
    color: rgba(13, 12, 34, 0.90);
    font-family: Nunito;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.s_problems .pagination .page-item {
    margin-right: 4px;
    margin-left: 4px;

    border-radius: 4px;
    border: 1px solid #D8E2EF;
    background: #FFF;
}

.s_problems .pagination .page-item.prev, .s_problems .pagination .page-item.next {
    border-radius: 0;
    border: none;
    background: none;
}

.s_problems .dropdown-toggle {
    color: #181C32;
    font-family: 'Nuni<PERSON>';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.s_problems .search.input-group {
    border-radius: 5px;
    border-top-right-radius: 5px !important;
    border-bottom-right-radius: 5px !important;
    border: 1px solid #D8E2EF;
    background: rgba(255, 255, 255, 0.50);
    padding: 2px !important;
}

.s_problems .search .form-control, .s_problems .search .input-group-append {
    border-radius: 0px;
    border: none;
    background: rgba(255, 255, 255, 0.50);
}

.s_problems .pagination .page-item .page-link {
    border-radius: 4px;
    border: 1px solid #D8E2EF;
    background: #FFF;

    color: #181C32;
    text-align: center;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.s_problems .pagination .page-item.active .page-link {
    background: #C9D6F1;
}

.s_problems .pagination .page-item.prev .page-link, .s_problems .pagination .page-item.next .page-link {
    border: 0;
    border-radius: 0;
    background: none;
}

.s_problems .card-problem .featured-image {
    border-radius: 16px;
    min-height: 149px;
    max-height: 149px;
} */

/* Large devices (desktops, 992px and up) */
/* @media (min-width: 992px) { 
    .s_problems .card-problem .featured-image {
        border-radius: 16px;
    }
} */
