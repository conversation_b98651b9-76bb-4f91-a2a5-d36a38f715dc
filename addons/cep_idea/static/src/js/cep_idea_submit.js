$(document).ready(function () {
  const tags = $('#tagContainer').data('tags');
  const ideaTags = $('#tagsinput').data('idea-tags');
  let selectedTagObjects = [];

  if (tags) {
    if (ideaTags) {
      selectedTagObjects = [...ideaTags];
      function initializeSelectedTags() {
        ideaTags.forEach((tag) => {
          // Find the span with the matching tag-id and change its class
          const tagElement = $(`.tag[tag-id="${tag.id}"]`);
          tagElement.addClass('badge-tag-selected text-white fw-bold');
        });
      }
      initializeSelectedTags();
    }

    $('.tag').click(function () {
      const $this = $(this);
      const tagId = parseInt($this.attr('tag-id'));

      const tagObject = tags.find((tag) => tag.id === tagId);

      $this.toggleClass('badge-tag-selected text-white fw-bold');

      if ($this.hasClass('badge-tag-selected text-white')) {
        selectedTagObjects.push(tagObject);
      } else {
        selectedTagObjects = selectedTagObjects.filter(
          (tag) => tag.id !== tagId
        );
      }
    });
  }

  $('.close-btn').click(function () {
    $(this).parent().remove();
  });

  // Go to previous page
  $('.back-btn').click(function () {
    history.back();
  });

  // handle characters on description field
  $('#description').on('change keyup paste', function () {
    const inputText = $(this).val();
    $('#description-char-numbers').text(inputText.length);
  });

  // Handling Uploaded images
  // Preview cover photo
  $('#cover_photo').change(function () {
    $('#cover_photo_preview').empty();
    if (this.files) {
      $.each(this.files, function (i, file) {
        let reader = new FileReader();
        reader.onload = function (e) {
          $('#cover_photo_preview').append(
            `<div class="position-relative">
                            <img src="${e.target.result}" alt="Cover Photo" class="img-thumbnail">
                            <button type="button" class="btn btn-sm btn-danger position-absolute top-0 start-100 translate-middle" onclick="removeFile('cover_photo', ${i})">&times;</button>
                        </div>`
          );
        };
        reader.readAsDataURL(file);
      });
    }
  });

  // Preview attachments
  $('#attachment').change(function () {
    if (this.files) {
      const allowedExtensions = ['doc', 'docx', 'pdf', 'jpg', 'jpeg', 'png']; // Array of allowed extensions

      $.each(this.files, function (i, file) {
        const fileExtension = file.name.split('.').pop().toLowerCase(); // Extract extension

        if (!allowedExtensions.includes(fileExtension)) {
          // File type not allowed
          alert(
            'Error: Unsupported file type. Only .doc, .docx, .pdf, and image files are allowed.'
          );
          return; // Skip processing this file
        }

        let reader = new FileReader();
        reader.onload = function (e) {
          $('#attachment_preview').append(
            `<div class="position-relative border rounded p-2">
                  <div class="d-flex flex-column align-items-start">
                    <p class="mb-1">${file.name}</p>
                    <p class="text-muted mb-1">${(file.size / 1024).toFixed(
                      2
                    )} KB</p>
                    <button type="button" class="btn btn-sm btn-danger position-absolute top-0 start-100 translate-middle" onclick="removeFile('attachment', ${i})">&times;</button>
                  </div>
                </div>`
          );
        };
        reader.readAsDataURL(file);
      });
    }
  });

  // Remove selected file
  window.removeFile = function (inputId, index) {
    let input = document.getElementById(inputId);
    let dt = new DataTransfer();
    for (let i = 0; i < input.files.length; i++) {
      if (i !== index) {
        dt.items.add(input.files[i]);
      }
    }
    input.files = dt.files;

    if (inputId === 'cover_photo') {
      $('#cover_photo_preview').children().eq(index).remove();
    } else {
      $('#attachment_preview').children().eq(index).remove();
    }
  };

  //on form submit
  var form = null;
  if ($('#idea_submit_form').length) {
    form = $('#idea_submit_form');
  } else {
    form = $('#idea_update_form');
  }
  form.on('submit', function (e) {
    // Attach tags to the hidden input field

    var serializedTags = JSON.stringify(selectedTagObjects);
    form.find('input[name="all_tag_ids"]').val(serializedTags);
  });
});
