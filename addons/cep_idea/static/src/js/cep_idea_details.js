console.log("cep_idea_details js page loaded");

$(document).ready(function() {

  // Copy Link button
  $('#copyLinkButton').on('click', function() {
    var currentUrl = window.location.href;
  
    // Create a temporary input element
    var tempInput = $('<input>');
    tempInput.val(currentUrl);
    $('body').append(tempInput);
  
    // Copy the value of the input element to clipboard
    tempInput.select();
    tempInput[0].setSelectionRange(0, 99999); // For mobile devices
    document.execCommand('copy');
  
    // Remove the temporary input element
    tempInput.remove();
  
    // Optionally, provide feedback to the user
    alert('Link copied to clipboard!');
  });


  //tag selector
  var tagContainer = $('#tagContainer');
  var tags = tagContainer.find('.tag-btn');

  tags.on('click', function() {
    var $tag = $(this);
    if ($tag.hasClass('selected')) {
      $tag.removeClass('selected');
    } else {
      $tag.addClass('selected');
    }
  });

  $('.problem-title').on('click', function() {
    var dynamicHref = $(this).data('href');
    // Navigate to the URL specified in data-href
    window.location.href = dynamicHref;
  });

});

