odoo.define('cep_idea.reaction', function (require) {
  "use strict";

  var publicWidget = require('web.public.widget');

  publicWidget.registry.ReactionButton = publicWidget.Widget.extend({
    selector: '.s_idea_details',
    events: {
      'click .reaction_button': '_onReact',
      'click .btn.delete': '_onDelete',
    },

    _onReact: function (ev) {
      ev.preventDefault();
      var self = this;
      var $button = $(ev.currentTarget);
      var model = $button.data('model');
      var id = $button.data('id');
      var type = $button.data('type');

      this._rpc({
        route: `/${model}/${id}/reactions/${type}/create`,
        params: {},
      }).then(function (result) {
        if (result.error) {
          if (result.error === 'not_authenticated') {
            window.location.href = result.redirect_url;
          } else {
            alert(result.error);
          }
        } else {
          $('#likes_count_' + model + '_' + id).text(result.likes_count);
          $('#dislikes_count_' + model + '_' + id).text(result.dislikes_count);
        }
      });
    },

    _onDelete: function (ev) {
      ev.preventDefault();
      var $button = $(ev.currentTarget);
      var commentId = $button.data('id');
      var typeOfComment = $button.data('type');

      this._rpc({
        route: `/comment/${commentId}/delete`,
        params: {},
      }).then(function (result) {
        if (result.error) {
          if (result.error === 'not_authenticated') {
            window.location.href = result.redirect_url;
          } else {
            alert(result.error);
          }
        } else if (result.success) {
          $('#' + typeOfComment + '-' + commentId).remove();
        }
      });
    },
  });
});
