<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.rule" id="official_update_multi_company_rule">
        <field name="name">Comment multi-company</field>
        <field name="model_id" ref="model_cep_idea_official_update"/>
        <field name="domain_force">['|', ('idea_id.problem_id.company_ids', '=', False), ('idea_id.problem_id.company_ids', 'in', company_ids)]</field>
    </record>
</odoo>