<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.rule" id="reaction_multi_company_rule">
        <field name="name">Reaction multi-company</field>
        <field name="model_id" ref="model_cep_idea_reaction"/>
        <field name="domain_force">['|', ('idea_id.problem_id.company_ids', 'in', company_ids), ('comment_id.idea_id.problem_id.company_ids', 'in', company_ids)]</field>
    </record>
</odoo>