{
    'name': "CEP Idea",
    'summary': "Module of CEP Idea",
    'description': """TODO""",
    'author': "Technovative Solutions LTD",
    'license': "AGPL-3",
    'website': "https://www.technovativesolutions.co.uk",
    'category': 'Advertisement',
    'version': '0.0.1',
    'depends': [
        'website'
    ],
    'data': [
        'security/ir.model.access.csv',
        'security/problem_security.xml',
        'security/idea_security.xml',
        'security/phase_security.xml',
        'security/comment_security.xml',
        'security/official_update_security.xml',
        'security/reaction_security.xml',
        'security/tag_security.xml',
        'views/cep_idea_problem_views.xml',
        'views/cep_idea_idea_views.xml',
        'views/cep_idea_phase_views.xml',
        'views/cep_idea_comment_views.xml',
        'views/cep_idea_official_update_views.xml',
        'views/cep_idea_reaction_views.xml',
        'views/cep_idea_tag_views.xml',
        'views/problems/list/templates.xml',
        'views/problems/view/templates.xml',
        'views/ideas/new/templates.xml',
        'views/ideas/edit/templates.xml',
        'views/ideas/view/templates.xml',
        'views/menu.xml',
        'views/snippets/problem_list.xml',
        'views/snippets/snippets.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'cep_idea/static/src/js/cep_problem_list_snippet.js',
            'cep_idea/static/src/css/cep_problem_list_snippet.css',
        ],
        'cep_idea.idea_details': [
            'cep_idea/static/src/css/cep_idea_details.css',
            'cep_idea/static/src/js/cep_idea_details.js',
        ],
        'cep_idea.problem_details': [
            'cep_idea/static/src/js/cep_problem_details.js',
            'cep_idea/static/src/css/cep_problem_details.css',
        ],
        'cep_idea.problem_list': [
            'cep_idea/static/src/js/cep_problem_list.js',
            'cep_idea/static/src/css/cep_problem_list.css',
        ],
        'cep_idea.idea_submit': [
            'cep_idea/static/src/js/cep_idea_submit.js',
            'cep_idea/static/src/css/cep_idea_submit.css',
        ],
        'cep_idea.cep_idea_common': [
            'cep_idea/static/src/css/cep_idea_common.css',
        ],
        'cep_idea.cep_idea_comment': [
            'cep_idea/static/src/js/cep_idea_comment.js',
            'cep_idea/static/src/css/cep_idea_comment.css',
        ],

    },
    'installable': True,
    'application': True,
    'auto_install': False,
}
