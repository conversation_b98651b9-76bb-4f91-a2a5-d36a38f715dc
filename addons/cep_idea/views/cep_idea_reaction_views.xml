<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_idea_reaction_search" model="ir.ui.view">
        <field name="name">cep.idea.reaction.search</field>
        <field name="model">cep.idea.reaction</field>
        <field name="arch" type="xml">
            <search string="CEP Idea Reaction">
                <field name="type"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_idea_reaction_tree" model="ir.ui.view">
        <field name="name">cep.idea.reaction.tree</field>
        <field name="model">cep.idea.reaction</field>
        <field name="arch" type="xml">
            <tree>
                <field name="type"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_idea_reaction_form" model="ir.ui.view">
        <field name="name">cep.idea.reaction.form</field>
        <field name="model">cep.idea.reaction</field>
        <field name="arch" type="xml">
            <form string="CEP Idea Reaction">
                <sheet>
                    <group>
                        <field name="type"/>
                        <field name="owner_id"/>
                        <field name="idea_id"/>
                        <field name="comment_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id='cep_idea_reaction_action' model='ir.actions.act_window'>
        <field name="name">Reactions</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.idea.reaction</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new reaction
            </p>
        </field>
    </record>

    <menuitem id="cep_idea_reaction" name="Reactions" parent="cep_idea_root" action="cep_idea_reaction_action"/>
</odoo>