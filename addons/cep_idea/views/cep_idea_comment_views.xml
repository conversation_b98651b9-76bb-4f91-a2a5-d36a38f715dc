<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_idea_comment_search" model="ir.ui.view">
        <field name="name">cep.idea.comment.search</field>
        <field name="model">cep.idea.comment</field>
        <field name="arch" type="xml">
            <search string="CEP Idea Comment">
                <field name="message"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_idea_comment_tree" model="ir.ui.view">
        <field name="name">cep.idea.comment.tree</field>
        <field name="model">cep.idea.comment</field>
        <field name="arch" type="xml">
            <tree>
                <field name="message"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_idea_comment_form" model="ir.ui.view">
        <field name="name">cep.idea.comment.form</field>
        <field name="model">cep.idea.comment</field>
        <field name="arch" type="xml">
            <form string="CEP Idea Comment">
                <sheet>
                    <group>
                        <field name="message"/>
                        <field name="owner_id"/>
                        <field name="idea_id"/>
                        <field name="parent_id"/>
                    </group>
                    <notebook>
                        <page name="reactions" string="Reactions">
                            <field name="reaction_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="type"/>
                                        <field name="owner_id"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id='cep_idea_comment_action' model='ir.actions.act_window'>
        <field name="name">Comments</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.idea.comment</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new comment
            </p>
        </field>
    </record>

    <menuitem id="cep_idea_comment" name="Comments" parent="cep_idea_root" action="cep_idea_comment_action"/>
</odoo>