<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_idea_problem_search" model="ir.ui.view">
        <field name="name">cep.idea.problem.search</field>
        <field name="model">cep.idea.problem</field>
        <field name="arch" type="xml">
            <search string="CEP Idea Problem">
                <field name="title"/>
                <field name="description"/>
                <field name="location"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_idea_problem_tree" model="ir.ui.view">
        <field name="name">cep.idea.problem.tree</field>
        <field name="model">cep.idea.problem</field>
        <field name="arch" type="xml">
            <tree>
                <field name="title"/>
                <field name="location"/>
                <field name="create_date"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_idea_problem_form" model="ir.ui.view">
        <field name="name">cep.idea.problem.form</field>
        <field name="model">cep.idea.problem</field>
        <field name="arch" type="xml">
            <form string="CEP Idea Problem">
                <sheet>
                    <field name="cover_photo" widget="image" class="oe_avatar"/>
                    <group>
                        <field name="title"/>
                        <field name="description"/>
                        <field name="location"/>
                        <field name="company_ids" groups="base.group_multi_company" widget="many2many_tags"/>
                        <field name="is_publish"/>
                    </group>
                    <notebook>
                        <page name="ideas" string="Ideas">
                            <field name="idea_ids" nolabel="1" options="{'no_create': True}">
                                <tree >
                                    <field name="title"/>
                                    <field name="status"/>
                                </tree>
                                <form>

                                    <sheet>
                                        <field name="cover_photo" widget="image" class="oe_avatar" readonly="1"/>
                                        <group>
                                            <field name="title" readonly="1"/>
                                            <field name="description" readonly="1"/>

                                            <field name="status" />

                                            <field name="problem_id" invisible="1"/>


                                        </group>
                                        <notebook>

                                            <page name="phases" string="Phases">
                                                <field name="phase_ids" options="{'no_create': True, 'no_edit': True}" context="{'default_problem_id': active_id}">
                                                    <tree>
                                                        <field name="name"/>
                                                        <field name="start_date"/>
                                                        <field name="end_date"/>
                                                    </tree>
                                                    <form>
                                                        <sheet>
                                                            <group>
                                                                <field name="name" readonly="1"/>
                                                                <field name="description" readonly="1"/>
                                                                <field name="start_date" readonly="1"/>
                                                                <field name="end_date" readonly="1"/>
                                                            </group>
                                                        </sheet>
                                                    </form>
                                                </field>
                                            </page>

                                            <page name="attachments" string="Attachments">
                                                <field name="attachment_ids" widget="one2many_list" options="{'no_create': True, 'no_open': True}" readonly="1">
                                                    <tree editable="top">
                                                        <field name="name"/>
                                                        <field name="create_date"/>
                                                    </tree>
                                                    <form>

                                                        <sheet>

                                                            <group>
                                                                <field name="type"/>
                                                                <field name="datas" filename="name" attrs="{'invisible':[('type','=','url')]}"/>
                                                                <field name="url" widget="url" attrs="{'invisible':[('type','=','binary')]}"/>
                                                                <field name="mimetype" groups="base.group_no_one"/>
                                                            </group>

                                                        </sheet>

                                                    </form>
                                                </field>
                                            </page>


                                        </notebook>
                                    </sheet>
                                </form>
                            </field>
                        </page>
                        <page name="phases" string="Phases">
                            <field name="phase_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="name"/>
                                        <field name="description"/>
                                        <field name="start_date"/>
                                        <field name="end_date"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page name="tags" string="Tags">
                            <field name="tag_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="name"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page name="other_info" string="Other Info">
                            <group>
                                <field name="owner_id"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id='cep_idea_problem_action' model='ir.actions.act_window'>
        <field name="name">Problems</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.idea.problem</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new problem
            </p>
        </field>
    </record>

    <menuitem id="cep_idea_root" name="Idea Generation" sequence="0">
        <menuitem id="cep_idea_problem" name="Problems" action="cep_idea_problem_action"/>
    </menuitem>
</odoo>