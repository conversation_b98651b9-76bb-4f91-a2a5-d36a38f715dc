<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_idea_official_update_search" model="ir.ui.view">
        <field name="name">cep.idea.official.update.search</field>
        <field name="model">cep.idea.official.update</field>
        <field name="arch" type="xml">
            <search string="CEP Idea Official update">
                <field name="message"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_idea_official_update_tree" model="ir.ui.view">
        <field name="name">cep.idea.official.update.tree</field>
        <field name="model">cep.idea.official.update</field>
        <field name="arch" type="xml">
            <tree>
                <field name="message"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_idea_official_update_form" model="ir.ui.view">
        <field name="name">cep.idea.official.update.form</field>
        <field name="model">cep.idea.official.update</field>
        <field name="arch" type="xml">
            <form string="CEP Idea Official update">
                <sheet>
                    <group>
                        <field name="message"/>
                        <field name="idea_id"/>
                        <field name="owner_id"/>
                        <field name="create_date"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id='cep_idea_official_update_action' model='ir.actions.act_window'>
        <field name="name">Official Updates</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.idea.official.update</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new official update
            </p>
        </field>
    </record>

    <menuitem id="cep_idea_official_update" name="Official Updates" parent="cep_idea_root" action="cep_idea_official_update_action"/>
</odoo>