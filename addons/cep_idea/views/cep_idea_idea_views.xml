<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_idea_idea_search" model="ir.ui.view">
        <field name="name">cep.idea.idea.search</field>
        <field name="model">cep.idea.idea</field>
        <field name="arch" type="xml">
            <search string="CEP Idea">
                <field name="title"/>
                <field name="description"/>
                <field name="status"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_idea_idea_tree" model="ir.ui.view">
        <field name="name">cep.idea.idea.tree</field>
        <field name="model">cep.idea.idea</field>
        <field name="arch" type="xml">
            <tree>
                <field name="title"/>
                <field name="status"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_idea_idea_form" model="ir.ui.view">
        <field name="name">cep.idea.idea.form</field>
        <field name="model">cep.idea.idea</field>
        <field name="arch" type="xml">
            <form string="CEP Idea">
                <sheet>
                    <field name="cover_photo" widget="image" class="oe_avatar" readonly="1"/>
                    <group>
                        <field name="title" readonly="1"/>
                        <field name="description" readonly="1"/>
                        <field name="status" />
                        <field name="tag_ids" widget="many2many_tags" readonly="1"/>
                        <field name="problem_id" readonly="1"/>
                    </group>
                    <notebook>
                        <page name="image_and_attachment" string="Image and Attachment">

                            <field name="attachment_ids" widget="one2many_list" options="{'no_create': True, 'no_open': True}" readonly="1">
                                <tree editable="top">
                                    <field name="name"/>
                                    <field name="create_date"/>
                                </tree>
                                <form>
                                    <sheet>
                                        <group>
                                            <field name="type"/>
                                            <field name="datas" filename="name" attrs="{'invisible':[('type','=','url')]}"/>
                                            <field name="url" widget="url" attrs="{'invisible':[('type','=','binary')]}"/>
                                            <field name="mimetype" groups="base.group_no_one"/>
                                        </group>
                                    </sheet>
                                </form>
                            </field>
                        </page>
                        <page name="comments" string="Comments">
                            <field name="comment_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="message"/>
                                        <field name="parent_id"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page name="phases" string="Phases">
                            <field name="phase_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="name"/>
                                        <field name="description"/>
                                        <field name="start_date"/>
                                        <field name="end_date"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page name="official_updates" string="Official Updates">
                            <field name="official_update_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="message"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page name="reactions" string="Reactions">
                            <field name="reaction_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="type"/>
                                        <field name="owner_id"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page name="other_info" string="Other Info">
                            <group>
                                <field name="owner_id"/>
                                <field name="create_date"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id='cep_idea_idea_action' model='ir.actions.act_window'>
        <field name="name">Ideas</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.idea.idea</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new CEP idea
            </p>
        </field>
    </record>

    <menuitem id="cep_idea_idea" name="Ideas" parent="cep_idea_root" action="cep_idea_idea_action"/>
</odoo>