<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="cep_idea.ideas_new" name="Create Idea">
        <t t-call="website.layout">
            <t t-set="title">Idea Submit</t>
            <t t-set="head">
                <t t-call-assets="web.assets_common"/>
                <t t-call-assets="web.assets_frontend"/>
                <t t-call-assets="cep_idea.cep_idea_common"/>
                <t t-call-assets="cep_idea.idea_submit"/>
            </t>

            <br/>
            <div class="oe_structure">
                <div class="container s_idea_submit">
                    <!-- Go Back Button -->
                    <div class="row justify-content-md-center">
                        <div class="col-12 col-md-6 col-md-auto">
                            <div class="d-flex flex-row align-items-center gap-2 mb-2">
                                <a href="#" class="text-decoration-none back-btn">
                                    <i class="fa fa-angle-left" aria-hidden="true"></i>
                                </a>

                                <h5 class="fw-500 c-dark-gunmetal p-0 m-0">Go Back</h5>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Form Section-->
                    <div class="row justify-content-md-center">
                        <div class="col-12 col-md-6 col-md-auto">
                            <div class="d-flex flex-column align-items-center gap-3 mb-3">
                                <h3 class="fw-800 c-maximum-blue-green">Share your Idea</h3>
                                <form role="form" t-att-action="'create'" method="POST" enctype="multipart/form-data"
                                    id="idea_submit_form">
                                    <!-- Hidden fields -->
                                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                    <input type="hidden" id="status" name="status" t-att-value="'proposed'"/>
                                    <input type="hidden" name="all_tag_ids" id="tag_ids_input" value=""/>

                                    <!-- Title and description field -->
                                    <div class="card w-100 mt-4">
                                        <div class="card-body">
                                            <h4 class="fw-700 c-maximum-blue-green">What is your
                                                Idea?</h4>

                                            <div class="mb-3">
                                                <label for="title">Title</label>
                                                <input type="text" class="form-control" name="title" id="title"
                                                    placeholder="Title" required="true"/>
                                            </div>

                                            <p class="text-form float-end">
                                                <span id="description-char-numbers">00</span>/200</p>

                                            <div class="form-text mb-2">
                                                Idea Description
                                            </div>

                                            <div class="mb-3">
                                                <textarea class="form-control" placeholder="Description"
                                                    name="description" id="description" style="height: 100px"
                                                    t-att-value="description" required="true" maxlength="200"></textarea>
                                            </div>

                                        </div>
                                    </div>

                                    <!-- Attachments and Cover Photo -->
                                    <div class="card w-100 mt-4">
                                        <div class="card-body d-flex flex-column gap-3">
                                            <h4 class="fw-700 c-maximum-blue-green">Attachments</h4>

                                            <div class="d-flex flex-column gap-1 mb-3">
                                                <h6>Cover Photo<span class="fw-400 opacity-80">
                                                        (optional)</span>
                                                </h6>
                                                <p class="fw-400">Make your idea stand out. This
                                                    image will be shown at the top of the content.</p>

                                                <div onclick="document.getElementById('cover_photo').click();"
                                                    class="d-flex flex-column align-items-center gap-3 py-4 upload-image">
                                                    <svg width="42" height="41" viewBox="0 0 42 41" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M24.3814 7.11116C25.0816 7.11116 25.6513 7.68079 25.6513 8.38099C25.6513 9.08119 25.0816 9.65082 24.3814 9.65082C23.6812 9.65082 23.1116 9.08116 23.1116 8.38099C23.1116 7.68079 23.6812 7.11116 24.3814 7.11116ZM24.3814 6.09528C23.119 6.09528 22.0957 7.11862 22.0957 8.38099C22.0957 9.64336 23.119 10.6667 24.3814 10.6667C25.6438 10.6667 26.6671 9.64336 26.6671 8.38099C26.6671 7.11862 25.6438 6.09528 24.3814 6.09528Z"
                                                            fill="#181C32"/>
                                                        <path
                                                            d="M28.8 1.06668C29.9763 1.06668 30.9333 2.02368 30.9333 3.2V28.8C30.9333 29.9763 29.9763 30.9333 28.8 30.9333H3.2C2.02368 30.9333 1.06668 29.9763 1.06668 28.8V3.2C1.06668 2.02368 2.02368 1.06668 3.2 1.06668H28.8ZM28.8 0H3.2C1.43252 0 0 1.43252 0 3.2V28.8C0 30.5675 1.43252 32 3.2 32H28.8C30.5675 32 32 30.5675 32 28.8V3.2C32 1.43252 30.5675 0 28.8 0ZM10.8061 11.9073C10.9354 11.9073 11.06 11.9542 11.1576 12.0399L19.8344 19.6479L20.5377 20.2646L21.2409 19.6478L23.6481 17.5366C23.7455 17.4513 23.8705 17.4043 24 17.4043C24.1295 17.4043 24.2545 17.4513 24.3517 17.5364L30.9333 23.3079V28.8C30.9333 29.9763 29.9763 30.9333 28.8 30.9333H3.2C2.02368 30.9333 1.06668 29.9763 1.06668 28.8V20.2716L10.4552 12.0394C10.5522 11.9543 10.6769 11.9073 10.8061 11.9073ZM10.8061 10.8407C10.4296 10.8407 10.0531 10.9731 9.75147 11.2379L0 19.7883V28.8C0 30.5675 1.43252 32 3.2 32H28.8C30.5675 32 32 30.5675 32 28.8V22.8245L25.0549 16.7344C24.753 16.4699 24.3765 16.3376 24 16.3376C23.6235 16.3376 23.2469 16.4699 22.9451 16.7344L20.5376 18.8459L11.8608 11.2379C11.5592 10.9731 11.1827 10.8407 10.8061 10.8407Z"
                                                            fill="#181C32"/>
                                                        <path
                                                            d="M35.7893 28.3345C35.7893 28.3246 35.7901 28.3151 35.7901 28.3053C35.7901 26.3014 34.0353 24.677 31.8707 24.677C31.1865 24.677 30.5434 24.8398 29.9834 25.1251C28.8014 23.2562 26.6111 22 24.1029 22C20.3412 22 17.292 24.823 17.292 28.305C17.292 28.3616 17.2951 28.4177 17.2967 28.4737C14.271 29.1322 12 31.6578 12 34.6524C12 38.1434 15.0855 41 18.8567 41H35.1433C38.9145 41 42 38.1437 42 34.6524C42 31.3632 39.2603 28.6384 35.7893 28.3345Z"
                                                            fill="#181C32"/>
                                                        <path
                                                            d="M26.5639 28.2679C26.8006 28.0174 27.1994 28.0174 27.4361 28.2679L30.3738 31.3769C30.7353 31.7595 30.4641 32.3889 29.9377 32.3889H24.0623C23.5359 32.3889 23.2647 31.7595 23.6262 31.3769L26.5639 28.2679Z"
                                                            fill="white"/>
                                                        <rect x="24.9023" y="31.7913" width="4.19355" height="5.04744"
                                                            rx="0.6" fill="white"/>
                                                    </svg>
                                                    <p class="p-0 m-0">Select an image (max. 10MB)</p>
                                                    <input type="file" name="cover_photo" id="cover_photo"
                                                        class="d-none" accept=".jpg, .jpeg, .png"/>
                                                </div>
                                                <!-- Preview section -->
                                                <div id="cover_photo_preview" class="d-flex flex-wrap gap-2"></div>
                                            </div>

                                            <div class="d-flex flex-column gap-1">
                                                <h6>Attachments<span class="fw-400 opacity-80">
                                                        (optional)</span>
                                                </h6>
                                                <p class="fw-400">Upload files to give others more
                                                    information and context</p>

                                                <div onclick="document.getElementById('attachment').click();"
                                                    class="d-flex flex-row gap-3 py-3 px-3 upload-image">
                                                    <i class="fa fa-cloud-upload" aria-hidden="true"></i>
                                                    <p class="p-0 m-0">Click to select a file</p>
                                                    <input type="file" name="attachment" id="attachment" class="d-none"
                                                        multiple="multiple" accept=".doc,.docx,.pdf,image/*"/>
                                                </div>
                                            </div>
                                            <!-- Preview section -->
                                            <div id="attachment_preview" class="d-flex flex-wrap gap-2"></div>
                                        </div>
                                    </div>

                                    <div class="card w-100 mt-4">
                                        <div class="card-body">
                                            <div class="d-flex flex-column align-items-start gap-1">
                                                <h4 class="fw-700 c-maximum-blue-green">Use Tags</h4>
                                                <h6>Tags <span class="fw-400">(optional)</span>
                                                </h6>
                                                <div id="tagContainer" class="tags-container w-100"
                                                    t-att-data-tags="json.dumps(tags)">

                                                    <div id="tagsinput" class="tagsinput"
                                                        style="width: 100%; min-height: auto; height: auto;">


                                                        <t t-foreach="tags" t-as="tag">
                                                            <span class="badge tag badge-tag " t-att-tag-id="tag['id']">
                                                                <t t-esc="tag['name']"/>
                                                            </span>
                                                        </t>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-submit mt-4 mb-4">Publish
                                        your Idea</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <br/>

            <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"
                integrity="sha256-VazP97ZCwtekAsvgPBSUwPFKdrwD3unUfSGVYrahUqU=" crossorigin="anonymous"></script>
            <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css"/>
        </t>
    </template>
</odoo>