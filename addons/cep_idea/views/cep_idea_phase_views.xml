<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_idea_phase_search" model="ir.ui.view">
        <field name="name">cep.idea.phase.search</field>
        <field name="model">cep.idea.phase</field>
        <field name="arch" type="xml">
            <search string="CEP Idea Phase">
                <field name="name"/>
                <field name="description"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_idea_phase_tree" model="ir.ui.view">
        <field name="name">cep.idea.phase.tree</field>
        <field name="model">cep.idea.phase</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="start_date"/>
                <field name="end_date"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_idea_phase_form" model="ir.ui.view">
        <field name="name">cep.idea.phase.form</field>
        <field name="model">cep.idea.phase</field>
        <field name="arch" type="xml">
            <form string="CEP Idea Phase">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="description"/>
                        <field name="start_date"/>
                        <field name="end_date"/>
                        <field name="problem_id"/>
                        <field name="owner_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id='cep_idea_phase_action' model='ir.actions.act_window'>
        <field name="name">Phases</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.idea.phase</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new phase
            </p>
        </field>
    </record>

    <menuitem id="cep_idea_phase" name="Phases" parent="cep_idea_root" action="cep_idea_phase_action"/>
</odoo>