# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_idea
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20241017\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-12 13:47+0000\n"
"PO-Revision-Date: 2024-12-12 13:47+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "&amp;times;"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "00/80"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> İndirmek"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "<span class=\"status\">This problem is closed.</span>"
msgstr "<span class=\"status\">Bu sorun kapandı.</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid ""
"<span class=\"status\">This problem is currently open for "
"participation.</span>"
msgstr ""
"<span class=\"status\">Bu sorun şu anda katılıma  "
"açık</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "<span class=\"summary\">Share</span>"
msgstr "<span class=\"summary\">Paylaşmak</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "<span id=\"description-char-numbers\">00</span>/200"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "About"
msgstr "Hakkında"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__accepted
msgid "Accepted"
msgstr "Kabul edildi"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Add an Official Update"
msgstr "Resmi Güncelleme Ekle"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "All"
msgstr "Tüm"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__all_tag_ids
msgid "All Tags"
msgstr "Tüm Etiketler"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__company_ids
msgid "Allowed Companies"
msgstr "İzin Verilen Şirketler"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__attachment_ids
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Attachments"
msgstr "Ekler"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Attachments <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"
msgstr   ""
"Ekler<span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(isteğe bağlı)</span>"


#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Attachments<span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"
msgstr ""
"Ekler <span class=\"fw-400 opacity-80\">\n"
"                                                        (isteğe bağlı)</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.custom_snippet
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_search
msgid "CEP Idea"
msgstr "YSÖP Fikri"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_search
msgid "CEP Idea Comment"
msgstr "YSÖP Fikir Yorumu"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_official_update
msgid "CEP Idea Official Update"
msgstr "CEP Fikri Resmi Güncellemesi"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_official_update_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_official_update_search
msgid "CEP Idea Official update"
msgstr "CEP Fikri Resmi Güncellemesi"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_phase
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_phase_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_phase_search
msgid "CEP Idea Phase"
msgstr "YSÖP Fikir Aşaması"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_search
msgid "CEP Idea Problem"
msgstr "YSÖP Fikir Sorunu"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_reaction_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_reaction_search
msgid "CEP Idea Reaction"
msgstr "YSÖP Fikir Tepkisi"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_tag_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_tag_search
msgid "CEP Idea Tag"
msgstr "YSÖP Fikir Etiketi"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_comment
msgid "Citizen Engagement Platform Comment"
msgstr "Vatandaş Katılım Platformu Yorumu"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_idea
msgid "Citizen Engagement Platform Idea"
msgstr "Vatandaş Katılımı Platformu Fikri"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_problem
msgid "Citizen Engagement Platform Problem for Idea"
msgstr "Vatandaş Katılımı Platformu Fikir Sorunu"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_reaction
msgid "Citizen Engagement Platform Reaction"
msgstr "Vatandaş Katılım Platformu Tepkisi"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_tag
msgid "Citizen Engagement Platform Tag"
msgstr "Vatandaş Katılımı Platformu Etiketi"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Click to select a file"
msgstr "Bir dosya seçmek için tıklayın"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__comment_id
msgid "Comment"
msgstr "Yorum"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_comment_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__comment_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_comment
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Comments"
msgstr "Yorumlar"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Comments ("
msgstr "Yorumlar ("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__cover_photo
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__cover_photo
msgid "Cover Photo"
msgstr "Kapak Fotoğrafı"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Cover Photo <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"
msgstr ""
"Kapak Fotoğrafı <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(isteğe bağlı)</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Cover Photo<span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"
msgstr ""
"Cover Photo<span class=\"fw-400 opacity-80\">\n"
"                                                        (isteğe bağlı)</span>"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__create_date
msgid "Create Date"
msgstr "Tarih Oluştur"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_idea_action
msgid "Create a new CEP idea"
msgstr ""

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_comment_action
msgid "Create a new comment"
msgstr "Yeni bir yorum oluştur"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_official_update_action
msgid "Create a new official update"
msgstr "Yeni bir resmi güncelleme oluştur"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_phase_action
msgid "Create a new phase"
msgstr "Yeni bir aşama oluşturun"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_problem_action
msgid "Create a new problem"
msgstr "Yeni bir sorun yarat"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_reaction_action
msgid "Create a new reaction"
msgstr "Yeni bir etiket oluştur"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_tag_action
msgid "Create a new tag"
msgstr "Yeni bir etiket oluştur"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__create_date
msgid "Created on"
msgstr "Oluşturulma tarihi"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Current status"
msgstr "Mevcut durum"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__description
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Description"
msgstr "Tanım"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_reaction__type__dislike
msgid "Dislike"
msgstr "Beğenmedim"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Dislike("
msgstr "Beğenmedim("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__display_name
msgid "Display Name"
msgstr "Ekran adı"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Download Attachments"
msgstr "Ekleri İndir"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "Download existing attachment"
msgstr "Mevcut eki indir"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__end_date
msgid "End Date"
msgstr "Bitiş Tarihi"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Ends on"
msgstr "Bitiş tarihi"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Go Back"
msgstr "Geri gitmek"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__id
msgid "ID"
msgstr ""

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__idea_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__idea_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__idea_id
msgid "Idea"
msgstr "Fikir"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Idea Description"
msgstr "Fikir Açıklaması"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Idea Details"
msgstr "Fikir Detayları"

#. module: cep_idea
#: model:ir.ui.menu,name:cep_idea.cep_idea_root
#: model:website.menu,name:cep_idea.menu_list_mp
msgid "Idea Generation"
msgstr "Fikir Üretimi"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Idea Submit"
msgstr "Fikir Gönder"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_idea_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__idea_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Ideas"
msgstr "Fikirler"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Image and Attachment"
msgstr "Resim ve Ek"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__implemented
msgid "Implemented"
msgstr "Uygulandı"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__is_publish
msgid "Is Publish"
msgstr "Yayınlanıyor"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag____last_update
msgid "Last Modified on"
msgstr "Son Değiştirilme tarihi"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleme:"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme tarihi"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_reaction__type__like
msgid "Like"
msgstr "Beğenmek"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Like("
msgstr "Beğenmek("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__location
msgid "Location"
msgstr "Konum"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Make your idea stand out. This\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\timage will be shown at the top of the content."
msgstr ""
"Teklifinizin öne çıkmasını sağlayın. Bu\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\tresim içeriğin en üstünde gösterilecektir."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Make your idea stand out. This\n"
"                                                    image will be shown at the top of the content."
msgstr ""
"Teklifinizin öne çıkmasını sağlayın. Bu\n"
"                                           resim içeriğin en üstünde gösterilecek"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__message
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__message
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Message"
msgstr "Mesaj"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Newest"
msgstr "En yeni"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "No Idea submitted yet."
msgstr "Henüz Fikir Gönderilmedi."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Official Update"
msgstr "Resmi Güncelleme"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_official_update_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__official_update_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_official_update
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Official Updates"
msgstr "Resmi Güncellemeler"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Oldest"
msgstr "En eski"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Other Info"
msgstr "Diğer Bilgiler"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__owner_id
msgid "Owner"
msgstr "Mal sahibi"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__parent_id
msgid "Parent Comment"
msgstr "Ebeveyn Yorumu"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Participants"
msgstr "Katılımcılar"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__description
msgid "Phase Description"
msgstr "Aşama Açıklaması"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__name
msgid "Phase Name"
msgstr "Aşama Adı"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_phase_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__phase_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__phase_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_phase
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Phases"
msgstr "Aşamalar"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Post"
msgstr "Postalamak"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Posted on -"
msgstr "Yayınlandığı tarih -"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__problem_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__problem_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__problem_id
msgid "Problem"
msgstr "Sorun"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__description
msgid "Problem Description"
msgstr "Sorun Açıklaması"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Problem Details"
msgstr "Sorun Ayrıntıları"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_list
msgid "Problem List"
msgstr "Sorun Listesi"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__title
msgid "Problem Name"
msgstr "Sorun Adı"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.custom_snippet
msgid "Problem, List"
msgstr "Sorun, Liste"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_problem_action
#: model:ir.ui.menu,name:cep_idea.cep_idea_problem
msgid "Problems"
msgstr "Sorunlar"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__proposed
msgid "Proposed"
msgstr "Önerilen"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Publish"
msgstr "Yayınla"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Publish\n"
"\t\t\t\t\t\t\t\t\t\tyour Idea"
msgstr ""
"Fikrinizi\n"
"\t\t\t\t\t\t\t\t\t\tyayınlayın"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Publish\n"
"                                        your Idea"
msgstr ""
"Fikrinizi\n"
"                                        yayınlayın"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_reaction_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__reaction_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__reaction_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_reaction
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Reactions"
msgstr "Tepkiler"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__rejected
msgid "Rejected"
msgstr "Reddedilmiş"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problem_list
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Search ..."
msgstr "Aramak ..."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Select an image (max. 10MB)"
msgstr "Bir resim seçin (maks. 10MB)"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Share"
msgstr "Paylaşmak"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Share your Idea"
msgstr "Fikrinizi paylaşın"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__start_date
msgid "Start Date"
msgstr "Başlangıç ​​Tarihi"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__status
msgid "Status"
msgstr "Durum"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Submit your idea"
msgstr "Fikrinizi gönderin"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Submitted Idea ("
msgstr "Gönderilen Fikir ("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__name
msgid "Tag"
msgstr "Etiket"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_tag_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__tag_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__tag_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_tag
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Tags"
msgstr "Etiketler"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Tags <span class=\"fw-400\">(optional)</span>"
msgstr "Etiketler <span class=\"fw-400\">(isteğe bağlı)</span>"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__title
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Title"
msgstr "Başlık"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__type
msgid "Type"
msgstr "Tip"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__under_consideration
msgid "Under consideration"
msgstr "Değerlendiriliyor"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Upload files to give others more\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\tinformation and context"
msgstr ""
"Başkalarına daha fazla bilgi ve bağlam\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\tsağlamak için dosya yükleyin"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Upload files to give others more\n"
"                                                    information and context"
msgstr ""
"Başkalarına daha fazla bilgi ve bağlam\n"
"                                       sağlamak için dosya yükleyin"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Use Tags"
msgstr "Etiketleri Kullan"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__viewed
msgid "Viewed"
msgstr "Görüntülendi"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"What is your\n"
"\t\t\t\t\t\t\t\t\t\t\t\tIdea?"
msgstr ""
"Fikriniz\n"
"\t\t\t\t\t\t\t\t\t\t\t\tnedir?"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"What is your\n"
"                                                Idea?"
msgstr ""
"Fikriniz\n "
"                       nedir?"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Write your comment here ..."
msgstr "Yorumunuzu buraya yazın..."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Write your reply here ..."
msgstr "Cevabınızı buraya yazın..."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "idea cover"
msgstr "fikir kapağı"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "problem cover"
msgstr "sorun kapağı"
