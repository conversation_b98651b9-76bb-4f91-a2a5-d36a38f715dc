# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_idea
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20241017\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-13 15:55+0000\n"
"PO-Revision-Date: 2024-12-13 15:55+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "&amp;times;"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "00/80"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Atsisiųsti"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "<span class=\"status\">This problem is closed.</span>"
msgstr "<span class=\"status\">Problema išspręsta.</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid ""
"<span class=\"status\">This problem is currently open for "
"participation.</span>"
msgstr ""
"<span class=\"status\">Ši problema yra atvira  "
"dalyviams</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "<span class=\"summary\">Share</span>"
msgstr "<span class=\"summary\">Pasidalinti</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "<span id=\"description-char-numbers\">00</span>/200"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "About"
msgstr "Apie"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__accepted
msgid "Accepted"
msgstr "Priimta"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Add an Official Update"
msgstr "Pridėti oficialų atnaujinimą"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "All"
msgstr "Viskas"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__all_tag_ids
msgid "All Tags"
msgstr "Visos žymos"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__company_ids
msgid "Allowed Companies"
msgstr "Priimtinos organizacijos"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__attachment_ids
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Attachments"
msgstr "Priedas"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Attachments <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"
msgstr ""
"Priedas  <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(neprivalomas)</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Attachments<span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"
msgstr ""
"Attachments <span class=\"fw-400 opacity-80\">\n"
"                                   (neprivalomas)"
#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.custom_snippet
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_search
msgid "CEP Idea"
msgstr "CEP idėja"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_search
msgid "CEP Idea Comment"
msgstr "CEP idėjos komentaras"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_official_update
msgid "CEP Idea Official Update"
msgstr "CEA idėjos oficialus atnaujinimas"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_official_update_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_official_update_search
msgid "CEP Idea Official update"
msgstr "CEA idėjos oficialus atnaujinimas"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_phase
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_phase_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_phase_search
msgid "CEP Idea Phase"
msgstr "CEP idėjos etapas"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_search
msgid "CEP Idea Problem"
msgstr "CEP idėjos problema"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_reaction_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_reaction_search
msgid "CEP Idea Reaction"
msgstr "CEP idėjos reakcija"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_tag_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_tag_search
msgid "CEP Idea Tag"
msgstr "CEP idėjos žyma"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_comment
msgid "Citizen Engagement Platform Comment"
msgstr "Piliečių įtraukimo platformos komentaras"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_idea
msgid "Citizen Engagement Platform Idea"
msgstr "Piliečių įtraukimo platformos idėja"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_problem
msgid "Citizen Engagement Platform Problem for Idea"
msgstr "Piliečių įtraukimo platformos problema idėjai"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_reaction
msgid "Citizen Engagement Platform Reaction"
msgstr "Piliečių įtraukimo platformos reakcija"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_tag
msgid "Citizen Engagement Platform Tag"
msgstr "Citizen Engagement platformos žyma"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Click to select a file"
msgstr "Paspauskite dokumentui pasirinkti"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__comment_id
msgid "Comment"
msgstr "Komentaras"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_comment_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__comment_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_comment
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Comments"
msgstr "Komentarai"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Comments ("
msgstr "Komentarai ("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__cover_photo
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__cover_photo
msgid "Cover Photo"
msgstr "Viršelio nuotrauka"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Cover Photo <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"
msgstr ""
"Viršelio nuotrauka <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(neprivalomas)</span>"


#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Viršelio nuotrauka<span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"
msgstr ""
"Cover Photo<span class=\"fw-400 opacity-80\">\n"
"                                                       (neprivalomas)</span>"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__create_date
msgid "Create Date"
msgstr "Sukūrimo data"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_idea_action
msgid "Create a new CEP idea"
msgstr "Sukurkite naują CEP idėją"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_comment_action
msgid "Create a new comment"
msgstr "Naujas komentaras"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_official_update_action
msgid "Create a new official update"
msgstr "Naujas oficialus atnaujinimas"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_phase_action
msgid "Create a new phase"
msgstr "Sukurkite naują etapą"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_problem_action
msgid "Create a new problem"
msgstr "Sukurkite naują problemą"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_reaction_action
msgid "Create a new reaction"
msgstr "Sukurti naują reakciją"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_tag_action
msgid "Create a new tag"
msgstr "Sukurti naują žymą"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Current status"
msgstr "Dabartinė būsena"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__description
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Description"
msgstr "Aprašymas"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_reaction__type__dislike
msgid "Dislike"
msgstr "Nepatinka"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Dislike("
msgstr "Nepatinka("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__display_name
msgid "Display Name"
msgstr "Rodomas vardas"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Download Attachments"
msgstr "Atsisiųsti priedus"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "Download existing attachment"
msgstr "Atsisiųsti esamą priedą"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__end_date
msgid "End Date"
msgstr "Galutinė data"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Ends on"
msgstr "Baigiasi"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Go Back"
msgstr "Grįžti atgal"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__id
msgid "ID"
msgstr ""

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__idea_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__idea_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__idea_id
msgid "Idea"
msgstr "Idėja"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Idea Description"
msgstr "Idėjos aprašymas"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Idea Details"
msgstr "Idėjos detalės"

#. module: cep_idea
#: model:ir.ui.menu,name:cep_idea.cep_idea_root
#: model:website.menu,name:cep_idea.menu_list_mp
msgid "Idea Generation"
msgstr "Idėjų generavimas"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Idea Submit"
msgstr "Idėja pateikti"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_idea_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__idea_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Ideas"
msgstr "Idėjos"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Image and Attachment"
msgstr "Vaizdas ir priedas"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__implemented
msgid "Implemented"
msgstr "Įgyvendinta"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__is_publish
msgid "Is Publish"
msgstr "Yra paskelbta"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą pakeista"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_reaction__type__like
msgid "Like"
msgstr "Patinka"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Like("
msgstr "Patinka("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__location
msgid "Location"
msgstr "Vieta"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Make your idea stand out. This\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\timage will be shown at the top of the content."
msgstr ""
"Padarykite savo idėją išskirtine. Šis\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\tipaveikslas bus rodomas turinio viršuje."


#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Make your idea stand out. This\n"
"                                                    image will be shown at the top of the content."
msgstr ""
"Padarykite savo idėją išskirtine. Šis\n"
"                                             paveikslas bus rodomas turinio viršuje."

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__message
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__message
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Message"
msgstr "Žinutė"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Newest"
msgstr "Naujausias"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "No Idea submitted yet."
msgstr "Nėra pateiktų idėjų. "

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Official Update"
msgstr "Oficialus atnaujinimas"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_official_update_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__official_update_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_official_update
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Official Updates"
msgstr "Oficialūs atnaujinimai"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Oldest"
msgstr "Seniausias"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Other Info"
msgstr "Kita informacija"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__owner_id
msgid "Owner"
msgstr "Savininkas"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__parent_id
msgid "Parent Comment"
msgstr "Komentaras"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Participants"
msgstr "Dalyviai"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__description
msgid "Phase Description"
msgstr "Etapo aprašymas"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__name
msgid "Phase Name"
msgstr "Etapo pavadinimas"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_phase_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__phase_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__phase_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_phase
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Phases"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Post"
msgstr "Etapai"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Posted on -"
msgstr "Paskelbta -"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__problem_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__problem_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__problem_id
msgid "Problem"
msgstr "Problema"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__description
msgid "Problem Description"
msgstr "Problemos aprašymas"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Problem Details"
msgstr "Problemos detalės"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_list
msgid "Problem List"
msgstr "Problemų sąrašas"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__title
msgid "Problem Name"
msgstr "Problemos pavadinimas"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.custom_snippet
msgid "Problem, List"
msgstr "Problema, sąrašas"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_problem_action
#: model:ir.ui.menu,name:cep_idea.cep_idea_problem
msgid "Problems"
msgstr "Problemos"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__proposed
msgid "Proposed"
msgstr "Siūloma"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Publish"
msgstr "Paskelbti"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Publish\n"
"\t\t\t\t\t\t\t\t\t\tyour Idea"
msgstr ""
"Paskelbkite \n"
"\t\t\t\t\t\t\t\t\t\tsavo idėją"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Publish\n"
"                                        your Idea"
msgstr ""
"Paskelbkite \n"
"                                        savo idėją"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_reaction_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__reaction_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__reaction_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_reaction
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Reactions"
msgstr "Reakcijos"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__rejected
msgid "Rejected"
msgstr "Atmesta"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problem_list
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Search ..."
msgstr "Ieškoti ..."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Select an image (max. 10MB)"
msgstr "Pasirinkite paveikslėlį (ne daugiau kaip 10 MB)"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Share"
msgstr "Dalintis"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Share your Idea"
msgstr "Pasidalinkite savo idėja"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__start_date
msgid "Start Date"
msgstr "Pradžios data"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__status
msgid "Status"
msgstr "Statusas"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Submit your idea"
msgstr "Pateikite savo idėją"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Submitted Idea ("
msgstr "Pateikta idėja ("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__name
msgid "Tag"
msgstr "Žyma"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_tag_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__tag_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__tag_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_tag
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Tags"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Tags <span class=\"fw-400\">(optional)</span>"
msgstr "Žymos <span class=\"fw-400\">(optional)</span>"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__title
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Title"
msgstr "Pavadinimas"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__type
msgid "Type"
msgstr "Tipas"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__under_consideration
msgid "Under consideration"
msgstr "Svarstoma"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Upload files to give others more\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\tinformation and context"
msgstr ""
"Įkelkite dokumentus, kurie kitiems suteiks\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\tdaugiau informacijos ir konteksto"


#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Upload files to give others more\n"
"                                                    information and context"
msgstr ""
"Įkelkite dokumentus, kurie kitiems suteiks\n"
"daugiau informacijos ir konteksto"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Use Tags"
msgstr "Naudokite žymas"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__viewed
msgid "Viewed"
msgstr "Peržiūrėta"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"What is your\n"
"\t\t\t\t\t\t\t\t\t\t\t\tIdea?"
msgstr ""
"Kokia tavo \n"
"\t\t\t\t\t\t\t\t\t\t\t\tIdėja?"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"What is your\n"
"                                                Idea?"
msgstr ""
"Kokia tavo \n"
"Idėja?"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Write your comment here ..."
msgstr "Pridėkite savo komentarą čia..."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Write your reply here ..."
msgstr "Pateikite savo atsakymą čia..."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "idea cover"
msgstr "idėjos viršelis"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "problem cover"
msgstr "problemos viršelis"
