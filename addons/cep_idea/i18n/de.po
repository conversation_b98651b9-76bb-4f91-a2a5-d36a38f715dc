# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_idea
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20241017\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-13 16:35+0000\n"
"PO-Revision-Date: 2024-12-13 16:35+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "&amp;times;"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "00/80"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Herunterladen"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "<span class=\"status\">This problem is closed.</span>"
msgstr "<span class=\"status\">Dieses Problem ist behoben.</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid ""
"<span class=\"status\">This problem is currently open for "
"participation.</span>"
msgstr "<span class=\"status\">Dieses Problem ist derzeit zur  "
"Teilnahme offen</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "<span class=\"summary\">Share</span>"
msgstr "<span class=\"summary\">Aktie</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "<span id=\"description-char-numbers\">00</span>/200"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "About"
msgstr "Um"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__accepted
msgid "Accepted"
msgstr "Akzeptiert"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Add an Official Update"
msgstr "Fügen Sie ein offizielles Update hinzu"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "All"
msgstr "Alle"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__all_tag_ids
msgid "All Tags"
msgstr "Alle Tags"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__company_ids
msgid "Allowed Companies"
msgstr "Zugelassene Unternehmen"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__attachment_ids
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Attachments"
msgstr "Anhänge"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Attachments <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"
msgstr ""
"Anhänge  <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Attachments<span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"
msgstr ""
"Anhänge  <span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"


#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.custom_snippet
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_search
msgid "CEP Idea"
msgstr "CEP-Idee"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_search
msgid "CEP Idea Comment"
msgstr "CEP-Ideenkommentar"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_official_update
msgid "CEP Idea Official Update"
msgstr "Offizielles Update zur CEP-Idee"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_official_update_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_official_update_search
msgid "CEP Idea Official update"
msgstr "Offizielles Update zur CEP-Idee"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_phase
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_phase_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_phase_search
msgid "CEP Idea Phase"
msgstr "CEP-Ideenphase"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_search
msgid "CEP Idea Problem"
msgstr "CEP-Ideenproblem"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_reaction_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_reaction_search
msgid "CEP Idea Reaction"
msgstr "CEP-Ideenreaktion"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_tag_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_tag_search
msgid "CEP Idea Tag"
msgstr "CEP-Ideen-Tag"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_comment
msgid "Citizen Engagement Platform Comment"
msgstr "Kommentar zur Bürgerengagementplattform"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_idea
msgid "Citizen Engagement Platform Idea"
msgstr "Idee einer Bürgerengagementplattform"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_problem
msgid "Citizen Engagement Platform Problem for Idea"
msgstr "Problem der Bürgerbeteiligungsplattform für Idee"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_reaction
msgid "Citizen Engagement Platform Reaction"
msgstr "Reaktion der Bürgerbeteiligungsplattform"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_tag
msgid "Citizen Engagement Platform Tag"
msgstr "Tag der Bürgerengagementplattform"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Click to select a file"
msgstr "Klicken Sie, um eine Datei auszuwählen"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__comment_id
msgid "Comment"
msgstr "Kommentar"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_comment_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__comment_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_comment
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Comments"
msgstr "Kommentare"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Comments ("
msgstr "Kommentare ("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__cover_photo
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__cover_photo
msgid "Cover Photo"
msgstr "Titelbild"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Cover Photo <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"
msgstr ""
"Titelbild <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"


#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Cover Photo<span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"
msgstr ""
"Titelbild <span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"


#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__create_date
msgid "Create Date"
msgstr "Datum erstellen"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_idea_action
msgid "Create a new CEP idea"
msgstr "Erstellen Sie eine neue CEP-Idee"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_comment_action
msgid "Create a new comment"
msgstr "Erstellen Sie einen neuen Kommentar"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_official_update_action
msgid "Create a new official update"
msgstr "Erstellen Sie ein neues offizielles Update"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_phase_action
msgid "Create a new phase"
msgstr "Erstellen Sie eine neue Phase"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_problem_action
msgid "Create a new problem"
msgstr "Erstellen Sie ein neues Problem"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_reaction_action
msgid "Create a new reaction"
msgstr "Erstellen Sie eine neue Reaktion"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_tag_action
msgid "Create a new tag"
msgstr "Erstellen Sie ein neues Tag"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Current status"
msgstr "Aktueller Stand"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__description
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Description"
msgstr "Beschreibung"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_reaction__type__dislike
msgid "Dislike"
msgstr "Abneigung"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Dislike("
msgstr "Abneigung("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Download Attachments"
msgstr "Anhänge herunterladen"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "Download existing attachment"
msgstr "Vorhandenen Anhang herunterladen"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__end_date
msgid "End Date"
msgstr "Enddatum"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Ends on"
msgstr "Endet am"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Go Back"
msgstr "Geh zurück"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__id
msgid "ID"
msgstr ""

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__idea_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__idea_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__idea_id
msgid "Idea"
msgstr "Idee"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Idea Description"
msgstr "Ideenbeschreibung"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Idea Details"
msgstr "Ideendetails"

#. module: cep_idea
#: model:ir.ui.menu,name:cep_idea.cep_idea_root
#: model:website.menu,name:cep_idea.menu_list_mp
msgid "Idea Generation"
msgstr "Ideengenerierung"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Idea Submit"
msgstr "Idee einreichen"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_idea_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__idea_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Ideas"
msgstr "Ideen"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Image and Attachment"
msgstr "Bild und Anhang"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__implemented
msgid "Implemented"
msgstr "Umgesetzt"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__is_publish
msgid "Is Publish"
msgstr "Ist Veröffentlichen"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_reaction__type__like
msgid "Like"
msgstr "Wie"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Like("
msgstr "Wie("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__location
msgid "Location"
msgstr "Standort"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Make your idea stand out. This\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\timage will be shown at the top of the content."
msgstr ""
"Heben Sie Ihre Idee hervor. Dieses\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\tBild wird oben im Inhalt angezeigt."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Make your idea stand out. This\n"
"                                                    image will be shown at the top of the content."
msgstr ""
"Heben Sie Ihre Idee hervor. Dieses\n"
"Bild wird oben im Inhalt angezeigt."

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__message
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__message
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Message"
msgstr "Nachricht"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Newest"
msgstr "Neueste"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "No Idea submitted yet."
msgstr "Noch keine Idee eingereicht."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Official Update"
msgstr "Offizielles Update"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_official_update_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__official_update_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_official_update
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Official Updates"
msgstr "Offizielle Updates"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Oldest"
msgstr "Älteste"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Other Info"
msgstr "Weitere Informationen"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__owner_id
msgid "Owner"
msgstr "Eigentümer"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__parent_id
msgid "Parent Comment"
msgstr "Elternkommentar"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Participants"
msgstr "Teilnehmer"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__description
msgid "Phase Description"
msgstr "Phasenbeschreibung"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__name
msgid "Phase Name"
msgstr "Phasenname"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_phase_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__phase_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__phase_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_phase
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Phases"
msgstr "Phasen"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Post"
msgstr "Post"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Posted on -"
msgstr "Gepostet am -"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__problem_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__problem_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__problem_id
msgid "Problem"
msgstr "Problem"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__description
msgid "Problem Description"
msgstr "Problembeschreibung"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Problem Details"
msgstr "Problemdetails"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_list
msgid "Problem List"
msgstr "Problemliste"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__title
msgid "Problem Name"
msgstr "Problemname"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.custom_snippet
msgid "Problem, List"
msgstr "Problem, Liste"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_problem_action
#: model:ir.ui.menu,name:cep_idea.cep_idea_problem
msgid "Problems"
msgstr "Probleme"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__proposed
msgid "Proposed"
msgstr "Vorgeschlagen"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Publish"
msgstr "Veröffentlichen"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Publish\n"
"\t\t\t\t\t\t\t\t\t\tyour Idea"
msgstr ""
"Veröffentlichen \n"
"\t\t\t\t\t\t\t\t\t\tSie Ihre Idee"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Publish\n"
"                                        your Idea"
msgstr ""
"Veröffentlichen \n"
"                                        Sie Ihre Idee"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_reaction_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__reaction_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__reaction_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_reaction
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Reactions"
msgstr "Reaktionen"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__rejected
msgid "Rejected"
msgstr "Abgelehnt"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problem_list
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Search ..."
msgstr "Suchen ..."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Select an image (max. 10MB)"
msgstr "Wählen Sie ein Bild aus (max. 10 MB)"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Share"
msgstr "Aktie"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Share your Idea"
msgstr "Teilen Sie Ihre Idee"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__start_date
msgid "Start Date"
msgstr "Startdatum"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__status
msgid "Status"
msgstr "Status"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Submit your idea"
msgstr "Reichen Sie Ihre Idee ein"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Submitted Idea ("
msgstr "Eingereichte Idee ("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__name
msgid "Tag"
msgstr "Etikett"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_tag_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__tag_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__tag_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_tag
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Tags"
msgstr "Schlagworte"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Tags <span class=\"fw-400\">(optional)</span>"
msgstr "Schlagworte <span class=\"fw-400\">(optional)</span>"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__title
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Title"
msgstr "Titel"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__type
msgid "Type"
msgstr "Typ"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__under_consideration
msgid "Under consideration"
msgstr "In Erwägung gezogen"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Upload files to give others more\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\tinformation and context"
msgstr ""
"Laden Sie Dateien hoch, um anderen mehr \n"
"\t\t\t\t\t\t\t\t\t\t\t\t\tInformationen und Kontext zu bieten"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Upload files to give others more\n"
"                                                    information and context"
msgstr ""
"Laden Sie Dateien hoch, um anderen mehr \n"
"Informationen und Kontext zu bieten"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Use Tags"
msgstr "Verwenden Sie Tags"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__viewed
msgid "Viewed"
msgstr "Gesehen"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"What is your\n"
"\t\t\t\t\t\t\t\t\t\t\t\tIdea?"
msgstr ""
"Was ist Ihre \n"
"\t\t\t\t\t\t\t\t\t\t\t\tIdee?"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"What is your\n"
"                                                Idea?"
msgstr ""
"Was ist Ihre \n"
"                                                Idee?"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Write your comment here ..."
msgstr "Schreiben Sie hier Ihren Kommentar ..."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Write your reply here ..."
msgstr "Schreiben Sie hier Ihre Antwort ..."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "idea cover"
msgstr "Ideencover"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "problem cover"
msgstr "Problemabdeckung"
