# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_idea
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20241017\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-13 16:35+0000\n"
"PO-Revision-Date: 2024-12-13 16:35+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "&amp;times;"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "00/80"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Descărcare"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "<span class=\"status\">This problem is closed.</span>"
msgstr "<span class=\"status\">Problema este închisă</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid ""
"<span class=\"status\">This problem is currently open for "
"participation.</span>"
msgstr ""
"<span class=\"status\">Această problemă este deschisă pentru  "
"participare</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "<span class=\"summary\">Share</span>"
msgstr "<span class=\"summary\">Împărtășește</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "<span id=\"description-char-numbers\">00</span>/200"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "About"
msgstr "Despre"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__accepted
msgid "Accepted"
msgstr "Acceptat"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Add an Official Update"
msgstr "Adaugă o actualizare oficială"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "All"
msgstr "Toate"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__all_tag_ids
msgid "All Tags"
msgstr "Toate Etichetele"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__company_ids
msgid "Allowed Companies"
msgstr "Companii permise"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__attachment_ids
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Attachments"
msgstr "Atașamente"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Attachments <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"
msgstr ""
"Atașamente <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Attachments<span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"
msgstr ""
"Atașamente <span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"


#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.custom_snippet
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_search
msgid "CEP Idea"
msgstr "Idee CEP"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_search
msgid "CEP Idea Comment"
msgstr "Comentariu Idee CEP"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_official_update
msgid "CEP Idea Official Update"
msgstr "Actualizare Oficială Idee CEP"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_official_update_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_official_update_search
msgid "CEP Idea Official update"
msgstr "Actualizare Oficială Idee CEP"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_phase
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_phase_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_phase_search
msgid "CEP Idea Phase"
msgstr "Faza de idee a CEP"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_search
msgid "CEP Idea Problem"
msgstr "Problemă Idee CEP"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_reaction_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_reaction_search
msgid "CEP Idea Reaction"
msgstr "Reacție Idee CEP"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_tag_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_tag_search
msgid "CEP Idea Tag"
msgstr "Eticheta de idee CEP"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_comment
msgid "Citizen Engagement Platform Comment"
msgstr "Citizen Engagement Platform Comentariu"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_idea
msgid "Citizen Engagement Platform Idea"
msgstr "Ideea platformei de implicare a cetățenilor"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_problem
msgid "Citizen Engagement Platform Problem for Idea"
msgstr "Problema platformei de implicare a cetățenilor pentru idee"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_reaction
msgid "Citizen Engagement Platform Reaction"
msgstr "Reacția platformei de implicare a cetățenilor"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_tag
msgid "Citizen Engagement Platform Tag"
msgstr "Eticheta platformei Citizen Engagement"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Click to select a file"
msgstr "Apasă pentru a selecta un fișier"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__comment_id
msgid "Comment"
msgstr "Comentariu"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_comment_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__comment_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_comment
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Comments"
msgstr "Comentarii"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Comments ("
msgstr "Comentarii ("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__cover_photo
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__cover_photo
msgid "Cover Photo"
msgstr "Imagine de Copertă"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Cover Photo <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"
msgstr ""
"Imagine de Copertă <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Cover Photo<span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"
msgstr ""
"Imagine de Copertă<span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"


#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__create_date
msgid "Create Date"
msgstr "Creează Dată"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_idea_action
msgid "Create a new CEP idea"
msgstr "Creați o nouă idee CEP"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_comment_action
msgid "Create a new comment"
msgstr "Creează un comentariu nou"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_official_update_action
msgid "Create a new official update"
msgstr "Creează o nouă actualizare oficială"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_phase_action
msgid "Create a new phase"
msgstr "Creați o nouă fază"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_problem_action
msgid "Create a new problem"
msgstr "Creați o nouă problemă"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_reaction_action
msgid "Create a new reaction"
msgstr "Creează o nouă reacție"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_tag_action
msgid "Create a new tag"
msgstr "Creează o etichetă nouă"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Current status"
msgstr "Starea actuală"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__description
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Description"
msgstr "Descriere"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_reaction__type__dislike
msgid "Dislike"
msgstr "Nu-mi place"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Dislike("
msgstr "Nu-mi place("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__display_name
msgid "Display Name"
msgstr "Nume de afișare"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Download Attachments"
msgstr "Descarcă Atașamente"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "Download existing attachment"
msgstr "Descarcă atașamente existente"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__end_date
msgid "End Date"
msgstr "Dată de încheiere"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Ends on"
msgstr "Se termină pe"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Go Back"
msgstr "Mergi înapoi"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__id
msgid "ID"
msgstr ""

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__idea_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__idea_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__idea_id
msgid "Idea"
msgstr "Idee"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Idea Description"
msgstr "Descrierea ideii"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Idea Details"
msgstr "Detalii de idee"

#. module: cep_idea
#: model:ir.ui.menu,name:cep_idea.cep_idea_root
#: model:website.menu,name:cep_idea.menu_list_mp
msgid "Idea Generation"
msgstr "Generarea de idei"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Idea Submit"
msgstr "Trimitere idee"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_idea_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__idea_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Ideas"
msgstr "Idei"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Image and Attachment"
msgstr "Imagine și Atașament"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__implemented
msgid "Implemented"
msgstr "Implementat"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__is_publish
msgid "Is Publish"
msgstr "Este Publicare"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag____last_update
msgid "Last Modified on"
msgstr "Ultima modificare pe"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__write_uid
msgid "Last Updated by"
msgstr "Ultima modificare de"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__write_date
msgid "Last Updated on"
msgstr "Ultima modificare pe"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_reaction__type__like
msgid "Like"
msgstr "Îmi place"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Like("
msgstr "Îmi place("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__location
msgid "Location"
msgstr "Locație"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Make your idea stand out. This\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\timage will be shown at the top of the content."
msgstr ""
"Fă-ți ideea să iasă în evidență. Această\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\timagine va fi afișată în partea de sus a conținutului."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Make your idea stand out. This\n"
"                                                    image will be shown at the top of the content."
msgstr ""
"Fă-ți ideea să iasă în evidență. Această\n"
"imagine va fi afișată în partea de sus a conținutului."
#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__message
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__message
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Message"
msgstr "Mesaj"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Newest"
msgstr "Cel mai nou"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "No Idea submitted yet."
msgstr "Nicio idee depusă."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Official Update"
msgstr "Actualizare oficială"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_official_update_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__official_update_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_official_update
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Official Updates"
msgstr "Actualizări oficiale"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Oldest"
msgstr "Cel mai vechi"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Other Info"
msgstr "Alte informații"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__owner_id
msgid "Owner"
msgstr "Proprietar"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__parent_id
msgid "Parent Comment"
msgstr "Prototip comentariu"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Participants"
msgstr "Participanți"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__description
msgid "Phase Description"
msgstr "Descrierea Fazei"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__name
msgid "Phase Name"
msgstr "Numele Fazei"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_phase_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__phase_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__phase_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_phase
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Phases"
msgstr "Faze"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Post"
msgstr "Postare"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Posted on -"
msgstr "Postat pe"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__problem_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__problem_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__problem_id
msgid "Problem"
msgstr "Problemă"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__description
msgid "Problem Description"
msgstr "Descrierea problemei"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Problem Details"
msgstr "Detalii probleme"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_list
msgid "Problem List"
msgstr "Lista problemelor"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__title
msgid "Problem Name"
msgstr "Numele problemei"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.custom_snippet
msgid "Problem, List"
msgstr "Problemă, listă"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_problem_action
#: model:ir.ui.menu,name:cep_idea.cep_idea_problem
msgid "Problems"
msgstr "Probleme"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__proposed
msgid "Proposed"
msgstr "Propus"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Publish"
msgstr "Publica"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Publish\n"
"\t\t\t\t\t\t\t\t\t\tyour Idea"
msgstr ""
"Publicați-vă \n"
"\t\t\t\t\t\t\t\t\t\tideea"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Publish\n"
"                                        your Idea"
msgstr ""
"Publicați-vă \n"
"ideea"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_reaction_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__reaction_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__reaction_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_reaction
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Reactions"
msgstr "Reacții"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__rejected
msgid "Rejected"
msgstr "Respins"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problem_list
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Search ..."
msgstr "Căutare ..."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Select an image (max. 10MB)"
msgstr "Selectează o imagine (max. 10MB)"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Share"
msgstr "Distribuie"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Share your Idea"
msgstr "Împărtășește-ți ideea"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__start_date
msgid "Start Date"
msgstr "Dată de începere"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__status
msgid "Status"
msgstr "Status"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Submit your idea"
msgstr "Trimiteți-vă ideea"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Submitted Idea ("
msgstr "Ideea trimisă ("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__name
msgid "Tag"
msgstr "Etichetă"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_tag_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__tag_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__tag_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_tag
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Tags"
msgstr "Etichete"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Tags <span class=\"fw-400\">(optional)</span>"
msgstr "Etichete <span class=\"fw-400\">(optional)</span>"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__title
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Title"
msgstr "Titlu"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__type
msgid "Type"
msgstr "Tip"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__under_consideration
msgid "Under consideration"
msgstr "În considerare"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Upload files to give others more\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\tinformation and context"
msgstr ""
"Încarcă fișiere pentru a le oferi celorlalți mai multe\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t informații și context"
#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Upload files to give others more\n"
"                                                    information and context"
msgstr ""
"Încarcă fișiere pentru a le oferi celorlalți mai multe\n"
" informații și context"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Use Tags"
msgstr "Utilizați etichete"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__viewed
msgid "Viewed"
msgstr "Vizualizat"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"What is your\n"
"\t\t\t\t\t\t\t\t\t\t\t\tIdea?"
msgstr ""
"Care este "
"\t\t\t\t\t\t\t\t\t\t\t\tIdeea ta?"
#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"What is your\n"
"                                                Idea?"
msgstr ""
"Care este\n"
"Ideea ta?"
#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Write your comment here ..."
msgstr "Scrie comentariul tau aici"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Write your reply here ..."
msgstr "Scrie raspunsul tău aici"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "idea cover"
msgstr "coperta ideii"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "problem cover"
msgstr "acoperirea problemei"
