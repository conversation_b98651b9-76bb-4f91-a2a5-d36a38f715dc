# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_idea
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20241017\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-04 10:20+0000\n"
"PO-Revision-Date: 2024-12-04 10:20+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "&amp;times;"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "00/80"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Descargar"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "<span class=\"status\">This problem is closed.</span>"
msgstr "<span class=\"status\">Este problema está cerrado.</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid ""
"<span class=\"status\">This problem is currently open for "
"participation.</span>"
msgstr ""
"<span class=\"status\">Este problema está actualmente abierto a la "
"participación.</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "<span class=\"summary\">Share</span>"
msgstr "<span class=\"summary\">Compartir</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "<span id=\"description-char-numbers\">00</span>/200"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "About"
msgstr "Acerca de"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__accepted
msgid "Accepted"
msgstr "Aceptado"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Add an Official Update"
msgstr "Agregar una actualización oficial"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "All"
msgstr "Todo"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__all_tag_ids
msgid "All Tags"
msgstr "Todas las etiquetas"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__company_ids
msgid "Allowed Companies"
msgstr "Empresas permitidas"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__attachment_ids
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Attachments"
msgstr "Adjuntos"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Attachments <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"
msgstr ""

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Attachments<span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"
msgstr ""
"Archivos adjuntos<span class=\"fw-400 opacity-80\">\n"
"                                                        (opcional)</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.custom_snippet
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_search
msgid "CEP Idea"
msgstr "idea del CEP"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_search
msgid "CEP Idea Comment"
msgstr "Comentario de la idea del CEP"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_official_update
msgid "CEP Idea Official Update"
msgstr "Actualización oficial de la idea CEP"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_official_update_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_official_update_search
msgid "CEP Idea Official update"
msgstr "Actualización oficial de la idea CEP"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_phase
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_phase_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_phase_search
msgid "CEP Idea Phase"
msgstr "Fase de ideas del CEP"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_search
msgid "CEP Idea Problem"
msgstr "Problema de idea del CEP"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_reaction_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_reaction_search
msgid "CEP Idea Reaction"
msgstr "Reacción a la idea del CEP"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_tag_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_tag_search
msgid "CEP Idea Tag"
msgstr "Etiqueta de idea de CEP"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_comment
msgid "Citizen Engagement Platform Comment"
msgstr "Comentario de la plataforma de participación ciudadana"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_idea
msgid "Citizen Engagement Platform Idea"
msgstr "Idea de plataforma de participación ciudadana"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_problem
msgid "Citizen Engagement Platform Problem for Idea"
msgstr "Problema de la plataforma de participación ciudadana para la idea"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_reaction
msgid "Citizen Engagement Platform Reaction"
msgstr "Reacción de la plataforma de participación ciudadana"

#. module: cep_idea
#: model:ir.model,name:cep_idea.model_cep_idea_tag
msgid "Citizen Engagement Platform Tag"
msgstr "Etiqueta de plataforma de participación ciudadana"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Click to select a file"
msgstr "Haga clic para seleccionar un archivo"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__comment_id
msgid "Comment"
msgstr "Comentario"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_comment_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__comment_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_comment
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Comments"
msgstr "Comentarios"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Comments ("
msgstr "Comentarios ("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__cover_photo
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__cover_photo
msgid "Cover Photo"
msgstr "Foto de portada"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Cover Photo <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(optional)</span>"
msgstr ""
"Foto de portada <span class=\"fw-400 opacity-80\">\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\t\t(opcional)</span>"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Cover Photo<span class=\"fw-400 opacity-80\">\n"
"                                                        (optional)</span>"
msgstr ""
"Foto de portada<span class=\"fw-400 opacity-80\">\n"
"                                                        (opcional)</span>"



#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__create_date
msgid "Create Date"
msgstr "Crear fecha"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_idea_action
msgid "Create a new CEP idea"
msgstr "Crear una nueva idea de CEP"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_comment_action
msgid "Create a new comment"
msgstr "Crear un nuevo comentario"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_official_update_action
msgid "Create a new official update"
msgstr "Crear una nueva actualización oficial"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_phase_action
msgid "Create a new phase"
msgstr "Crear una nueva fase"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_problem_action
msgid "Create a new problem"
msgstr "Crear un nuevo problema"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_reaction_action
msgid "Create a new reaction"
msgstr "Crea una nueva reacción"

#. module: cep_idea
#: model_terms:ir.actions.act_window,help:cep_idea.cep_idea_tag_action
msgid "Create a new tag"
msgstr "Crear una nueva etiqueta"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__create_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__create_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__create_date
msgid "Created on"
msgstr "Creado el"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Current status"
msgstr "Estado actual"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__description
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Description"
msgstr "Descripción"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_reaction__type__dislike
msgid "Dislike"
msgstr "Aversión"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Dislike("
msgstr "Aversión("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__display_name
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__display_name
msgid "Display Name"
msgstr "Nombre para mostrar"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Download Attachments"
msgstr "Descargar archivos adjuntos"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid "Download existing attachment"
msgstr "Descargar archivo adjunto existente"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__end_date
msgid "End Date"
msgstr "Fecha de finalización"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Ends on"
msgstr "Termina el"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Go Back"
msgstr "Volver"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__id
msgid "ID"
msgstr ""

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__idea_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__idea_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__idea_id
msgid "Idea"
msgstr "Idea"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Idea Description"
msgstr "Descripción de la idea"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Idea Details"
msgstr "Detalles de la idea"

#. module: cep_idea
#: model:ir.ui.menu,name:cep_idea.cep_idea_root
#: model:website.menu,name:cep_idea.menu_list_mp
msgid "Idea Generation"
msgstr "Generación de ideas"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Idea Submit"
msgstr "Enviar una idea"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_idea_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__idea_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Ideas"
msgstr "Ideas"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Image and Attachment"
msgstr "Imagen y archivo adjunto"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__implemented
msgid "Implemented"
msgstr "Implementado"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__is_publish
msgid "Is Publish"
msgstr "es publicar"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction____last_update
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag____last_update
msgid "Last Modified on"
msgstr "Última actualización el"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__write_uid
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__write_uid
msgid "Last Updated by"
msgstr "Actualizado por última vez por"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__write_date
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_reaction__type__like
msgid "Like"
msgstr "Como"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Like("
msgstr "Como("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__location
msgid "Location"
msgstr "Ubicación"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Make your idea stand out. This\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\timage will be shown at the top of the content."
msgstr ""
"Haz que tu idea destaque. Esta imagen\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\tse mostrará en la parte superior del contenido."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Make your idea stand out. This\n"
"                                                    image will be shown at the top of the content."
msgstr ""
"Haz que tu idea destaque. Esta imagen\n"
"                                                    se mostrará en la parte superior del contenido."

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__message
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__message
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Message"
msgstr "Mensaje"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Newest"
msgstr "El más nuevo"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "No Idea submitted yet."
msgstr "Aún no se ha enviado ninguna idea."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Official Update"
msgstr "Actualización oficial"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_official_update_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__official_update_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_official_update
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Official Updates"
msgstr "Actualizaciones oficiales"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Oldest"
msgstr "más antiguo"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Other Info"
msgstr "Otra información"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_official_update__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__owner_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__owner_id
msgid "Owner"
msgstr "Dueño"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__parent_id
msgid "Parent Comment"
msgstr "Comentario de los padres"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Participants"
msgstr "Participantes"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__description
msgid "Phase Description"
msgstr "Descripción de la fase"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__name
msgid "Phase Name"
msgstr "Nombre de fase"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_phase_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__phase_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__phase_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_phase
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Phases"
msgstr "Fases"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Post"
msgstr "correo"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Posted on -"
msgstr "publicado en -"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__problem_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__problem_id
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__problem_id
msgid "Problem"
msgstr "Problema"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__description
msgid "Problem Description"
msgstr "Descripción del problema"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Problem Details"
msgstr "Detalles del problema"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_list
msgid "Problem List"
msgstr "Lista de problemas"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__title
msgid "Problem Name"
msgstr "Nombre del problema"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.custom_snippet
msgid "Problem, List"
msgstr "Problema, Lista"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_problem_action
#: model:ir.ui.menu,name:cep_idea.cep_idea_problem
msgid "Problems"
msgstr "Problemas"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__proposed
msgid "Proposed"
msgstr "Propuesto"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Publish"
msgstr "Publica"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Publish\n"
"\t\t\t\t\t\t\t\t\t\tyour idea"
msgstr ""
"Publish\n"
"\t\t\t\t\t\t\t\t\t\ttu idea"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Publish\n"
"                                        your idea"
msgstr ""
"Publica\n"
"                                        tu idea"
 
#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_reaction_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_comment__reaction_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__reaction_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_reaction
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_comment_form
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_idea_form
msgid "Reactions"
msgstr "Reacciones"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__rejected
msgid "Rejected"
msgstr "Rechazado"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problem_list
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Search ..."
msgstr "Buscar ..."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Select an image (max. 10MB)"
msgstr "Seleccione una imagen (máximo. 10MB)"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Share"
msgstr "Compartir"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Share your Idea"
msgstr "Comparte tu idea"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_phase__start_date
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__status
msgid "Status"
msgstr "Estado"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Submit your idea"
msgstr "Envía tu idea"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "Submitted Idea ("
msgstr "Idea enviada ("

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_tag__name
msgid "Tag"
msgstr "Etiqueta"

#. module: cep_idea
#: model:ir.actions.act_window,name:cep_idea.cep_idea_tag_action
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__tag_ids
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_problem__tag_ids
#: model:ir.ui.menu,name:cep_idea.cep_idea_tag
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
#: model_terms:ir.ui.view,arch_db:cep_idea.view_cep_idea_problem_form
msgid "Tags"
msgstr "Etiquetas"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Tags <span class=\"fw-400\">(optional)</span>"
msgstr "Etiquetas <span class=\"fw-400\">(opcional)</span>"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_idea__title
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Title"
msgstr "Título"

#. module: cep_idea
#: model:ir.model.fields,field_description:cep_idea.field_cep_idea_reaction__type
msgid "Type"
msgstr "Tipo"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__under_consideration
msgid "Under consideration"
msgstr "Está siendo considerado"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"Upload files to give others more\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\tinformation and context"
msgstr ""
"Cargue archivos para brindar a otros más\n"
"\t\t\t\t\t\t\t\t\t\t\t\t\tinformación y contexto."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"Upload files to give others more\n"
"                                                    information and context"
msgstr ""
"Cargue archivos para brindar a otros más\n"
"                                                    información y contexto."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid "Use Tags"
msgstr "Usar etiquetas"

#. module: cep_idea
#: model:ir.model.fields.selection,name:cep_idea.selection__cep_idea_idea__status__viewed
msgid "Viewed"
msgstr "Visto"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_edit
msgid ""
"What is your\n"
"\t\t\t\t\t\t\t\t\t\t\t\tIdea?"
msgstr ""
"¿Cuál es tu\n"
"\t\t\t\t\t\t\t\t\t\t\t\tIdea?"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_new
msgid ""
"What is your\n"
"                                                Idea?"
msgstr ""
"¿Cuál es tu\n"
"                                                Idea?"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Write your comment here ..."
msgstr "Escribe tu comentario aquí..."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "Write your reply here ..."
msgstr "Escribe tu respuesta aquí..."

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.ideas_view
msgid "idea cover"
msgstr "portada de ideas"

#. module: cep_idea
#: model_terms:ir.ui.view,arch_db:cep_idea.problems_view
msgid "problem cover"
msgstr "cubierta del problema"
