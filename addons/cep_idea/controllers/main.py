from odoo import http
from odoo.http import request
import json
import base64


def isAuthenticate():
    return request.env.user.id != request.website.user_id


def isIdeaOwner(owner_id):
    return owner_id.id == request.env.user.id


class ProblemController(http.Controller):

    @http.route('/problems', type='http', auth='public', website=True)
    def list(self, **kwargs):
        problems = request.env['cep.idea.problem'].sudo().search(
            [('is_publish', '=', True)])
        context = {'problems': problems}
        return request.render('cep_idea.problems_list', context)

    @http.route(['/problems/<string:id>/view'], type='http', auth='public', website=True)
    def view(self, id, **kwargs):
        problem = request.env['cep.idea.problem'].browse(int(id)).sudo()
        phaseswise_ideas = problem.get_phaseswise_ideas()
        idea_count = len(phaseswise_ideas[0]['ideas'])
        context = {
            'problem': problem,
            'phases': phaseswise_ideas,
            'idea_count': idea_count,
        }
        return request.render('cep_idea.problems_view', context)

    # Get problem list by page number API
    @http.route('/problems/problem-list/<int:page_no>/<int:per_page>', type='json', auth='public', methods=['POST'], website=True)
    def get_problem_list_by_page(self, page_no, per_page, **kwargs):
        print('page_no------', page_no)
        # per_page = 4
        print('per_page------', per_page)
        offset = (page_no - 1) * per_page
        if offset < 0:
            offset = 0
        problems = request.env['cep.idea.problem'].sudo().search(
            [('is_publish', '=', True)], offset=offset, limit=per_page)
        problem_list = []
        for problem in problems:
            problem_object = {
                "problem_id": problem.id,
                "problem_title": problem.title,
                "problem_description": problem.description,
                "problem_cover_photo": problem.cover_photo.decode("utf-8") if problem.cover_photo else "",
                "problem_created_by": problem.owner_id.name,
                # DD MMM YYYY (01 Jan 2020)
                "problem_created_date": problem.create_date.strftime("%d %b %Y"),
                # DD MMM YYYY (01 Jan 2020)
                "problem_end_date": problem.get_end_date().strftime("%d %b %Y"),
            }
            problem_list.append(problem_object)

        # get number of problems
        problem_count = request.env['cep.idea.problem'].sudo(
        ).search_count([('is_publish', '=', True)])

        total_pages = (problem_count + per_page - 1) // per_page

        return {
            "status": "success",
            "problems": problem_list,
            "problem_count": problem_count,
            "total_pages": total_pages,
        }

    # Get Ideas by Phase ID API
    @http.route('/problems/get-ideas-by-phase', type='json', auth='public', methods=['POST'], website=True)
    def get_ideas_by_phase(self, **kwargs):
        phase_id = kwargs['phase_id']
        print('phase_id------', phase_id)
        phase = request.env['cep.idea.phase'].sudo().search(
            [('id', '=', phase_id)], limit=1)
        phase_object = {
            "phase_name": phase.name,
            "phase_description": phase.description,
            "phase_start_date": phase.start_date.strftime('%d %b %Y'),
            "phase_end_date": phase.end_date.strftime('%d %b %Y'),
        }

        ideas = request.env['cep.idea.idea'].sudo().search(
            [('phase_ids', '=', int(phase_id))])
        idea_list = []
        for idea in ideas:
            create_date_str = idea.create_date.strftime(
                "%d %b %Y")  # DD MMM YYYY (01 Jan 2020)
            limited_description = idea.description[:48]
            idea_object = {
                "idea_id": idea.id,
                "idea_title": idea.title,
                "idea_description": limited_description,
                "idea_status": idea.status,
                "idea_creation_date": create_date_str,
                "idea_cover_photo": idea.cover_photo.decode("utf-8") if idea.cover_photo else "",
                "idea_created_by": idea.owner_id.name,
                "idea_likes": idea.count_like(),
                "idea_dislikes": idea.count_dislike(),
            }
            idea_list.append(idea_object)

        return {
            "status": "success",
            "phase": phase_object,
            "ideas": idea_list
        }

    # Idea Search API
    @http.route('/problems/ideas/search', type='json', auth='public', methods=['POST'], website=True)
    def idea_search(self, **kwargs):
        keyword = kwargs['keyword']
        print('keyword------', keyword)
        phase_id = kwargs['phase_id']
        print('phase_id------', phase_id)
        ideas = request.env['cep.idea.idea'].sudo().search([
            '|',
            ('title', 'ilike', keyword),
            ('description', 'ilike', keyword),
            ('phase_ids', '=', int(phase_id))
        ])
        idea_list = []
        for idea in ideas:
            create_date_str = idea.create_date.strftime(
                "%d %b %Y")  # DD MMM YYYY (01 Jan 2020)
            limited_description = idea.description[:48]
            idea_object = {
                "idea_id": idea.id,
                "idea_title": idea.title,
                "idea_description": limited_description,
                "idea_status": idea.status,
                "idea_creation_date": create_date_str,
                "idea_cover_photo": idea.cover_photo.decode("utf-8") if idea.cover_photo else "",
                "idea_created_by": idea.owner_id.name,
                "idea_likes": idea.count_like(),
                "idea_dislikes": idea.count_dislike(),
            }
            idea_list.append(idea_object)

        return {
            "status": "success",
            "ideas": idea_list
        }

    # Problem Search API
    @http.route('/problems/search', type='json', auth='public', methods=['POST'], website=True)
    def problem_search(self, **kwargs):
        keyword = kwargs['keyword']
        print('keyword------', keyword)
        problems = request.env['cep.idea.problem'].sudo().search([
            '|',
            ('title', 'ilike', keyword),
            ('description', 'ilike', keyword),
            ('is_publish', '=', True),
        ])
        problem_list = []
        for problem in problems:
            problem_object = {
                "problem_id": problem.id,
                "problem_title": problem.title,
                "problem_description": problem.description,
                "problem_cover_photo": problem.cover_photo.decode("utf-8") if problem.cover_photo else "",
                "problem_created_by": problem.owner_id.name,
                # DD MMM YYYY (01 Jan 2020)
                "problem_created_date": problem.create_date.strftime("%d %b %Y"),
                # DD MMM YYYY (01 Jan 2020)
                "problem_end_date": problem.get_end_date().strftime("%d %b %Y"),
            }
            problem_list.append(problem_object)

        # get number of problems
        problem_count = len(problem_list)

        return {
            "status": "success",
            "problems": problem_list,
            "problem_count": problem_count
        }

    # Problem Search suggestions API
    @http.route('/problems/search_suggestions', type='json', auth='public', methods=['POST'], website=True)
    def problem_suggestions(self, **kwargs):
        keyword = kwargs['keyword']
        problems = request.env['cep.idea.problem'].sudo().search([
            '|',
            ('title', 'ilike', keyword),
            ('description', 'ilike', keyword),
            ('is_publish', '=', True),
        ])

        problem_list = []
        for problem in problems:
            problem_object = {
                "problem_id": problem.id,
                "problem_title": problem.title,
            }
            problem_list.append(problem_object)

        return {
            "status": "success",
            "problems": problem_list,
        }


class IdeaController(http.Controller):

    # New Idea Submit View
    @http.route(['/problems/<string:problem_id>/phases/<string:phase_id>/ideas/new'], type='http', auth='public', website=True)
    def new(self, problem_id, phase_id, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')

        # get tags by problem id
        tags = request.env['cep.idea.tag'].sudo().search([('problem_id', '=', int(problem_id))])
        return request.render('cep_idea.ideas_new', {
            'problem_id': problem_id,
            'phase_id': phase_id,
            'tags': [ { 'id': tag.id, 'name': tag.name } for tag in tags ],
        })

    # New Idea Submit
    @http.route(['/problems/<string:problem_id>/phases/<string:phase_id>/ideas/create'], type='http', auth='public', website=True)
    def create(self, problem_id, phase_id, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')

        kwargs['problem_id'] = int(problem_id)
        kwargs['owner_id'] = request.env.user.id
        kwargs['phase_ids'] = [(6, 0, [int(phase_id)])]
        cover_photo = request.httprequest.files.get('cover_photo')
        kwargs['cover_photo'] = base64.b64encode(cover_photo.read())
        attachments = []
        if 'attachment' in kwargs and kwargs['attachment']:
          attachments = request.httprequest.files.getlist('attachment')
        
        del kwargs['attachment']
           


        if len(kwargs['all_tag_ids']) > 0:
            tag_ids = []
            tag_names = []

            all_tag_ids = json.loads(kwargs['all_tag_ids'])
            for tag in all_tag_ids:
                tag_ids.append(tag['id'])
                tag_names.append(tag['name'])

            kwargs['all_tag_ids'] = json.dumps(tag_names)
            kwargs['tag_ids'] = [(6, 0, tag_ids)]


        idea = request.env['cep.idea.idea'].sudo().create(kwargs)

        # # Handling the attachments
        attachment_ids = []
        if len(attachments) > 0:
            for attachment in attachments:
                attachment_data = {
                    'name': attachment.filename,
                    'datas': base64.b64encode(attachment.read()),
                    'res_model': 'cep.idea.idea',
                    'res_id': idea.id,
                }
                attachment_id = request.env['ir.attachment'].sudo().create(attachment_data)
                attachment_ids.append(attachment_id.id)

        if attachment_ids:
            idea.sudo().write({'attachment_ids': [(6, 0, attachment_ids)]})


        return request.redirect('/ideas/{}/view'.format(idea.id))

    @http.route(['/ideas/edit/<string:id>'], type='http', auth='public', website=True)
    def edit(self, id, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        idea = request.env['cep.idea.idea'].browse(int(id)).sudo()
        if not isIdeaOwner(idea.owner_id):
            return request.redirect('/my/ideas')
        if not idea.exists():
            return request.not_found()

        problem_id = idea.problem_id.id
        tags = request.env['cep.idea.tag'].sudo().search([('problem_id', '=', int(problem_id))])

        context = {
            'idea': idea,
            'tags': [ { 'id': tag.id, 'name': tag.name } for tag in tags ],
            'csrf_token': request.csrf_token()
        }
        return request.render('cep_idea.ideas_edit', context)

    @http.route(['/ideas/update/<int:id>'], type='http', auth='public', website=True)
    def update(self, id, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')

        idea = request.env['cep.idea.idea'].browse(int(id)).sudo()

        if not isIdeaOwner(idea.owner_id):
            return request.redirect('/my/ideas')

        if not idea.exists():
            return request.not_found()

        if 'cover_photo' in kwargs and kwargs['cover_photo']:
            kwargs['cover_photo'] = base64.b64encode(
                kwargs['cover_photo'].read())
        else:
            kwargs['cover_photo'] = idea.cover_photo
    
        attachments = []
        if 'attachment' in kwargs and kwargs['attachment']:
          attachments = request.httprequest.files.getlist('attachment')
        
        del kwargs['attachment']
          

        if len(kwargs['all_tag_ids']) > 0:
            tag_ids = []
            tag_names = []
            
            all_tag_ids = json.loads(kwargs['all_tag_ids'])
            for tag in all_tag_ids:
                tag_ids.append(tag['id'])
                tag_names.append(tag['name'])

            kwargs['all_tag_ids'] = json.dumps(tag_names)
            kwargs['tag_ids'] = [(6, 0, tag_ids)]

        idea.write(kwargs)

         # # Handling the attachments
        attachment_ids = []
        if len(attachments) > 0:
            for attachment in attachments:
                attachment_data = {
                    'name': attachment.filename,
                    'datas': base64.b64encode(attachment.read()),
                    'res_model': 'cep.idea.idea',
                    'res_id': idea.id,
                }
                attachment_id = request.env['ir.attachment'].sudo().create(attachment_data)
                attachment_ids.append(attachment_id.id)
        
        if attachment_ids:
            idea.write({'attachment_ids': [(6, 0, attachment_ids)]})


        return request.redirect('/ideas/{}/view'.format(idea.id))

    # View Idea Detail
    @http.route(['/ideas/<string:id>/view'], type='http', auth='public', website=True)
    def view(self, id, **kwargs):
        idea = request.env['cep.idea.idea'].browse(int(id)).sudo()
        problem = idea.problem_id

        # Get the filter type from the query parameters
        # Default to 'newest' if no filter is provided
        filter_type = kwargs.get('filter', 'newest')

        comment_count = len(idea.get_parent_comments())
        context = {
            'idea': idea,
            'user': request.env.user,
            'problem': problem,
            'comment_count': comment_count,
            'isIdeaOwner': isIdeaOwner(idea.owner_id),
            'filterType': filter_type,
        }
        return request.render('cep_idea.ideas_view', context)

    @http.route(['/ideas/<string:id>/delete'], type='http', auth='public', website=True)
    def delete_idea(self, id, **kwargs):
        idea = request.env['cep.idea.idea'].browse(int(id)).sudo()

        if not isIdeaOwner(idea.owner_id):
            return request.redirect('/web/login')

        idea.unlink()
        return request.redirect('/my/ideas')


class CommentController(http.Controller):

    @http.route(['/ideas/<string:id>/comments/<string:parent_id>/create'], type='http', auth='public', website=True)
    def create(self, id, parent_id, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        kwargs['idea_id'] = int(id)
        kwargs['owner_id'] = request.env.user.id
        if parent_id != 'None':
            kwargs['parent_id'] = int(parent_id)

        request.env['cep.idea.comment'].sudo().create(kwargs)
        return request.redirect('/ideas/{}/view'.format(id))

    @http.route(['/comment/<string:id>/delete'], type='json', methods=['POST'], auth='public', website=True)
    def delete(self, id, **kwargs):
        if not isAuthenticate():
            return {'error': 'not_authenticated', 'redirect_url': '/web/login'}

        try:
            comment = request.env['cep.idea.comment'].browse(int(id)).sudo()
            if comment:
                comment.unlink()
                return {'success': True}
            else:
                return {'error': 'Comment not found'}
        except Exception as e:
            return {'error': str(e)}


class OfficialUpdateController(http.Controller):

    @http.route(['/ideas/<string:id>/official-update/create'], type='http', auth='public', website=True)
    def create(self, id, **kwargs):
        kwargs['idea_id'] = int(id)
        kwargs['owner_id'] = request.env.user.id

        request.env['cep.idea.official.update'].sudo().create(kwargs)
        return request.redirect('/ideas/{}/view'.format(id))


class ReactionController(http.Controller):

    @http.route('/<string:model>/<string:id>/reactions/<string:type>/create', type='json', auth='public', methods=['POST'], website=True)
    def create(self, model, id, type, **kwargs):
        if not isAuthenticate():
            return {'error': 'not_authenticated', 'redirect_url': '/web/login'}

        kwargs['type'] = str(type)
        kwargs['owner_id'] = request.env.user.id

        if model == 'ideas':
            kwargs['idea_id'] = int(id)
            reaction = request.env['cep.idea.reaction'].sudo().search(
                [('type', '=', kwargs['type']), ('idea_id', '=', kwargs['idea_id']), ('owner_id', '=', kwargs['owner_id'])])
            if len(reaction) > 0:
                pass
            else:
                if kwargs['type'] == 'like':
                    dislike = request.env['cep.idea.reaction'].sudo().search(
                        [('type', '=', 'dislike'), ('idea_id', '=', kwargs['idea_id']), ('owner_id', '=', kwargs['owner_id'])], limit=1)
                    if dislike:
                        dislike.unlink()
                else:
                    like = request.env['cep.idea.reaction'].sudo().search(
                        [('type', '=', 'like'), ('idea_id', '=', kwargs['idea_id']), ('owner_id', '=', kwargs['owner_id'])], limit=1)
                    if like:
                        like.unlink()

                request.env['cep.idea.reaction'].sudo().create(kwargs)

            likes_count = request.env['cep.idea.reaction'].sudo().search_count(
                [('type', '=', 'like'), ('idea_id', '=', kwargs['idea_id'])])
            dislikes_count = request.env['cep.idea.reaction'].sudo().search_count(
                [('type', '=', 'dislike'), ('idea_id', '=', kwargs['idea_id'])])
        elif model == 'ideas_comments':
            kwargs['comment_id'] = int(id)
            reaction = request.env['cep.idea.reaction'].sudo().search([('type', '=', kwargs['type']), (
                'comment_id', '=', kwargs['comment_id']), ('owner_id', '=', kwargs['owner_id'])])
            if len(reaction) > 0:
                pass
            else:
                if kwargs['type'] == 'like':
                    dislike = request.env['cep.idea.reaction'].sudo().search([('type', '=', 'dislike'), (
                        'comment_id', '=', kwargs['comment_id']), ('owner_id', '=', kwargs['owner_id'])], limit=1)
                else:
                    dislike = request.env['cep.idea.reaction'].sudo().search([('type', '=', 'like'), (
                        'comment_id', '=', kwargs['comment_id']), ('owner_id', '=', kwargs['owner_id'])], limit=1)

                if dislike:
                    dislike.unlink()
                request.env['cep.idea.reaction'].sudo().create(kwargs)

            likes_count = request.env['cep.idea.reaction'].sudo().search_count(
                [('type', '=', 'like'), ('comment_id', '=', kwargs['comment_id'])])
            dislikes_count = request.env['cep.idea.reaction'].sudo().search_count(
                [('type', '=', 'dislike'), ('comment_id', '=', kwargs['comment_id'])])

        return {
            "likes_count": likes_count,
            "dislikes_count": dislikes_count
        }
