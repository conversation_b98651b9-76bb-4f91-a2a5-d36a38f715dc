# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import api, models, fields

from datetime import datetime
from dateutil.relativedelta import relativedelta

class Comment(models.Model):
    _name = 'cep.idea.comment'
    _description = 'Citizen Engagement Platform Comment'
    _rec_name = 'message'

    message = fields.Text('Message', required=True, translate=True)
    create_date = fields.Datetime(string='Create Date', default=lambda self: fields.Datetime.now(), readonly=True)
    owner_id = fields.Many2one('res.users', string='Owner', index=True, tracking=True, default=lambda self: self.env.user)
    idea_id = fields.Many2one('cep.idea.idea', required=True, ondelete='cascade')
    parent_id = fields.Many2one('cep.idea.comment', required=False, string='Parent Comment', index=True, ondelete='cascade')
    reaction_ids = fields.One2many('cep.idea.reaction', 'comment_id', string='Reactions')

    def get_reply_comments(self):
        reply_comments = self.search([('parent_id', '=', self.id)])
        return reply_comments
    
    def count_like(self):
        like = self.reaction_ids.search([('type', '=', 'like'), ('comment_id', '=', self.id)])
        return len(like)
    
    def count_dislike(self):
        dislike = self.reaction_ids.search([('type', '=', 'dislike'), ('comment_id', '=', self.id)])
        return len(dislike)