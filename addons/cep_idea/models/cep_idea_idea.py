# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
import uuid
from odoo import api, fields, models


class Idea(models.Model):
    _name = "cep.idea.idea"
    _description = "Citizen Engagement Platform Idea"
    _rec_name = 'title'

    title = fields.Char('Title', required=True, translate=True)
    description = fields.Text('Description', required=True, translate=True)
    cover_photo = fields.Binary(string="Cover Photo", attachment=False) 
    attachment_ids = fields.One2many(
        'ir.attachment', 'res_id', domain=[('res_model', '=', 'cep.idea.idea')],
        string='Attachments', ondelete='cascade')
        
    status = fields.Selection([
        ('proposed', 'Proposed'),
        ('viewed', 'Viewed'),
        ('under consideration', 'Under consideration'),
        ('accepted', 'Accepted'),
        ('rejected', 'Rejected'),
        ('implemented', 'Implemented'),
    ], string='Status', default='proposed', required=True)
    create_date = fields.Datetime(
        string='Create Date', default=lambda self: fields.Datetime.now(), readonly=True)
    problem_id = fields.Many2one('cep.idea.problem', required=True)
    phase_ids = fields.Many2many(
        'cep.idea.phase', string='Phases', domain="[('problem_id', '=', problem_id)]")
    owner_id = fields.Many2one('res.users', string='Owner', index=True,
                               tracking=True, default=lambda self: self.env.user)
    tag_ids = fields.Many2many(
        'cep.idea.tag', string='Tags', domain="[('problem_id', '=', problem_id)]")
    all_tag_ids = fields.Char('All Tags', store=False)
    comment_ids = fields.One2many(
        'cep.idea.comment', 'idea_id', string='Comments')
    reaction_ids = fields.One2many(
        'cep.idea.reaction', 'idea_id', string='Reactions')
    official_update_ids = fields.One2many(
        'cep.idea.official.update', 'idea_id', string='Official Updates')

    def get_parent_comments(self, filter='newest'):
        parent_comments = self.comment_ids.search(
            [('parent_id', '=', False), ('idea_id', '=', self.id)])

        if filter == 'newest':
            parent_comments = parent_comments.sorted(
                key=lambda c: c.create_date, reverse=True)[:5]
        elif filter == 'oldest':
            parent_comments = parent_comments.sorted(
                key=lambda c: c.create_date)[:5]
        elif filter == 'all':
            parent_comments = parent_comments

        return parent_comments

    def get_child_comments(self, parent_id):
        child_comments = self.comment_ids.search(
            [('parent_id', '=', parent_id), ('idea_id', '=', self.id)])
        return child_comments

    def format_create_date(self):
        format_date = self.create_date.strftime('%B %d, %Y')
        return format_date

    def count_like(self):
        like = self.reaction_ids.search(
            [('type', '=', 'like'), ('idea_id', '=', self.id)])
        return len(like)

    def count_dislike(self):
        dislike = self.reaction_ids.search(
            [('type', '=', 'dislike'), ('idea_id', '=', self.id)])
        return len(dislike)

    def last_official_update(self):
        last_official_update = self.official_update_ids.search(
            [('idea_id', '=', self.id)], order='id desc', limit=1)
        return last_official_update