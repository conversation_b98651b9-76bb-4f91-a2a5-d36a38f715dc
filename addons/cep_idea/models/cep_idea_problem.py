# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class Problem(models.Model):
    _name = 'cep.idea.problem'
    _description = 'Citizen Engagement Platform Problem for Idea'
    _rec_name = 'title'

    title = fields.Char('Problem Name', required=True, translate=True)
    description = fields.Html('Problem Description', required=True, translate=True)
    cover_photo = fields.Image("Cover Photo")
    location = fields.Char('Location', required=True)
    idea_ids = fields.One2many('cep.idea.idea', 'problem_id', string='Ideas')
    phase_ids = fields.One2many('cep.idea.phase', 'problem_id', string='Phases')
    tag_ids = fields.One2many('cep.idea.tag', 'problem_id', string='Tags')
    owner_id = fields.Many2one('res.users', string='Owner', index=True, tracking=True, default=lambda self: self.env.user)
    company_ids = fields.Many2many('res.company', required=True, default=lambda self: self.env.user.company_ids, string="Allowed Companies")
    is_publish = fields.Boolean(string='Is Publish', default=False)

    def get_phaseswise_ideas(self):
        phaseswise_ideas = []
        for phase in self.phase_ids:
            obj = {}
            obj['phase'] = phase
            obj['ideas'] = []
            for idea in self.idea_ids:
                if phase in idea.phase_ids:
                    obj['ideas'].append(idea)
            phaseswise_ideas.append(obj)
        return phaseswise_ideas
    
    def get_end_date(self):
        return self.phase_ids[-1].end_date
    
    def get_phase_count(self):
        return len(self.phase_ids)
    
    def get_participant_count(self):
        participant = set()
        for idea in self.idea_ids:
            participant.add(idea.owner_id)
        return len(participant)
    
    @api.constrains('phase_ids')
    def _check_phase_ids(self):
        for problem in self:
            if not problem.phase_ids:
                raise ValidationError("A problem must have at least one phase.")
            
    @api.constrains('tag_ids')
    def _check_tag_ids(self):
        for problem in self:
            if not problem.tag_ids:
                raise ValidationError("A problem must have at least one tag.")
            
    @api.constrains('phase_ids')
    def _check_phase_ids_limit(self):
        max_phases = 5
        for problem in self:
            if len(problem.phase_ids) > max_phases:
                raise ValidationError("You cannot add more than 5 phases to a problem.")