# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import models, fields

class Reaction(models.Model):
    _name = 'cep.idea.reaction'
    _description = 'Citizen Engagement Platform Reaction'
    _rec_name = 'type'

    type = fields.Selection([
        ('like', 'Like'),
        ('dislike', 'Dislike'),
    ], string='Type', required=True)
    owner_id = fields.Many2one('res.users', string='Owner', index=True, tracking=True, default=lambda self: self.env.user)
    idea_id = fields.Many2one('cep.idea.idea', string='Idea', required=False)
    comment_id = fields.Many2one('cep.idea.comment', string='Comment', required=False)