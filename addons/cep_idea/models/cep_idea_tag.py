# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import models, fields

class Tag(models.Model):
    _name = 'cep.idea.tag'
    _description = 'Citizen Engagement Platform Tag'
    _rec_name = 'name'

    name = fields.Char('Tag', required=True)
    owner_id = fields.Many2one('res.users', string='Owner', index=True, tracking=True, default=lambda self: self.env.user)
    problem_id = fields.Many2one('cep.idea.problem', required=True)