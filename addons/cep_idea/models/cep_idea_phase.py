# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import models, fields, api
from odoo.exceptions import UserError
from datetime import timedelta


class Phase(models.Model):
    _name = 'cep.idea.phase'
    _description = 'CEP Idea Phase'
    _rec_name = 'name'        

    name = fields.Char('Phase Name', required=True, size=50, translate=True)
    description = fields.Text('Phase Description', required=True, translate=True)
    start_date = fields.Date('Start Date', required=True, compute='_compute_start_date', store=True, inverse='_set_start_date')
    end_date = fields.Date('End Date', required=True)
    problem_id = fields.Many2one('cep.idea.problem', required=True)
    owner_id = fields.Many2one('res.users', string='Owner', index=True, tracking=True, default=lambda self: self.env.user)

    def is_deadline_pass(self):
        return self.end_date < fields.Date.today()

    @api.depends('problem_id')
    def _compute_start_date(self):
        for phase in self:
            latest_end_date = False
            for related_phase in phase.problem_id.phase_ids:
                if related_phase != phase:
                    if not latest_end_date or related_phase.end_date > latest_end_date:
                        latest_end_date = related_phase.end_date
            if latest_end_date:
                phase.start_date = latest_end_date + timedelta(days=1) 
            else:
                phase.start_date = fields.Date.today()

    def _set_start_date(self):
            pass
    
    @api.constrains('start_date', 'end_date')
    def _check_dates(self):
        for phase in self:
            if phase.start_date >= phase.end_date:
                raise UserError("End Date must be greater than Start Date for Phase")
        
    
    