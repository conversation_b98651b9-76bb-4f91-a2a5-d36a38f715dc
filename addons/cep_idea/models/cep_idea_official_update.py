# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import models, fields

class OfficialUpdate(models.Model):
    _name = 'cep.idea.official.update'
    _description = 'CEP Idea Official Update'
    _rec_name = 'message'

    message = fields.Text('Message', required=True, translate=True)
    create_date = fields.Datetime(string='Create Date', default=lambda self: fields.Datetime.now(), readonly=True)
    idea_id = fields.Many2one('cep.idea.idea', string='Idea')
    owner_id = fields.Many2one('res.users', string='Owner', index=True, tracking=True, default=lambda self: self.env.user)

    def format_create_date(self):
        format_date = self.create_date.strftime('%B %d, %Y')
        return format_date