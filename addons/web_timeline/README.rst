============
Web timeline
============

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:83336483b07a21cb0d427e4961bc70735ff1f8d7200f4faf488c3b509e683d6c
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Production%2FStable-green.png
    :target: https://odoo-community.org/page/development-status
    :alt: Production/Stable
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fweb-lightgray.png?logo=github
    :target: https://github.com/OCA/web/tree/16.0/web_timeline
    :alt: OCA/web
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/web-16-0/web-16-0-web_timeline
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/web&target_branch=16.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

Define a new view displaying events in an interactive visualization chart.

The widget is based on the external library
https://visjs.github.io/vis-timeline/examples/timeline

**Table of contents**

.. contents::
   :local:

Configuration
=============

You need to define a view with the tag <timeline> as base element. These are
the possible attributes for the tag:

+--------------------+-----------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Attribute          | Required? | Description                                                                                                                                                                                                                                                               |
+====================+===========+===========================================================================================================================================================================================================================================================================+
| date_start         | **Yes**   | Defines the name of the field of type date that contains the start of the event.                                                                                                                                                                                          |
+--------------------+-----------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| date_stop          | No        | Defines the name of the field of type date that contains the end of the event. The date_stop can be equal to the attribute date_start to display events has 'point' on the Timeline (instantaneous event).                                                                |
+--------------------+-----------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| date_delay         | No        | Defines the name of the field of type float/integer that contain the duration in hours of the event, default = 1.                                                                                                                                                         |
+--------------------+-----------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| default_group_by   | **Yes**   | Defines the name of the field that will be taken as default group by when accessing the view or when no other group by is selected.                                                                                                                                       |
+--------------------+-----------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| zoomKey            | No        | Specifies whether the Timeline is only zoomed when an additional key is down. Available values are '' (does not apply), 'altKey', 'ctrlKey', or 'metaKey'. Set this option if you want to be able to use the scroll to navigate vertically on views with a lot of events. |
+--------------------+-----------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| mode               | No        | Specifies the initial visible window. Available values are: 'day' to display the current day, 'week', 'month' and 'fit'. Default value is 'fit' to adjust the visible window such that it fits all items.                                                                 |
+--------------------+-----------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| margin             | No        | Specifies the margins around the items. It should respect the JSON format. For example '{"item":{"horizontal":-10}}'. Available values are: '{"axis":<number>}' (The minimal margin in pixels between items and the time axis)                                            |
|                    |           | '{"item":<number>}' (The minimal margin in pixels between items in both horizontal and vertical direction), '{"item":{"horizontal":<number>}}' (The minimal horizontal margin in pixels between items),                                                                   |
|                    |           | '{"item":{"vertical":<number>}}' (The minimal vertical margin in pixels between items), '{"item":{"horizontal":<number>,"vertical":<number>}}' (Combination between horizontal and vertical margins in pixels between items).                                             |
+--------------------+-----------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| event_open_popup   | No        | When set to true, it allows to edit the events in a popup. If not (default value), the record is edited changing to form view.                                                                                                                                            |
+--------------------+-----------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| stack              | No        | When set to false, items will not be stacked on top of each other such that they do overlap.                                                                                                                                                                              |
+--------------------+-----------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| colors             | No        | Allows to set certain specific colors if the expressed condition (JS syntax) is met.                                                                                                                                                                                      |
+--------------------+-----------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| dependency_arrow   | No        | Set this attribute to a x2many field to draw arrows between the records referenced in the x2many field.                                                                                                                                                                   |
+--------------------+-----------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+

Optionally you can declare a custom template, which will be used to render the
timeline items. You have to name the template 'timeline-item'.
These are the variables available in template rendering:

* ``record``: to access the fields values selected in the timeline definition.
* ``field_utils``: used to format and parse values (see available functions in ``web.field_utils``).

You also need to declare the view in an action window of the involved model.

Example:

.. code-block:: xml

    <?xml version="1.0" encoding="utf-8"?>
    <odoo>
        <record id="view_task_timeline" model="ir.ui.view">
            <field name="model">project.task</field>
            <field name="type">timeline</field>
            <field name="arch" type="xml">
                <timeline date_start="date_assign"
                          date_stop="date_end"
                          string="Tasks"
                          default_group_by="project_id"
                          event_open_popup="true"
                          colors="white: user_ids == []; #2ecb71: kanban_state == 'done'; #ec7063: kanban_state == 'blocked'"
                          dependency_arrow="depend_on_ids"
                >
                    <field name="user_ids" />
                    <field name="planned_hours" />
                    <templates>
                        <t t-name="timeline-item">
                            <div class="o_project_timeline_item">
                                <t t-foreach="record.user_ids" t-as="user">
                                    <img
                                        t-if="record.user_ids"
                                        t-attf-src="/web/image/res.users/#{user}/image_128/16x16"
                                        t-att-title="record.user"
                                        width="16"
                                        height="16"
                                        class="mr8"
                                        alt="User"
                                    />
                                </t>
                                <span name="display_name">
                                    <t t-esc="record.display_name" />
                                </span>
                                <small
                                    name="planned_hours"
                                    class="text-info ml4"
                                    t-if="record.planned_hours"
                                >
                                    <t
                                        t-esc="field_utils.format.float_time(record.planned_hours)"
                                    />
                                </small>
                            </div>
                        </t>
                    </templates>
                </timeline>
            </field>
        </record>

        <record id="project.action_view_task" model="ir.actions.act_window">
            <field
                name="view_mode"
            >kanban,tree,form,calendar,timeline,pivot,graph,activity</field>
        </record>
    </odoo>

Usage
=====

For accessing the timeline view, you have to click on the button with the clock
icon in the view switcher. The first time you access to it, the timeline window
is zoomed to fit all the current elements, the same as when you perform a
search, filter or group by operation.

You can use the mouse scroll to zoom in or out in the timeline, and click on
any free area and drag for panning the view in that direction.

The records of your model will be shown as rectangles whose widths are the
duration of the event according our definition. You can select them clicking
on this rectangle. You can also use Ctrl or Shift keys for adding discrete
or range selections. Selected records are hightlighted with a different color
(but the difference will be more noticeable depending on the background color).
Once selected, you can drag and move the selected records across the timeline.

When a record is selected, a red cross button appears on the upper left corner
that allows to remove that record. This doesn't work for multiple records
although they were selected.

Records are grouped in different blocks depending on the group by criteria
selected (if none is specified, then the default group by is applied).
Dragging a record from one block to another change the corresponding field to
the value that represents the block. You can also click on the group name to
edit the involved record directly.

Double-click on the record to edit it. Double-click in open area to create a
new record with the group and start date linked to the area you clicked in.
By holding the Ctrl key and dragging left to right, you can create a new record
with the dragged start and end date.

Known issues / Roadmap
======================

* Implement a more efficient way of refreshing timeline after a record update;
* Make `attrs` attribute work;
* Make action attributes work (create, edit, delete) like in form and tree views.
* When grouping by m2m and more than one record is set, the timeline item appears only
  on one group. Allow showing in both groups.
* When grouping by m2m and dragging for changing the time or the group, the changes on
  the group will not be set, because it could make disappear the records not related
  with the changes that we want to make. When the item is showed in all groups change
  the value according the group of the dragged item.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/web/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/web/issues/new?body=module:%20web_timeline%0Aversion:%2016.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* ACSONE SA/NV
* Tecnativa
* Monk Software
* Onestein
* Trobz

Contributors
~~~~~~~~~~~~

* Laurent Mignon <<EMAIL>>
* Adrien Peiffer <<EMAIL>>
* Leonardo Donelli <<EMAIL>>
* Adrien Didenot <<EMAIL>>
* Thong Nguyen Van <<EMAIL>>
* Murtaza Mithaiwala <<EMAIL>>
* Ammar Officewala <<EMAIL>>
* `Tecnativa <https://www.tecnativa.com>`_:

  * Pedro M. Baeza
  * Alexandre Díaz
  * César A. Sánchez
* `Onestein <https://www.onestein.nl>`_:
    * Dennis Sluijk <<EMAIL>>
    * Anjeel Haria

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-tarteo| image:: https://github.com/tarteo.png?size=40px
    :target: https://github.com/tarteo
    :alt: tarteo

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-tarteo| 

This module is part of the `OCA/web <https://github.com/OCA/web/tree/16.0/web_timeline>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
