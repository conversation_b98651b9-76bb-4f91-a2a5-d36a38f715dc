# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_timeline
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2020-07-08 05:19+0000\n"
"Last-Translator: 黎伟杰 <<EMAIL>>\n"
"Language-Team: none\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 3.10\n"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_renderer.js:0
#, python-format
msgid "<b>UNASSIGNED</b>"
msgstr "<b>未分配</b>"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_controller.esm.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "您确定要删除此记录吗？"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Day"
msgstr "天"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Month"
msgstr "月"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_renderer.js:0
#, python-format
msgid "Template \"timeline-item\" not present in timeline view definition."
msgstr "时间线视图定义中不存在模板“timeline-item”。"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_view.js:0
#: model:ir.model.fields.selection,name:web_timeline.selection__ir_ui_view__type__timeline
#, python-format
msgid "Timeline"
msgstr "时间线"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_renderer.js:0
#, python-format
msgid "Timeline view has not defined 'date_start' attribute."
msgstr "时间线视图尚未定义\"date_start\"属性。"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Today"
msgstr "今天"

#. module: web_timeline
#: model:ir.model,name:web_timeline.model_ir_ui_view
msgid "View"
msgstr "视图"

#. module: web_timeline
#: model:ir.model.fields,field_description:web_timeline.field_ir_ui_view__type
msgid "View Type"
msgstr "查看类型"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_controller.esm.js:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Week"
msgstr "周"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Year"
msgstr "年"

#~ msgid "Activity"
#~ msgstr "活动"

#~ msgid "Calendar"
#~ msgstr "日历"

#~ msgid "Diagram"
#~ msgstr "图表"

#~ msgid "Form"
#~ msgstr "表单"

#~ msgid "Gantt"
#~ msgstr "甘特图"

#~ msgid "Graph"
#~ msgstr "图形"

#~ msgid "Kanban"
#~ msgstr "看板"

#~ msgid "Pivot"
#~ msgstr "透视表"

#~ msgid "QWeb"
#~ msgstr "QWeb"

#~ msgid "Search"
#~ msgstr "搜索"

#~ msgid "Tree"
#~ msgstr "树形"
