# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_timeline
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2021-09-23 07:34+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: fa\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_renderer.js:0
#, python-format
msgid "<b>UNASSIGNED</b>"
msgstr ""

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_controller.esm.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr ""

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Day"
msgstr "روز"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Month"
msgstr ""

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_renderer.js:0
#, python-format
msgid "Template \"timeline-item\" not present in timeline view definition."
msgstr ""

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_view.js:0
#: model:ir.model.fields.selection,name:web_timeline.selection__ir_ui_view__type__timeline
#, python-format
msgid "Timeline"
msgstr ""

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_renderer.js:0
#, python-format
msgid "Timeline view has not defined 'date_start' attribute."
msgstr ""

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Today"
msgstr ""

#. module: web_timeline
#: model:ir.model,name:web_timeline.model_ir_ui_view
msgid "View"
msgstr ""

#. module: web_timeline
#: model:ir.model.fields,field_description:web_timeline.field_ir_ui_view__type
msgid "View Type"
msgstr ""

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_controller.esm.js:0
#, python-format
msgid "Warning"
msgstr ""

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Week"
msgstr ""

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Year"
msgstr ""

#~ msgid "Display Name"
#~ msgstr "نام نمایشی"
