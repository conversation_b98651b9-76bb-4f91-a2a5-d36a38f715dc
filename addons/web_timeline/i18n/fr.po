# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web_timeline
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-01-03 03:50+0000\n"
"PO-Revision-Date: 2020-07-22 12:19+0000\n"
"Last-Translator: c2cdidier <<EMAIL>>\n"
"Language-Team: French (https://www.transifex.com/oca/teams/23907/fr/)\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 3.10\n"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_renderer.js:0
#, python-format
msgid "<b>UNASSIGNED</b>"
msgstr "<b>UNASSIGNIERT</b>"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_controller.esm.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "Êtes vous sûr de vouloir supprimer cet enregistrement ?"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Day"
msgstr "Jour"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Month"
msgstr "Mois"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_renderer.js:0
#, python-format
msgid "Template \"timeline-item\" not present in timeline view definition."
msgstr ""
"Le modèle \"timeline-item\" n'est pas présent dans la définition de la vue "
"timeline."

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_view.js:0
#: model:ir.model.fields.selection,name:web_timeline.selection__ir_ui_view__type__timeline
#, python-format
msgid "Timeline"
msgstr "Chronologie"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_renderer.js:0
#, python-format
msgid "Timeline view has not defined 'date_start' attribute."
msgstr "La vue chronologique n'a pas défini l'attribut 'date_start'."

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Today"
msgstr "Aujourd'hui"

#. module: web_timeline
#: model:ir.model,name:web_timeline.model_ir_ui_view
msgid "View"
msgstr "Vue"

#. module: web_timeline
#: model:ir.model.fields,field_description:web_timeline.field_ir_ui_view__type
msgid "View Type"
msgstr "Type de vue"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/js/timeline_controller.esm.js:0
#, python-format
msgid "Warning"
msgstr "Alerte"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Week"
msgstr "Semaine"

#. module: web_timeline
#. odoo-javascript
#: code:addons/web_timeline/static/src/xml/web_timeline.xml:0
#, python-format
msgid "Year"
msgstr "Année"

#~ msgid "Activity"
#~ msgstr "Activité"

#~ msgid "Calendar"
#~ msgstr "Calendrier"

#~ msgid "Diagram"
#~ msgstr "Diagramme"

#~ msgid "Form"
#~ msgstr "Formulaire"

#~ msgid "Gantt"
#~ msgstr "Gantt"

#~ msgid "Graph"
#~ msgstr "Graphique"

#~ msgid "Kanban"
#~ msgstr "Kanban"

#~ msgid "Pivot"
#~ msgstr "Pivot"

#~ msgid "QWeb"
#~ msgstr "QWeb"

#~ msgid "Search"
#~ msgstr "Recherche"

#~ msgid "Tree"
#~ msgstr "Arborescence"

#~ msgid "ir.ui.view"
#~ msgstr "ir.ui.view"
