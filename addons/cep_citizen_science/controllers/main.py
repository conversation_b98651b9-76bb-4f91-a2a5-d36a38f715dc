from odoo import http
from odoo.http import request
import json
import requests
import re

class GetCitizenScienceDataController(http.Controller):
    @http.route('/citizenscience/data', type='http', auth="public", website=True)
    def get_citizen_science_data(self, **kw):
        url = "https://decidim.eurecatprojects.com/api/"
        query = """
        {
            participatoryProcess(slug: "cstoolkit") {
                id
                slug
                publishedAt
                stats {
                    name
                    value
                }
                components {
                    id
                    name {
                        translation(locale: "ca")
                    }
                    ...on Blogs {
                        id
                        name {
                            translations {
                                locale
                                text
                            }
                        }
                        posts {
                            nodes {
                                id
                                author {
                                    nickname
                                }
                                title {
                                    translations {
                                        locale
                                        text
                                    }
                                }
                                body {
                                    translations {
                                        locale
                                        text
                                    }
                                }
                                endorsementsCount
                                publishedAt
                                updatedAt
                                totalCommentsCount
                                comments {
                                    author {
                                        nickname
                                    }
                                    body
                                    alignment
                                    downVotes
                                    upVotes
                                }
                            }
                        }
                    }
                }
            }
        }
        """
        response = requests.post(url, json={'query': query})
        if response.status_code == 200:
            context = response.json()
            body_text = context['data']['participatoryProcess']['components'][0]['posts']['nodes'][0]['body']['translations'][1]['text']
            # Define the base URL
            base_url = "https://decidim.eurecatprojects.com"
    
            # Use regex to find and replace the src attribute in img tags
            body_text = re.sub(r'src="(/rails/active_storage/blobs/redirect/[^"]+)"', r'src="' + base_url + r'\1"', body_text)
            # Use regex to add width="100%" to img tags if not already present
            body_text = re.sub(r'<img(?![^>]*\bwidth\b)([^>]*)>', r'<img\1 width="100%">', body_text)
            print(body_text)
            
            title = context['data']['participatoryProcess']['components'][0]['posts']['nodes'][0]['title']['translations'][1]['text']
            return request.render('cep_citizen_science.decidim_view', {'body_text': body_text, 'title': title})
        else:
            return "Failed to retrieve data"
