from odoo import http
from odoo.http import request

import random


def isAuthenticate():
    return request.env.user != request.website.user_id


class LLMController(http.Controller):

    @http.route('/llm', type='http', auth='public', website=True)
    def home(self, **kwargs):
        agenda_list = request.env['cep.llm.agenda'].sudo().search([])
        agenda = random.choice(agenda_list)
        context = {'agenda': agenda}    
        return request.render('cep_llm.llms_view', context)
    
    # Get assemply member list by API
    @http.route('/llm/assemply-member-list/', type='json', auth='public', methods=['GET'], website=True)
    def get_assemply_member_list(self, **kwargs):
        members = request.env['cep.llm.assemply.member'].sudo().search([])

        return {
            "status": "success",
            "members": members,
        }
    
    @http.route('/llm/get-assembly-member-opinion', type='json', auth='public', methods=['POST'], website=True)
    def get_assembly_member_opinion(self, **kwargs):
        agenda = kwargs['agenda']
        profile = kwargs['profile']
        print(agenda+' ---- '+profile)
        return 'hello'