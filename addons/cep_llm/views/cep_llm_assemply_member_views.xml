<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_llm_assemply_member_search" model="ir.ui.view">
        <field name="name">cep.llm.assemply.member.search</field>
        <field name="model">cep.llm.assemply.member</field>
        <field name="arch" type="xml">
            <search string="CEP LLM">
                <field name="name"/>
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_llm_assemply_member_tree" model="ir.ui.view">
        <field name="name">cep.llm.assemply.member.tree</field>
        <field name="model">cep.llm.assemply.member</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_llm_assemply_member_form" model="ir.ui.view">
        <field name="name">cep.llm.assemply.member.form</field>
        <field name="model">cep.llm.assemply.member</field>
        <field name="arch" type="xml">
            <form string="CEP LLM">
                <sheet>
                     <group>
                        <field name="name"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id='cep_llm_assemply_member_action' model='ir.actions.act_window'>
        <field name="name">Assemply Member</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.llm.assemply.member</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new assemply member
            </p>
        </field>
    </record>

    <menuitem id="cep_llm_assemply_member" name="Assemply Members" parent="cep_llm_root" action="cep_llm_assemply_member_action"/>
</odoo>