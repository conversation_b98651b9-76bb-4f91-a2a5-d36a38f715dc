<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="cep_llm.llms_view" name="LLM View">
        <t t-call="website.layout">
            <t t-set="title">LLM View</t>
            <t t-set="head">
                <t t-call-assets="web.assets_common"/>
                <t t-call-assets="cep_llm.llms_view"/>
            </t>
            <div class="oe_structure">
                <section class="s_ca_simulation pt-3 bg-white">
                    <div class="container">
                        <div class="card text-center mb-3">
                            <div class="card-header">
                                Today's Topic
                            </div>
                            <div class="card-body">
                                <div class="d-flex flex-column gap-2">
                                    <h3 class="card-title">
                                        <span id="agenda_title"><t t-esc="agenda.name"/></span>
                                    </h3>
                                    <div class="d-flex flex-column flex-md-row justify-content-center gap-2">
                                        <a t-att-href="'/llm'" class="btn btn-outline-primary btn-lg">Generate New Agenda</a>
                                        <!-- <button type="button" class="btn btn-primary btn-lg">Set Agenda</button>     -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex mb-3">
                            <select name="number_of_assembly_member" id="number_of_assembly_member" class="form-select w-25 ms-auto" aria-label="Number of Assembly Member">
                                <option value="" selected="selected" disabled="disabled">Number of Assembly Member</option>
                                <option value="3">Number 1-3</option>
                                <option value="4">Number 1-4</option>
                                <option value="5">Number 1-5</option>
                                <option value="6">Number 1-6</option>
                            </select>
                        </div>

                        <div class="row justify-content-center" id="assembly_members_container">

                        </div>


                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="card">
                                    <div class="card-header text-center">
                                        Outcomes
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex flex-column gap-3">

                                            <div class="overflow-scroll" style="max-height: 120px;font-size: 16px;" id="outcome_opinion">
                                                
                                            </div>

                                            <div class="d-flex flex-row justify-content-center gap-2">
                                                <button type="button" class="btn btn-outline-primary btn-lg" id="get_outcome_opinion">
                                                    Get Opinion
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-lg">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <g clip-path="url(#clip0_1541_510)">
                                                            <path
                                                                d="M18.6666 24H5.33332C3.90883 24 2.56952 23.4452 1.56212 22.4378C0.554771 21.4306 0 20.0912 0 18.6666V17.3332C0 16.5968 0.596923 15.9999 1.33334 15.9999C2.06976 15.9999 2.66668 16.5968 2.66668 17.3332V18.6666C2.66668 19.3789 2.94409 20.0486 3.44767 20.5522C3.9514 21.0559 4.62103 21.3333 5.33332 21.3333H18.6666C19.3789 21.3333 20.0485 21.0559 20.5522 20.5522C21.0559 20.0485 21.3333 19.3788 21.3333 18.6666V17.3332C21.3333 16.5968 21.9302 15.9999 22.6666 15.9999C23.403 15.9999 24 16.5968 24 17.3332V18.6666C24 20.0911 23.4452 21.4304 22.4378 22.4378C21.4304 23.4452 20.0911 24 18.6666 24ZM12 18.6666C11.8155 18.6666 11.6399 18.6291 11.4802 18.5615C11.3311 18.4985 11.1911 18.407 11.0685 18.2874C11.0685 18.2873 11.0685 18.2873 11.0684 18.2873C11.0676 18.2864 11.0667 18.2855 11.0658 18.2847C11.0656 18.2845 11.0653 18.2842 11.065 18.2839C11.0643 18.2833 11.0637 18.2826 11.063 18.2819C11.0625 18.2814 11.0621 18.281 11.0616 18.2805C11.0611 18.2801 11.0605 18.2794 11.0601 18.2791C11.0592 18.2781 11.0582 18.2771 11.0572 18.2762L5.72386 12.9428C5.20318 12.4221 5.20318 11.5779 5.72386 11.0571C6.24454 10.5365 7.08883 10.5364 7.6095 11.0571L10.6667 14.1143V1.33334C10.6666 0.596923 11.2636 0 12 0C12.7364 0 13.3334 0.596923 13.3334 1.33334V14.1143L16.3905 11.0571C16.9111 10.5365 17.7555 10.5365 18.2761 11.0571C18.7968 11.5778 18.7968 12.4221 18.2761 12.9428L12.9428 18.2761C12.9418 18.277 12.9408 18.278 12.9399 18.279C12.9394 18.2794 12.9388 18.2801 12.9384 18.2804C12.9379 18.2809 12.9375 18.2813 12.937 18.2818C12.9364 18.2825 12.9356 18.2832 12.935 18.2838C12.9348 18.2841 12.9344 18.2844 12.9342 18.2846C12.9334 18.2855 12.9325 18.2863 12.9316 18.2872C12.9316 18.2872 12.9316 18.2872 12.9315 18.2873C12.9168 18.3016 12.902 18.3154 12.8868 18.329C12.7751 18.4285 12.6508 18.5062 12.5193 18.5616C12.5188 18.5618 12.5185 18.562 12.518 18.5622C12.5175 18.5624 12.5171 18.5626 12.5166 18.5628C12.3576 18.6297 12.1832 18.6666 12 18.6666Z"
                                                                fill="#1FCCC6" />
                                                        </g>
                                                        <defs>
                                                            <clipPath id="clip0_1541_510">
                                                                <rect width="24" height="24" fill="white" />
                                                            </clipPath>
                                                        </defs>
                                                    </svg>
                                                </button>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        

                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="card">
                                    <div class="card-header text-center">
                                        Next Agenda Recommendation
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex flex-column gap-3">
                                            <div class="overflow-scroll" style="max-height: 120px;font-size: 16px;" id="next_agenda">
                                                
                                            </div>

                                            <div class="d-flex flex-row justify-content-center gap-2">
                                                <button type="button" class="btn btn-outline-primary btn-lg" id="get_next_agenda">
                                                    Get Opinion
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-lg" id="download_next_agenda">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <g clip-path="url(#clip0_1541_510)">
                                                            <path
                                                                d="M18.6666 24H5.33332C3.90883 24 2.56952 23.4452 1.56212 22.4378C0.554771 21.4306 0 20.0912 0 18.6666V17.3332C0 16.5968 0.596923 15.9999 1.33334 15.9999C2.06976 15.9999 2.66668 16.5968 2.66668 17.3332V18.6666C2.66668 19.3789 2.94409 20.0486 3.44767 20.5522C3.9514 21.0559 4.62103 21.3333 5.33332 21.3333H18.6666C19.3789 21.3333 20.0485 21.0559 20.5522 20.5522C21.0559 20.0485 21.3333 19.3788 21.3333 18.6666V17.3332C21.3333 16.5968 21.9302 15.9999 22.6666 15.9999C23.403 15.9999 24 16.5968 24 17.3332V18.6666C24 20.0911 23.4452 21.4304 22.4378 22.4378C21.4304 23.4452 20.0911 24 18.6666 24ZM12 18.6666C11.8155 18.6666 11.6399 18.6291 11.4802 18.5615C11.3311 18.4985 11.1911 18.407 11.0685 18.2874C11.0685 18.2873 11.0685 18.2873 11.0684 18.2873C11.0676 18.2864 11.0667 18.2855 11.0658 18.2847C11.0656 18.2845 11.0653 18.2842 11.065 18.2839C11.0643 18.2833 11.0637 18.2826 11.063 18.2819C11.0625 18.2814 11.0621 18.281 11.0616 18.2805C11.0611 18.2801 11.0605 18.2794 11.0601 18.2791C11.0592 18.2781 11.0582 18.2771 11.0572 18.2762L5.72386 12.9428C5.20318 12.4221 5.20318 11.5779 5.72386 11.0571C6.24454 10.5365 7.08883 10.5364 7.6095 11.0571L10.6667 14.1143V1.33334C10.6666 0.596923 11.2636 0 12 0C12.7364 0 13.3334 0.596923 13.3334 1.33334V14.1143L16.3905 11.0571C16.9111 10.5365 17.7555 10.5365 18.2761 11.0571C18.7968 11.5778 18.7968 12.4221 18.2761 12.9428L12.9428 18.2761C12.9418 18.277 12.9408 18.278 12.9399 18.279C12.9394 18.2794 12.9388 18.2801 12.9384 18.2804C12.9379 18.2809 12.9375 18.2813 12.937 18.2818C12.9364 18.2825 12.9356 18.2832 12.935 18.2838C12.9348 18.2841 12.9344 18.2844 12.9342 18.2846C12.9334 18.2855 12.9325 18.2863 12.9316 18.2872C12.9316 18.2872 12.9316 18.2872 12.9315 18.2873C12.9168 18.3016 12.902 18.3154 12.8868 18.329C12.7751 18.4285 12.6508 18.5062 12.5193 18.5616C12.5188 18.5618 12.5185 18.562 12.518 18.5622C12.5175 18.5624 12.5171 18.5626 12.5166 18.5628C12.3576 18.6297 12.1832 18.6666 12 18.6666Z"
                                                                fill="#1FCCC6" />
                                                        </g>
                                                        <defs>
                                                            <clipPath id="clip0_1541_510">
                                                                <rect width="24" height="24" fill="white" />
                                                            </clipPath>
                                                        </defs>
                                                    </svg>
                                                </button>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
            <br/>
        </t>
    </template>
</odoo>