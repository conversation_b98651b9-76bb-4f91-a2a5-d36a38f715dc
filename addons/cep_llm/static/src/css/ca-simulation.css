.s_ca_simulation .btn-outline-primary {
    color: #1FCCC6;
    font-family: 'Nuni<PERSON>';
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;

    border-radius: 8px;
    border: 1px solid #1FCCC6;
    background-color: #FFFFFF;
}

.s_ca_simulation .btn-primary {
    color: #FFFFFF;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;

    border-radius: 8px;
    border: 1px solid #1FCCC6;
    background-color: #1FCCC6;
}

.s_ca_simulation .btn.disabled {
    border: 1px solid #D3DDF3;
    background: #FFFFFF;
    color: #D3DDF3;
}

.s_ca_simulation .card {
    border-radius: 16px;
    background: #FFFFFF;
    box-shadow: 0px 12px 50px 0px rgba(9, 23, 107, 0.12);

    color: #181C32;
    font-family: 'Nunito';
}

.s_ca_simulation .card-header {
    padding: 1rem 2.25rem;
    background: rgba(211, 221, 243, 0.80);
    color: #181C32;
    text-align: center;
    font-family: 'Nunito';
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}

.s_ca_simulation h3 {
    color: #181C32;
    font-family: 'Nunito';
    font-size: 32px;
    font-style: normal;
    font-weight: 800;
    line-height: normal;
}

.s_ca_simulation ol {
    color: #181C32;
    text-align: justify;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.s_ca_simulation label {
    color: #181C32;
    text-align: center;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}

.s_ca_simulation select {
    color: #181C32;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}
