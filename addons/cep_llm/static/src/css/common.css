/**
Colors
**/
.c-dark-gunmetal {
    color: #181C32 !important; 
}

.c-maximum-blue-green {
    color: #1FCCC6 !important;
}

.c-red {
    color: #DF0000 !important;
}

.alert-seashell {
    background-color: #FEEEEE;
    border-color: #cb5454;
    border-radius: 16px;
}

.alert-seashell p {
    color: #181C32;
    text-align: justify;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

body {
    font-family: 'Nunito', sans-serif;
}

p {
    color: rgba(26, 41, 66, 0.70);
    font-family: 'Nunito';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
}

h1 {
    color: rgba(13, 12, 34, 0.90);
    font-family: 'Nunito';
    font-size: 48px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

h2 {
    color: rgba(13, 12, 34, 0.90);
    font-family: <PERSON><PERSON><PERSON>;
    font-size: 40px;
    font-style: normal;
    font-weight: 400;
    line-height: normal; 
}

h3 {
    color: rgba(13, 12, 34, 0.90);
    font-family: Nunito;
    font-size: 32px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

h4 {
    color: rgba(13, 12, 34, 0.90);
    font-family: Nunito;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

h5 {
    color: rgba(13, 12, 34, 0.90);
    font-family: Nunito;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

h6 {
    color: rgba(13, 12, 34, 0.90);
    font-family: Nunito;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.fw-500 {
    font-weight: 500;
}

.fw-700 {
    font-weight: 700;
}

.fw-400 {
    font-weight: 400;
}

.fw-800 {
    font-weight: 800;
}

.opacity-80 {
    opacity: 0.8;
}


.bg-alice-blue {
    background-color: #EEF3FE;
}

.ms-n5 {
    margin-left: -40px;
}

.callout {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    margin-top: 1rem;
    margin-bottom: 1rem;
    border-left: 1px solid #1FCCC6;
    border-left-width: 0.25rem;
    border-radius: 0.25rem;
}

.callout-title {
    color: #181C32;
    font-family: 'Nunito';
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.callout-text {
    color: #181C32;
    text-align: justify;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.badge-status {
    border-radius: 5px;
    border: 1px solid #181C32;
    opacity: 0.2;
    background: #FFF;

    color: #181C32;
    font-family: 'Nunito';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    text-transform: uppercase;
}

.badge-tag {
    border-radius: 5px;
    opacity: 0.8;
    background: #EEF3FE;
    color: #181C32;
    font-family: 'Nunito';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal; 
}

.text-justify {
    text-align: justify !important;
}
