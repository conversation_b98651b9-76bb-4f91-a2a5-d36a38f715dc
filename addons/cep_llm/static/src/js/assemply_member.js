$(document).ready(function () {
  $('#number_of_assembly_member').on('change', function () {
    // Get the selected value
    var selectedValue = this.value;

    console.log(selectedValue);

    // Get the container for the assembly members
    var container = document.getElementById('assembly_members_container');

    // Clear the container first
    container.innerHTML = '';

    // Loop to create and append the divs
    for (let i = 1; i <= selectedValue; i++) {
      var assemblyMemberDiv = document.createElement('div');
      assemblyMemberDiv.className = 'col-12 col-md-4 mb-3';
      assemblyMemberDiv.innerHTML = `
                  <div class="card">
                      <div class="card-header text-center">
                          Assembly Member ${i}
                      </div>
                      <div class="card-body">
                          <div class="d-flex flex-column gap-3">
                              <form>
                                  <div class="row">
                                      <label for="profile${i}" class="col-sm-4 col-form-label">Profile</label>
                                      <div class="col-sm-8">
                                          <select name="profile" id="profile${i}" class="form-select assembly-member-dropdown" aria-label="Profile">
                                              <option>Select profile</option>
                                              <option value="Environmental Engineer">Environmental Engineer</option>
                                              <option value="Water Resource Engineer">Water Resource Engineer</option>
                                              <option value="Mechanical Engineer">Mechanical Engineer</option>
                                          </select>
                                      </div>
                                  </div>
                              </form>
                              <div class="overflow-scroll assembly-member-opinion" style="max-height: 120px;font-size: 16px;">
                              </div>
                              <div class="d-flex flex-row justify-content-center gap-2">
                                  <button type="button" class="btn btn-outline-primary btn-lg generate-assembly-member-opinion">Generate Opinion</button>
                                  <button type="button" class="btn btn-outline-primary btn-lg">
                                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <g clip-path="url(#clip0_1541_510)">
                                              <path d="M18.6666 24H5.33332C3.90883 24 2.56952 23.4452 1.56212 22.4378C0.554771 21.4306 0 20.0912 0 18.6666V17.3332C0 16.5968 0.596923 15.9999 1.33334 15.9999C2.06976 15.9999 2.66668 16.5968 2.66668 17.3332V18.6666C2.66668 19.3789 2.94409 20.0486 3.44767 20.5522C3.9514 21.0559 4.62103 21.3333 5.33332 21.3333H18.6666C19.3789 21.3333 20.0485 21.0559 20.5522 20.5522C21.0559 20.0485 21.3333 19.3788 21.3333 18.6666V17.3332C21.3333 16.5968 21.9302 15.9999 22.6666 15.9999C23.403 15.9999 24 16.5968 24 17.3332V18.6666C24 20.0911 23.4452 21.4304 22.4378 22.4378C21.4304 23.4452 20.0911 24 18.6666 24ZM12 18.6666C11.8155 18.6666 11.6399 18.6291 11.4802 18.5615C11.3311 18.4985 11.1911 18.407 11.0685 18.2874C11.0685 18.2873 11.0685 18.2873 11.0684 18.2873C11.0676 18.2864 11.0667 18.2855 11.0658 18.2847C11.0656 18.2845 11.0653 18.2842 11.065 18.2839C11.0643 18.2833 11.0637 18.2826 11.063 18.2819C11.0625 18.2814 11.0621 18.281 11.0616 18.2805C11.0611 18.2801 11.0605 18.2794 11.0601 18.2791C11.0592 18.2781 11.0582 18.2771 11.0572 18.2762L5.72386 12.9428C5.20318 12.4221 5.20318 11.5779 5.72386 11.0571C6.24454 10.5365 7.08883 10.5364 7.6095 11.0571L10.6667 14.1143V1.33334C10.6666 0.596923 11.2636 0 12 0C12.7364 0 13.3334 0.596923 13.3334 1.33334V14.1143L16.3905 11.0571C16.9111 10.5365 17.7555 10.5365 18.2761 11.0571C18.7968 11.5778 18.7968 12.4221 18.2761 12.9428L12.9428 18.2761C12.9418 18.277 12.9408 18.278 12.9399 18.279C12.9394 18.2794 12.9388 18.2801 12.9384 18.2804C12.9379 18.2809 12.9375 18.2813 12.937 18.2818C12.9364 18.2825 12.9356 18.2832 12.935 18.2838C12.9348 18.2841 12.9344 18.2844 12.9342 18.2846C12.9334 18.2855 12.9325 18.2863 12.9316 18.2872C12.9316 18.2872 12.9316 18.2872 12.9315 18.2873C12.9168 18.3016 12.902 18.3154 12.8868 18.329C12.7751 18.4285 12.6508 18.5062 12.5193 18.5616C12.5188 18.5618 12.5185 18.562 12.518 18.5622C12.5175 18.5624 12.5171 18.5626 12.5166 18.5628C12.3576 18.6297 12.1832 18.6666 12 18.6666Z" fill="#1FCCC6" />
                                          </g>
                                          <defs>
                                              <clipPath id="clip0_1541_510">
                                                  <rect width="24" height="24" fill="white" />
                                              </clipPath>
                                          </defs>
                                      </svg>
                                  </button>
                              </div>
                          </div>
                      </div>
                  </div>
              `;
      container.appendChild(assemblyMemberDiv);
    }
  });

  $(document).on('click', '.generate-assembly-member-opinion', function () {
    const agenda = $('#agenda_title').html();
    console.log('agenda title: ' + agenda);
    const profile = $(this)
      .closest('.card')
      .find('.assembly-member-dropdown')
      .val();
    console.log('profile: ' + profile);
    let $opinion = $(this).closest('.card').find('.assembly-member-opinion');
    if (profile == 'Select profile') {
      alert('Please select a profile');
      return;
    } else {
      let $generateButton = $(this);
      $generateButton.html(`<div class="spinner-border text-info" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>`);
      $.ajax({
        type: 'post',
        url: 'https://eef1-106-0-53-190.ngrok-free.app/api/predict-opinion',
        // headers: {
        //     'X-CSRFToken': odoo.csrf_token
        // },
        contentType: 'application/json',
        data: JSON.stringify({ user_prompt: agenda, assemply_member: profile }),
        success: function (response) {
          console.log('Response:', response);
          // Handle the response data here
          // let text = response.response;
          // // Split the text by newline characters
          // let lines = text.split('\n');
          // // Map each line to a list item
          // let listItems = lines.map(line => `<li>${line.replace(/^\d+\.\s*/, '')}</li>`).join('');

          let member_opinion = response.response.replace(/\n/g, '<br>');
          $opinion.html(member_opinion);
          $generateButton.html('Generate Opinion');
        },
        error: function (xhr, status, error) {
          console.error('Error:', error);
          // Handle errors
          alert('Something went wrong! Please contact admin if issue persists.');
        },
      });
    }
  });

  $('#get_outcome_opinion').on('click', function () {
    // button loading spinner
    let $generateButton = $(this);
    $generateButton.html(`<div class="spinner-border text-info" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>`);
    let user_prompt = '';
    // foreach assembly member, get the opinion
    $('.assembly-member-opinion').each(function () {
      //console.log($(this).html());
      user_prompt += $(this).html() + ' ';
    });
    user_prompt = user_prompt.replace(/<br>/g, ' ');
    console.log('user_prompt:', user_prompt);
    $.ajax({
      type: 'post',
      url: 'https://eef1-106-0-53-190.ngrok-free.app/api/predict-outcome',
      // headers: {
      //     'X-CSRFToken': odoo.csrf_token
      // },
      contentType: 'application/json',
      data: JSON.stringify({ user_prompt: user_prompt }),
      success: function (response) {
        console.log('Response:', response);
        // Handle the response data here
        let outcome = response.response.replace(/\n/g, '<br>');
        $('#outcome_opinion').html(outcome);
        $generateButton.html('Get Opinion');
      },
      error: function (xhr, status, error) {
        console.error('Error:', error);
        // Handle errors
        alert('Something went wrong! Please contact admin if issue persists.');
      },
    });
  });

  $('#get_next_agenda').on('click', function () {
    // button loading spinner
    let $generateButton = $(this);
    $generateButton.html(`<div class="spinner-border text-info" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>`);
    let user_prompt = $('#outcome_opinion').html();
    user_prompt = user_prompt.replace(/<br>/g, ' ');
    console.log('user_prompt:', user_prompt);
    $.ajax({
      type: 'post',
      url: 'https://eef1-106-0-53-190.ngrok-free.app/api/predict-next-opinion',
      // headers: {
      //     'X-CSRFToken': odoo.csrf_token
      // },
      contentType: 'application/json',
      data: JSON.stringify({ user_prompt: user_prompt }),
      success: function (response) {
        console.log('Response:', response);
        let next_agenda = response.response.replace(/\n/g, '<br>');
        // Handle the response data here
        $('#next_agenda').html(next_agenda);
        $generateButton.html('Get Opinion');
      },
      error: function (xhr, status, error) {
        console.error('Error:', error);
        // Handle errors
        alert('Something went wrong! Please contact admin if issue persists.');
      },
    });
  });

  $('#download_next_agenda').on('click', function () {
    html2canvas(document.querySelector('#next_agenda')).then((canvas) => {
      var imgData = canvas.toDataURL('image/png');
      var pdf = new jsPDF('p', 'mm', 'a4');

      var margin = 10; // Standard margin size in mm
      var topMargin = 20; // Increased top margin in mm
      var imgWidth = 210 - 2 * margin; // A4 width in mm minus side margins
      var pageHeight = 295 - topMargin - margin; // A4 height in mm minus top and bottom margins
      var imgHeight = (canvas.height * imgWidth) / canvas.width;
      var heightLeft = imgHeight;

      var position = topMargin; // Start position considering increased top margin

      // Set font size
      pdf.setFontSize(20); // Adjust the font size as needed

      // Add some text (optional)
      pdf.text('Agenda', margin, topMargin - 10); // Adjust text position as needed

      pdf.addImage(imgData, 'PNG', margin, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight + topMargin; // Adjust position for new page
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', margin, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save('agenda.pdf');
    });
  });
});
