.main-content .subheading {
  font-size: 20px;
  font-weight: 800;
  line-height: 24px;
  color: #0F182A;
}

.main-content .btn {
  border-radius: 6px;
  font-weight: bold;
  margin: 2px;
}

@media screen and (max-width: 768px) {
  .main-content td {
    min-width: auto !important;
  }
  .main-content .btn {
    padding: 4px 8px;
    font-size: 12px;
    display: inline-block;
    white-space: nowrap;
  }
}

.main-content .btn-primary {
  color: white;
  background-color: #249AFB;
  border-color: #249AFB;
}

/* .main-content .badge {
   min-width: 180px;
  height: 40px;
  padding: 8px 16px;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  text-align: left; 
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  display: flex;
  justify-content: center;
  align-items: center;
}  */


.main-content .table-badge {
  padding: 3px 20px;
  border-radius: 29px;
  font-size: 12px;
  font-weight: 600;
  line-height: 20px;
}

.main-content .badge-primary {
  background-color: #EEFAFF;
  color: #249AFB
}

.main-content .badge-secondary {
  background-color: #F1F5F9;
  color: #0F182A
}

.main-content .badge-danger {
  background-color: #FEF3F2;
  color: #F04438
}

.main-content .badge-warning {
  background-color: #FFFAEB;
  color: #F79009;
}

.main-content .badge-success {
  background-color: #ECFDF3;
  color: #12B76A;
}

.main-content .checkbox-row {
  margin: 10px 4px;
}

.main-content .title {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  color: #0F182A
}

.main-content .list-group-item {
  border: none;
}

.custom-card {
  border: none;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  max-width: 300px;
  margin: 20px auto;
}
.custom-card:hover{
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s;
}

.custom-card img {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.custom-card .heart-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.8);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-card .heart-icon:hover {
  transform: scale(1.1);
}

.custom-card .card-body {
  padding: 16px;
  background: #F8FAFC !important;
}

.custom-card .card-title {
font-size: 16px;
font-weight: 600;
line-height: 24px;
color: #000000;
margin-bottom: 16px;
}

.custom-card .card-subtitle {
font-size: 16px;
font-weight: 400;
line-height: 26px;
color: #000000;
}

.custom-card .card-buttons {
  display: flex;
  gap: 10px;
  margin-top: 12px;
}

.custom-card .card-buttons .btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 4px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  border-radius: 8px;
}

.custom-card .btn-edit {
  color: #0E7AE7;
  border: none;
  padding: 8px 12px;
  font-size: 14px;
}

.custom-card .btn-edit:hover {
  background-color: #0d3a6a;
}

.custom-card .btn-remove {
  color: #DB4437;
  border: none;
  padding: 8px 12px;
  font-size: 14px;
}
.custom-card .btn:hover {
  color: #FFFFFF;
  border-radius: 8px;
  transition: all 0.3s;
}
.custom-card .btn-remove:hover {
  background-color: #c9302c;
}

.main-content   .table.agenda-table td.title-cell {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
@media (max-width: 1440px) {
  .main-content .table.agenda-table td.title-cell {
      max-width: 150px;
  }
}