/**
Colors
**/
.c-dark-gunmetal {
  color: #181c32 !important;
}

.c-maximum-blue-green {
  color: #1fccc6 !important;
}

.c-red {
  color: #df0000 !important;
}

.alert-seashell {
  background-color: #feeeee;
  border-color: #cb5454;
  border-radius: 16px;
}

.alert-seashell p {
  color: #181c32;
  text-align: justify;
  font-family: 'Nunito', 'Odoo Unicode Support Noto', sans-serif;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

body {
  overflow: inherit;
  height: inherit;
}

p {
  color: rgba(26, 41, 66, 0.7);
  font-family: 'Nunito', 'Odoo Unicode Support Noto', sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 21px;
}

h1 {
  color: rgba(13, 12, 34, 0.9);
  font-family: 'Nunito', 'Odoo Unicode Support Noto', sans-serif;
  font-size: 48px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

h2 {
  color: rgba(13, 12, 34, 0.9);
  font-family: 'Nunito', 'Odoo Unicode Support Noto', sans-serif;
  font-size: 40px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

h3 {
  color: rgba(13, 12, 34, 0.9);
  font-family: 'Nunito', 'Odoo Unicode Support Noto', sans-serif;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

h4 {
  color: rgba(13, 12, 34, 0.9);
  font-family: 'Nunito', 'Odoo Unicode Support Noto', sans-serif;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

h5 {
  color: rgba(13, 12, 34, 0.9);
  font-family: 'Nunito', 'Odoo Unicode Support Noto', sans-serif;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

h6 {
  color: rgba(13, 12, 34, 0.9);
  font-family: 'Nunito', 'Odoo Unicode Support Noto', sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.fw-500 {
  font-weight: 500;
}

.fw-700 {
  font-weight: 700;
}

.fw-400 {
  font-weight: 400;
}

.fw-800 {
  font-weight: 800;
}

.opacity-80 {
  opacity: 0.8;
}

.bg-alice-blue {
  background-color: #eef3fe;
}

.ms-n5 {
  margin-left: -40px;
}

.callout {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
  border-left: 1px solid #1fccc6;
  border-left-width: 0.25rem;
  border-radius: 0.25rem;
}

.callout-title {
  color: #181c32;
  font-family: 'Nunito', 'Odoo Unicode Support Noto', sans-serif;
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.callout-text {
  color: #181c32;
  text-align: justify;
  font-family: 'Nunito', 'Odoo Unicode Support Noto', sans-serif;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.badge-status {
  border-radius: 5px;
  border: 1px solid #181c32;
  opacity: 0.2;
  background: #fff;

  color: #181c32;
  font-family: 'Nunito', 'Odoo Unicode Support Noto', sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: uppercase;
}

.badge-tag {
  border-radius: 5px;
  opacity: 0.8;
  background: #eef3fe;
  color: #181c32;
  font-family: 'Nunito', 'Odoo Unicode Support Noto', sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.text-justify {
  text-align: justify !important;
}

/* CSS added by tanvir ibn touhid */
#wrapwrap {
  z-index: inherit;
}
.main-content {
  padding: 24px;
  padding-top: 32px;
  margin-left: 250px;
  flex-grow: 1;
  background-color: #fff;
  min-height: calc(100vh - 157px);
}

.main-content .heading-4 {
  font-size: 28px;
  font-weight: 800;
  line-height: 38px;
}

.main-content .checkbox-label {
  display: flex;
  align-items: center;
  gap: 12px;
}

.main-content .custom-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #0f182a;
  border-radius: 4px;
  appearance: none;
  cursor: pointer;
  outline: none;
  position: relative;
}

.main-content .custom-checkbox:checked {
  background-color: #0f182a;
  border-color: #0f182a;
}

.main-content .custom-checkbox:checked::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 6px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.main-content .checkbox-text {
  padding: 8px 12px;
  font-size: 14px;
  color: #64748b;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  width: 300px;
}

.main-content.homepage .subheading {
  font-size: 20px;
  font-weight: 800;
  line-height: 24px;
  color: #0f182a;
}

.main-content.homepage .btn-primary {
  margin-top: 12px;
  width: 200px;
  padding: 12px 16px;
  border-radius: 6px;
  color: white;
  font-weight: bold;
  background-color: #249afb;
}

.main-content.homepage .badge {
  min-width: 180px;
  height: 40px;
  padding: 8px 16px;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main-content.homepage .badge-primary {
  background-color: #eefaff;
  color: #249afb;
}

.main-content.homepage .badge-secondary {
  background-color: #f1f5f9;
  color: #0f182a;
}

.main-content.homepage .badge-danger {
  background-color: #fef3f2;
  color: #f04438;
}

.main-content.homepage .badge-warning {
  background-color: #fffaeb;
  color: #f79009;
}

.main-content.homepage .badge-success {
  background-color: #ecfdf3;
  color: #12b76a;
}

.main-content.homepage .checkbox-row {
  margin: 10px 4px;
}

.main-content.homepage .title {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  color: #0f182a;
}

.main-content.homepage .list-group-item {
  border: none;
}


.main-content .btn {
  font-family: 'Nunito', 'Odoo Unicode Support Noto', sans-serif;
  
}
.main-content.listPage .btn {
  padding: 0.15rem 0.8rem;
  font-size: 0.7rem;
}
.toast-container {
  z-index: 9999;
}

.main-content .accordion-item {
  border: 3px solid white;
  background-color: #f8fafc;
}

.main-content .accordion-button {
  font-weight: bold;
  font-family: Nunito, 'Odoo Unicode Support Noto', sans-serif;
  text-transform: capitalize;
}
.main-content .accordion-body {
  background-color: #f8fafc;
}
.main-content .tagify {
  width: 100%;
}

.project-description {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 3em;
  line-height: 1.5em;
}

.loading-dots {
  animation: loading 1.5s infinite;
  display: inline-block;
}
@keyframes loading {
  0% {
    opacity: 0.2;
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}

.climas-logo {
  text-align: center;
  margin-bottom: 20px;
}

.climas-logo img {
  max-width: 200px;
  height: auto;
  width: 190px;
}

.js-tooltip {
  position: relative;
  display: inline-block;
}

.js-tooltip .info-symbol i::before {
    color: #007bff;
    font-size: 13px;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 50%;
    border: 1px solid #007bff;
}

.js-tooltip .tooltip-content {
  display: none;
  position: absolute;
  background: #2c3e50;
  color: white;
  padding: 10px;
  border-radius: 6px;
  width: 250px;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 5px;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  font-size: 13px;
  font-weight: normal;
  text-align: center;
}

.js-tooltip .tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #2c3e50 transparent transparent transparent;
}

.js-tooltip.active .tooltip-content {
  display: block;
}
