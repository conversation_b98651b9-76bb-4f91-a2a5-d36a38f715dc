/* Sidebar Styles */
.sidebar {
  width: 250px;
  background-color: #f8fafc;
  position: fixed;
  padding-top: calc(0px + 20px);
  left: -250px;
  height: 100%;
  z-index: 1;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease-in-out;
}

.sidebar::before {
  position: absolute;
  content: '';
  width: 250px;
  height: 300px;
  top: -263px;
  background-color: #f8fafc;
  box-shadow: 2px -7px 10px rgba(0, 0, 0, 0.1);
  z-index: -6;
}
.o_footer {
  z-index: 2;
}
.sidebar.open {
  transform: translateX(250px);
}

.sidebar .header {
  text-align: center;
  margin-bottom: 1rem;
}

.sidebar .header h4 {
  font-size: 27px;
  font-weight: 600;
  line-height: 40px;
  color: #155493;
  margin-top: 30px;
}

.sidebar .header h6 {
  font-size: 22px;
  font-weight: 400;
  line-height: 28px;
  color: #155493;
}

.sidebar .divider {
  height: 1px;
  background-color: #dee2e6;
  margin: 1rem 0;
}

.sidebar .menu a {
  padding: 10px 20px;
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  line-height: 28px;
  text-align: left;
  color: #155493;
  text-decoration: none;
  transition: all 0.3s ease;
}

.menu-item-group {
  position: relative;
}

.menu-item.has-submenu {
  justify-content: space-between;
}

.submenu-icon {
  transition: transform 0.3s ease;
}

.submenu-icon.open {
  transform: rotate(-180deg);
}

.submenu {
  display: none;
  background-color: #f0f4f8;
  overflow: hidden;
}

.submenu.open {
  display: block;
}

.submenu-item {
  padding-left: 40px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

.sidebar .menu a.active,
.sidebar .menu a:hover {
  background-color: #eefaff;
}

.sidebar .menu a i {
  margin-right: 10px;
}

/* For large screens, keep the sidebar visible */
@media screen and (min-width: 768px) {
  .sidebar {
    left: 0;
    /* Sidebar is always visible */
    transform: none;
    /* Remove transform */
  }

  .sidebar-toggle {
    display: none;
    /* Hide the button on large screens */
  }
}

/* For small screens, hide the sidebar and show the sidebar toggle button */
@media screen and (max-width: 767px) {
  .main-content {
    margin-left: 0px;
  }

  .sidebar {
    z-index: 1024;
  }

  .sidebar-toggle {
    display: block;
    position: fixed;
    left: 16px;
    top: calc(73px + 12px);
    transform: translateX(-50%);
    background-color: #007bff;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    color: white;
    border: none;
    padding: 8px 16px;
    font-size: 18px;
    cursor: pointer;
    z-index: 1100;
  }
}
