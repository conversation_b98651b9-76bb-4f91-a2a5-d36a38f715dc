.prioritize-main-content {
  .criteria-header {
    font-size: 16px;
    font-weight: 500;
    color: #0F182A;
    margin-bottom: 10px;
    flex: 0 0 150px;
  }
  
  .input-group {
    flex: 1;
  }
  
  .form-control {
    flex: 1;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #CBD5E1;
    margin-right: 10px;
  }
  
  .options {
    display: flex;
    gap: 10px;
  }
  
  .option-box {
    padding: 8px 16px;
    border: 1px solid #CBD5E1;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    color: #64748B;
    transition: background-color 0.3s;
  }
  
  .option-box:hover {
    background-color: #E5F2FF;
  }
  
  .option-box.active {
    background-color: #0E7AE7;
    color: white;
  }
  
  .output-button {
    background-color: #0E7AE7 !important;
    border: none !important;
    padding: 8px 16px !important;
    border-radius: 6px;
    margin-top: 32px;
    font-weight: 700;
  }
  
  @media (max-width: 576px) {
    .criteria-header {
      font-size: 14px;
    }
  
    .form-control {
      padding: 10px;
    }
  
    .option-box {
      padding: 8px;
      font-size: 14px;
    }
  }
  
}

