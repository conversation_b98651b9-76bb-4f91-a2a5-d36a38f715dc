$(document).ready(function () {
  $('.criteria-weight-input').on('change', function () {
    let value = parseFloat($(this).val()).toFixed(2);
    $(this).val(value);
  });

  let dilemmas = [];
  const project_id = $('#project_id').val();
  const youtube_source = $('#youtube_source').val();
  const dilemmaModal = $('#dilemma-modal');

  const dilemma_container = $('#dilemma-container');
  const agenda_container = $('#agenda-container');

  const dilemma_priritise = $('#dilemma_priritise');
  const ngramContainer = $('#ngram-container');
  const dilemma_loader = $('#dilemma-spinner');

  // create dilemma list element
  const dilemmas_list = $('<ol/>', {
    class: 'list-group list-group-numbered accordion',
    id: 'dilemmas-list',
  });

  const agendas_list = $('<div/>', {
    class: 'accordion',
    id: 'agendaAccordion',
  });

  const statusManager = {
    dilemma: {
      container: dilemma_container,
      list: dilemmas_list,
      loader: dilemma_loader,
      status: $('#dilemma_status').val(),
      errorMessage: $('#dilemma_error_message').val(),
    },
    agenda: {
      container: agenda_container,
      list: agendas_list,
      loader: null, // Will be set dynamically
      status: $('#agenda_status').val(),
      errorMessage: $('#agenda_error_message').val(),
    },
    ngram: {
      container: ngramContainer,
      loader: null, // Will be set dynamically
      status: $('#ngram_status').val(),
      errorMessage: $('#ngram_error_message').val(),
    },
    frequency: {
      container: $('#frequency-container'),
      loader: null, // Will be set dynamically
      status: $('#analyzer_status').val(),
      errorMessage: $('#analyzer_error_message').val(),
    },
    sentiment: {
      container: $('#sentiment-container'),
      loader: null, // Will be set dynamically
      status: $('#sentiment_analyzer_status').val(),
      errorMessage: $('#sentiment_analyzer_error_message').val(),
    },
  };

  const createLoader = (message) =>
    $('<div/>', {
      class: 'text-center',
      html:
        '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">' +
        message +
        '</p>',
    });

  const createErrorMessage = (message) =>
    $('<div/>', {
      class: 'alert alert-danger',
      html: `<i class="fa fa-exclamation-triangle"></i> ${message}`,
    });

  // Initialize component displays based on current status
  function initializeComponentDisplays() {
    Object.keys(statusManager).forEach((componentKey) => {
      const component = statusManager[componentKey];
      updateComponentDisplay(
        componentKey,
        component.status,
        component.errorMessage
      );
    });
  }

  // Update component display based on status
  function updateComponentDisplay(componentKey, status, errorMessage) {
    const component = statusManager[componentKey];
    // Clear existing content
    if (component.container) {
      component.container.empty();
    }

    switch (status) {
      case 'idle':
        // Show blank/empty state
        if (componentKey === 'dilemma') {
          component.container.html(
            '<p class="text-muted text-center">No dilemmas generated yet.</p>'
          );
        } else if (componentKey === 'agenda') {
          component.container.html(
            '<p class="text-muted text-center">No agendas generated yet.</p>'
          );
        } else if (componentKey === 'ngram') {
          component.container.html(
            '<p class="text-muted text-center">N-gram analysis not started.</p>'
          );
        } else if (componentKey === 'frequency') {
          component.container.html(
            '<p class="text-muted text-center">Frequency analysis not started.</p>'
          );
        } else if (componentKey === 'sentiment') {
          component.container.html(
            '<p class="text-muted text-center">Sentiment analysis not started.</p>'
          );
        }
        break;

      case 'running':
        // Show spinner
        let message = '';
        if (componentKey === 'dilemma') message = 'Loading dilemmas...';
        else if (componentKey === 'agenda') message = 'Loading agendas...';
        else if (componentKey === 'ngram')
          message = 'Loading N-gram visualization...';
        else if (componentKey === 'frequency')
          message = 'Generating frequency analysis...';
        else if (componentKey === 'sentiment')
          message = 'Analyzing sentiment...';

        const spinner = createLoader(message);
        component.container.append(spinner);
        break;

      case 'failed':
        // Show error message
        const errorMsg = errorMessage || 'An error occurred during processing.';
        const errorDiv = createErrorMessage(errorMsg);
        component.container.append(errorDiv);
        break;

      case 'completed':
        if (componentKey === 'agenda') {
          const header = $('<h5/>', {
            class: 'header',
            text: 'List of Agendas',
          });
          component.container.append(header);
        }

        break;
    }
  }

  function fetchResults() {
    axios
      .get(`/cep_agenda_generation/${project_id}/results`)
      .then((response) => {
        const data = response.data;

        // Update status manager with latest status from server
        if (data.data.project) {
          const project = data.data.project;
          statusManager.dilemma.status = project.dilemma_status;
          statusManager.dilemma.errorMessage = project.dilemma_error_message;
          statusManager.agenda.status = project.agenda_status;
          statusManager.agenda.errorMessage = project.agenda_error_message;
          statusManager.ngram.status = project.ngram_status;
          statusManager.ngram.errorMessage = project.ngram_error_message;
          statusManager.frequency.status = project.analyzer_status;
          statusManager.frequency.errorMessage = project.analyzer_error_message;
          statusManager.sentiment.status = project.sentiment_analyzer_status;
          statusManager.sentiment.errorMessage =
            project.sentiment_analyzer_error_message;
        }

        // Handle dilemmas based on status
        if (
          statusManager.dilemma.status === 'completed' &&
          $('#dilemmas-list').children('.accordion-item').length == 0
        ) {
          const header = $('<h5/>', {
            class: 'header',
            html: `
            List of Dilemmas
            <div class="js-tooltip">
                                                <span class="info-symbol ms-1"><i class="fa fa-info"></i></span>
                                                <div class="tooltip-content">A short description of every dilemma and the adjacent questions will be shown</div>
                                        </div>
            `,
          });
          dilemma_container.empty();
          dilemma_container.append(header);
          
          dilemma_container.append(dilemmas_list);
          if (data.data.dilemma.length > 0) {
            dilemmas = data.data.dilemma;
            const showMoreBtn = $('<button/>', {
              class: 'mt-3 d-none',
              text: 'Show More',
              id: 'show-more-dilemmas',
              style: 'font-size: 15px;padding: 8px 15px; margin-bottom: 50px;',
            });

            data.data.dilemma.forEach((dilemma, index) => {
              let questions = [];
              try {
                questions = JSON.parse(dilemma.questions || '[]');
              } catch (e) {
                console.error('Error parsing questions:', e);
                questions = [];
              }

              const listItem = $(`<div class="accordion-item ${
                index >= 15 ? 'd-none' : ''
              }" id="dilemma-${dilemma.id}">
                  <h2 class="accordion-header" id="dilemmaHeading${index}">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#dilemmaCollapse${index}" aria-expanded="false"
                      aria-controls="dilemmaCollapse${index}">
                      ${dilemma.title}
                    </button>
                  </h2>
                  <div id="dilemmaCollapse${index}" class="accordion-collapse collapse"
                    aria-labelledby="dilemmaHeading${index}" data-bs-parent="#dilemmas-list">
                    <div class="accordion-body">
                      <p class="mb-3">${dilemma.description}</p>
                      ${
                        questions.length > 0 &&
                        !(questions.length === 1 && questions[0] === 'nan')
                          ? `
                        <div class="questions-section">
                          <h6 class="fw-bold">${'Questions:'}</h6>
                          <ul class="list-unstyled">
                            ${questions
                              .map((q) => `<li class="mb-2">• ${q}</li>`)
                              .join('')}
                          </ul>
                        </div>
                      `
                          : ''
                      }
                    </div>
                  </div>
                </div>`);

              dilemmas_list.append(listItem);
            });

            if (data.data.dilemma.length > 15) {
              showMoreBtn.removeClass('d-none');
              showMoreBtn.text('Show More');
              dilemmas_list.after(showMoreBtn);
            }

            showMoreBtn.on('click', function () {
              const hiddenItems = dilemmas_list.find('.accordion-item:hidden');
              const visibleItems = dilemmas_list.find(
                '.accordion-item:visible'
              );

              if (hiddenItems.length > 0) {
                hiddenItems.removeClass('d-none');
                $(this).text('Show Less');
              } else {
                visibleItems.slice(15).addClass('d-none');
                $(this).text('Show More');
              }
            });

            dilemma_priritise.removeClass('d-none');
          }
        } else if (statusManager.dilemma.status === 'failed') {
          clearInterval(pollingInterval);
          updateComponentDisplay(
            'dilemma',
            statusManager.dilemma.status,
            statusManager.dilemma.errorMessage
          );
        }

        // Handle agendas based on status
        if (
          statusManager.agenda.status === 'completed' &&
          data.data.agenda.length > 0
        ) {
          clearInterval(pollingInterval);
          agenda_container.empty();
          const header = $('<h5/>', {
            class: 'header',
            html: `List of Agendas
            
              <div class="js-tooltip">
                                                        <span class="info-symbol ms-1">
                                                            <i class="fa fa-info"></i>
                                                        </span>
                                                        <div class="tooltip-content">
                                                            The dilemmas have been grouped
                                                            under the agendas, and references have been added for
                                                    transparency
                                                        </div>
                                                    </div>
            `,
          });
          agenda_container.append(header);
          agenda_container.append(agendas_list);
          data.data.agenda.forEach((agenda, index) => {
            // const listItem = `  <li class="list-group-item">
            //                                   <strong style="text-transform: capitalize">  ${agenda.title}</strong>
            //                           </li>`;

            const listItem = `  <div class="accordion-item">
                                            <h2 class="accordion-header" id="heading${index}">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                                    data-bs-target="#collapse${index}" aria-expanded="false"
                                                    aria-controls="collapse${index}">
                                                    ${agenda.title}
                                                </button>
                                            </h2>
                                            <div id="collapse${index}" class="accordion-collapse collapse"
                                                aria-labelledby="heading${index}" data-bs-parent="#agendaAccordion">
                                                <div class="accordion-body">
                                                <ul>
                                                  <li><p class="fw-bold">${'Grouped Dilemmas for the respective Agenda:'}</p></li>
                                                  <ul>
                                                    ${agenda.dilemmas
                                                      .map((dilemma, idx) => {
                                                        return `<li class="fw-bold"><a href="#dilemma-${dilemma.id}" class="text-decoration-none" >${dilemma.title}</a></li>`;
                                                      })
                                                      .join('')}
                                                  </ul>
                                                </ul>
                                                
                                                <p class="fw-bold"><i>${'Reference'}</i></p>
                                                <ul>
                                                ${
                                                  agenda.reference.pdf &&
                                                  agenda.reference.pdf.length >
                                                    0
                                                    ? `
                                                  <li>
                                                    <strong>${'PDF'}</strong>
                                                    <ul>
                                                      ${agenda.reference.pdf
                                                        .map(
                                                          (pdf) =>
                                                            `<li class="">${pdf}</li>`
                                                        )
                                                        .join('')}
                                                    </ul>
                                                  </li>
                                                `
                                                    : ''
                                                }


                                                ${
                                                  agenda.reference
                                                    .printed_media &&
                                                  agenda.reference.printed_media
                                                    .length > 0
                                                    ? `
                                                  <li>
                                                    <strong>${'Printed media'}</strong>
                                                    <ul>
                                                      ${agenda.reference.printed_media
                                                        .map(
                                                          (
                                                            printed_media
                                                          ) => `<li class="">
                                                      <a href="${printed_media}" target="_blank">${printed_media}</a></li>`
                                                        )
                                                        .join('')}
                                                    </ul>
                                                  </li>
                                                `
                                                    : ''
                                                }

                                                ${
                                                  agenda.reference
                                                    .social_media &&
                                                  agenda.reference.social_media
                                                    .length > 0
                                                    ? `
                                                  <li>
                                                    <strong>${'Social media'}</strong>
                                                    <ul>
                                                      ${agenda.reference.social_media
                                                        .map(
                                                          (social_media) =>
                                                            `<li class=""><a href="${social_media}" target="_blank">${social_media}</a></li>`
                                                        )
                                                        .join('')}
                                                    </ul>
                                                  </li>
                                                `
                                                    : ''
                                                }
                                                </ul>
                                                </div>
                                            </div>
                                        </div>`;
            agendas_list.append(listItem);
          });
        } else if (statusManager.agenda.status === 'failed') {
          clearInterval(pollingInterval);
          updateComponentDisplay(
            'agenda',
            statusManager.agenda.status,
            statusManager.agenda.errorMessage
          );
        }

        // Handle n-gram based on status
        if (
          statusManager.ngram.status === 'completed' &&
          data.data.n_gram &&
          $('#ngram-image').length == 0
        ) {
          const ngramImage = $('<img/>', {
            src: `data:image/png;base64,${data.data.n_gram.attachment}`,
            alt: 'N-gram Visualization',
            class: 'img-fluid mb-4',
            style: 'max-width: 100%; height: auto;',
            id: 'ngram-image',
          });
          ngramContainer.empty().append(ngramImage);
        } else if (statusManager.ngram.status === 'failed') {
          updateComponentDisplay(
            'ngram',
            statusManager.ngram.status,
            statusManager.ngram.errorMessage
          );
        }
      })
      .catch((error) => {
        console.error('❌ Error fetching results:', error);
      });
  }

  // Initialize component displays on page load
  initializeComponentDisplays();

  // Start polling every 5 seconds if status is "in_progress"
  let pollingInterval = setInterval(fetchResults, 5000);

  // Fetch immediately on page load
  fetchResults();

  $('#submit-prioritize').click(function () {
    const project_id = $('#project_id').val();

    let criteria1 = (parseFloat($('#criteria1').val()) || 0) * 10;
    let criteria2 = (parseFloat($('#criteria2').val()) || 0) * 10;
    let criteria3 = (parseFloat($('#criteria3').val()) || 0) * 10;
    let criteria4 = (parseFloat($('#criteria4').val()) || 0) * 10;

    let total = (criteria1 + criteria2 + criteria3 + criteria4) / 10;

    if (total !== 1) {
      alert(
        `The sum of all criteria weights must be exactly 1. Total: ${total}`
      );
      return;
    }

    let data = {
      env: criteria1 / 10,
      eco: criteria2 / 10,
      soc: criteria3 / 10,
      tech: criteria4 / 10,
    };

    axios
      .post(`/cep_agenda_generation/${project_id}/prioritize`, data)
      .then((response) => {
        location.reload();
      })
      .catch((error) => {
        alert('An error occurred while submitting data.');
        console.error(error);
      });
  });

  // Frequency Analysis
  let frequencyPollingInterval = null;
  const frequencyContainer = $('#frequency-container');

  // Initialize frequency display based on current status
  updateComponentDisplay(
    'frequency',
    statusManager.frequency.status,
    statusManager.frequency.errorMessage
  );

  
    $('#pills-frequency-tab').on('click', function () {
      // Update status to running and show spinner
      console.log('\x1b[41m%s\x1b[0m' ,'📌 results-507-> statusManager.frequency =>', statusManager.frequency);
      if (statusManager.frequency.status !== 'idle') {
          return;
      }

      statusManager.frequency.status = 'running';
      updateComponentDisplay('frequency', 'running', '');

      const project_id = $('#project_id').val();

      axios
        .post(`/cep_agenda_generation/get_frequency`, {
          project_id: project_id,
        })
        .then((response) => {
          frequencyPollingInterval = setInterval(pollFrequencyData, 5000);
        })
        .catch((error) => {
          console.error('Error:', error);
          $btn.prop('disabled', false);
          statusManager.frequency.status = 'failed';
          statusManager.frequency.errorMessage =
            'Failed to start frequency analysis. Please try again.';
          updateComponentDisplay(
            'frequency',
            'failed',
            statusManager.frequency.errorMessage
          );
        });
    });
  

  function pollFrequencyData() {
    axios
      .get(`/cep_agenda_generation/${project_id}/frequency`)
      .then((response) => {
        const data = response.data;

        // Update status manager
        statusManager.frequency.status = data.status;

        if (data.status === 'running') {
          updateComponentDisplay('frequency', 'running', '');
          return;
        }

        if (data.status === 'idle' || data.status === 'completed') {
          clearInterval(frequencyPollingInterval);
        }

        if (data.status === 'completed' && data.data && data.data.length > 0) {
          const row = $('<div/>', {
            class: 'row g-4',
          });

          // Create three columns for the images
          data.data.map((image, i) => {
            const col = $('<div/>', {
              class: 'col-md-4',
            });

            const frequencyImage = $('<img/>', {
              src: `data:image/png;base64,${image.attachment}`,
              alt: `Frequency Analysis ${i + 1}`,
              class: 'img-fluid rounded shadow-sm',
              style: 'width: 100%; height: auto; object-fit: contain;',
            });

            col.append(frequencyImage);
            row.append(col);
          });

          frequencyContainer.empty().append(row);
          statusManager.frequency.status = 'completed';
        } else if (data.status === 'failed') {
          statusManager.frequency.errorMessage =
            'Error loading frequency analysis.';
          updateComponentDisplay(
            'frequency',
            'failed',
            statusManager.frequency.errorMessage
          );
        } else {
          updateComponentDisplay('frequency', data.status, '');
        }
      })
      .catch((error) => {
        console.error('Error fetching frequency data:', error);
        clearInterval(frequencyPollingInterval);
        statusManager.frequency.status = 'failed';
        statusManager.frequency.errorMessage =
          'Error loading frequency analysis.';
        updateComponentDisplay(
          'frequency',
          'failed',
          statusManager.frequency.errorMessage
        );
        $('#get-frequency-btn').prop('disabled', false).show();
      });
  }

  
  let sentimentPollingInterval = null;
  const sentimentContainer = $('#sentiment-container');

  // Initialize sentiment display based on current status
  updateComponentDisplay(
    'sentiment',
    statusManager.sentiment.status,
    statusManager.sentiment.errorMessage
  );

   // Start sentiment polling if status is completed
  if (statusManager.frequency.status === 'completed') {
    frequencyPollingInterval = setInterval(pollFrequencyData, 5000);
  }
 
    $('#pills-sentiment-tab').on('click', function () {
      
      if (youtube_source !== 1 || statusManager.sentiment.status !== 'idle') {
        return;
      }

      // Update status to running and show spinner
      statusManager.sentiment.status = 'running';
      updateComponentDisplay('sentiment', 'running', '');

      const project_id = $('#project_id').val();

      axios
        .post(`/cep_agenda_generation/get_sentiment`, {
          project_id: project_id,
        })
        .then((response) => {
          sentimentPollingInterval = setInterval(pollSentimentData, 5000);
        })
        .catch((error) => {
          console.error('Error:', error);
          $btn.prop('disabled', false);
          statusManager.sentiment.status = 'failed';
          statusManager.sentiment.errorMessage =
            'Failed to start sentiment analysis. Please try again.';
          updateComponentDisplay(
            'sentiment',
            'failed',
            statusManager.sentiment.errorMessage
          );
        });
    });
 

  function pollSentimentData() {
    axios
      .get(`/cep_agenda_generation/${project_id}/sentiment`)
      .then((response) => {
        const data = response.data;

        // Update status manager
        statusManager.sentiment.status = data.status;

        if (data.status === 'running') {
          updateComponentDisplay('sentiment', 'running', '');
          return;
        }

        if (data.status === 'idle' || data.status === 'completed') {
          clearInterval(sentimentPollingInterval);
        }

        if (data.status === 'completed' && data.data) {
          let sentimentHtml = `<div id="sentiment-${data.data.id}"></div>`;

          sentimentContainer.empty().append(sentimentHtml);
          Plotly.newPlot(
            `sentiment-${data.data.id}`,
            JSON.parse(data.data.sentiment_analysis)
          );
        } else if (data.status === 'failed' || data.error) {
          statusManager.sentiment.errorMessage =
            data.error || 'Error loading sentiment analysis.';
          updateComponentDisplay(
            'sentiment',
            'failed',
            statusManager.sentiment.errorMessage
          );
        } else {
          updateComponentDisplay('sentiment', data.status, '');
        }
      })
      .catch((error) => {
        console.error('Error fetching sentiment data:', error);
        clearInterval(sentimentPollingInterval);
        statusManager.sentiment.status = 'failed';
        statusManager.sentiment.errorMessage =
          'Error loading sentiment analysis.';
        updateComponentDisplay(
          'sentiment',
          'failed',
          statusManager.sentiment.errorMessage
        );
      });
  }

  // Start sentiment polling if status is completed
  if (statusManager.sentiment.status === 'completed') {
    sentimentPollingInterval = setInterval(pollSentimentData, 5000);
  }

  // Event delegation for dilemma links in agenda sections
  $(document).on('click', 'a[href^="#dilemma-"]', function (e) {
    e.preventDefault();
    const dilemmaId = $(this).attr('href').replace('#dilemma-', '');
    const dilemma = dilemmas.find((d) => d.id == dilemmaId);

    if (dilemma) {
      // Set modal title
      dilemmaModal.find('.modal-title').text(dilemma.title);

      // Parse questions if available
      let questions = [];
      try {
        questions = JSON.parse(dilemma.questions || '[]');
      } catch (e) {
        console.error('Error parsing questions:', e);
        questions = [];
      }

      // Build modal body content
      let modalBody = `<p class="mb-3">${dilemma.description}</p>`;

      // Add questions if available
      if (
        questions.length > 0 &&
        !(questions.length === 1 && questions[0] === 'nan')
      ) {
        modalBody += `
            <div class="questions-section mt-4">
              <h6 class="fw-bold">Questions:</h6>
              <ul class="list-unstyled">
                ${questions.map((q) => `<li class="mb-2">• ${q}</li>`).join('')}
              </ul>
            </div>
          `;
      }

      // Set modal body content
      dilemmaModal.find('.modal-body').html(modalBody);

      // Trigger the hidden button to open the modal
      $('#open-dilemma-modal').click();
    }
  });
});
