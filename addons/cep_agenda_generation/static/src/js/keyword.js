$(document).ready(function () {
  let files = [];
  let uploadedFiles = [];

  $('#upload-button, #upload-text').click(function () {
    $('#upload-paper').click();
  });

  $('#upload-paper').change(function () {
    files = Array.from(this.files); // Convert FileList to array
    const validFiles = files.filter((file) => file.type === 'application/pdf'); // Only accept PDFs

    const maxTotalSize = 50 * 1024 * 1024; // 50MB in bytes

    let totalSize = 0;

    for (let i = 0; i < validFiles.length; i++) {
      totalSize += validFiles[i].size;
    }

    if (totalSize > maxTotalSize) {
      alert(
        `Total file size (${(totalSize / 1024 / 1024).toFixed(
          2
        )}MB) exceeds 50MB limit!`
      );
      $(this).val(''); // Clear the input using jQuery
      return;
    }

    // Display valid files
    validFiles.forEach((file) => {
      uploadedFiles.push(file);
      $('#file-list').append(`<li>${file.name}</li>`);
    });

    // Show error for invalid files
    const invalidFiles = files.filter(
      (file) => file.type !== 'application/pdf'
    );
    if (invalidFiles.length > 0) {
      alert(
        `Only PDF files are allowed. The following files were skipped: ${invalidFiles
          .map((f) => f.name)
          .join(', ')}`
      );
    }
  });

  const validate = ({
    keywords,
    selected_print_media,
    selected_social_media,
    project_id,
    target_country,
    start_year,
    end_year,
    has_any_pdfs,
    predefinedPdfIds,
    use_ontology,
  }) => {
    let isValid = true;
    if (!keywords.trim()) {
      $('#keywords-input').addClass('is-invalid');
      $('#keywords-error').text('Please enter at least one keyword.');
      isValid = false;
    } else {
      $('#keywords-input').removeClass('is-invalid');
      $('#keywords-error').text('');
    }

    if (!project_id) {
      $('#project_id').addClass('is-invalid');
      $('#project-error').text('Project ID is required.');
      isValid = false;
    } else {
      $('#project_id').removeClass('is-invalid');
      $('#project-error').text('');
    }
    if (!target_country && (has_any_pdfs || predefinedPdfIds.length > 0)) {
      $('#country').addClass('is-invalid');
      $('#country-error').text('Please select a country.');
      isValid = false;
    } else {
      $('#country').removeClass('is-invalid');
      $('#country-error').text('');
    }
    

    // if (keywords.trim() && (selected_social_media.length === 0 && selected_print_media.length === 0)) {
    //   $('#media-error').text('Please select at least one media source.');
    //   isValid = false;
    // } else {
    //   $('#media-error').text('');
    // }

    // ✅ Validation: Start Year Required
    if (!start_year) {
      $('#start-year').addClass('is-invalid');
      $('#start-year-error').text('Start year is required.');
      isValid = false;
    } else {
      $('#start-year').removeClass('is-invalid');
      $('#start-year-error').text('');
    }

    // ✅ Validation: End Year Required
    if (!end_year) {
      $('#end-year').addClass('is-invalid');
      $('#end-year-error').text('End year is required.');
      isValid = false;
    } else {
      $('#end-year').removeClass('is-invalid');
      $('#end-year-error').text('');
    }

    // ✅ Validation: Start Year should be smaller than End Year
    if (start_year && end_year && parseInt(start_year) > parseInt(end_year)) {
      $('#start-year').addClass('is-invalid');
      $('#end-year').addClass('is-invalid');
      $('#start-year-error').text('Start year must be smaller than end year.');
      isValid = false;
    } else {
      $('#start-year').removeClass('is-invalid');
      $('#end-year').removeClass('is-invalid');
      $('#start-year-error').text('');
    }

    if (
      !has_any_pdfs &&
      selected_social_media.length === 0 &&
      selected_print_media.length === 0
    ) {
      const error_message = $('#error-message');
      const error_text_el = document.getElementById('toast-error-text');
      
      error_text_el.textContent =
        'Please upload at least one PDF or select at least one media source.';
      error_message.addClass('show');
      setTimeout(() => {
        error_message.removeClass('show');
      }, 3000);
      isValid = false;
    }

    if (use_ontology && (!has_any_pdfs && predefinedPdfIds.length === 0)) {
      $('#ontology-checkbox-label').addClass('is-invalid');
      $('#use-ontology-error').text('Please upload at least one PDF or select predefined PDF.');
      isValid = false;
    }else{
      $('#ontology-checkbox-label').removeClass('is-invalid');
      $('#use-ontology-error').text('');
    }



    
    return isValid;
  };

  // const success_message = document.getElementById('success-message');

  if (typeof Tagify == 'undefined' || Tagify == null) return;

  // const success_message_toast = new bootstrap.Toast(success_message);

  const input = document.querySelector('#keywords-input'),
    tagify = new Tagify(input, {
      enforceWhitelist: false,
      whitelist: [
        'climate change',
        'global warming',
        'sustainability',
        'greenhouse gases',
        'carbon emissions',
        'renewable energy',
        'carbon footprint',
        'climate action',
        'climate crisis',
        'environmental impact',
        'green economy',
        'energy transition',
        'rising temperatures',
        'climate adaptation',
        'climate mitigation',
        'fossil fuels',
        'net zero',
        'decarbonization efforts',
        'extreme weather',
        'ocean acidification',
        'sea level rise',
        'arctic ice melt',
        'climate impacts',
        'carbon neutrality',
        'paris agreement',
        'solar power',
        'wind energy',
        'geothermal energy',
        'hydropower systems',
        'bioenergy technologies',
        'renewable fuels',
        'green hydrogen',
        'wind turbines',
        'solar panels',
        'biomass energy',
        'energy efficiency',
        'renewable transition',
        'zero-carbon grid',
        'clean energy',
        'smart grids',
        'energy storage',
        'sustainable power',
        'electric vehicles',
        'energy innovation',
        'community solar',
        'carbon offsets',
        'emission reduction',
        'methane leaks',
        'low-carbon technologies',
        'carbon sequestration',
        'co2 removal',
        'industrial emissions',
        'carbon pricing',
        'carbon tax',
        'greenhouse effect',
        'carbon intensity',
        'transportation emissions',
        'agricultural emissions',
        'emission controls',
        'carbon capture',
        'net emissions',
        'emissions gap',
        'scope 3 emissions',
        'zero-emission targets',
        'coral bleaching',
        'glacier retreat',
        'habitat loss',
        'biodiversity loss',
        'deforestation impacts',
        'melting permafrost',
        'drought conditions',
        'flood damages',
        'ocean warming',
        'climate refugees',
        'dying ecosystems',
        'rising oceans',
        'extreme heatwaves',
        'wildfire risks',
        'soil degradation',
        'forest fires',
        'species extinction',
        'desertification threats',
        'water scarcity',
        'habitat destruction',
        'climate policies',
        'global agreements',
        'environmental justice',
        'green investments',
        'carbon trading',
        'energy regulations',
        'green legislation',
        'international climate',
        'climate finance',
        'esg initiatives',
        'cop negotiations',
        'climate governance',
        'adaptation policies',
        'clean tech funding',
        'emission pledges',
        'sustainability laws',
        'green innovation',
        'corporate responsibility',
        'environmental standards',
        'climate activism',
        'personal footprint',
        'recycling programs',
        'renewable choices',
        'eco-friendly practices',
        'green living',
        'low-carbon lifestyle',
        'sustainable habits',
        'reforestation campaigns',
        'plastic reduction',
        'urban gardening',
        'ethical consumption',
        'water conservation',
        'smart commuting',
        'energy saving',
        'diy solar',
        'public transport',
        'climate protests',
        'local advocacy',
        'green communities',
        'climate modeling',
        'weather patterns',
        'jet stream shifts',
        'urban heat islands',
        'ocean currents',
        'climate data',
        'polar vortex',
        'climate tipping points',
        'albedo effect',
        'radiative forcing',
        'feedback loops',
        'heat absorption',
        'carbon budgeting',
        'ice core studies',
        'arctic amplification',
        'seasonal variability',
        'long-term trends',
        'temperature records',
        'atmospheric layers',
        'greenhouse balance',
        'sustainable business',
        'green markets',
        'renewable investments',
        'eco-tourism industry',
        'corporate greenwashing',
        'climate insurance',
        'green job market',
        'circular economy',
        'sustainable supply chains',
        'esg reporting',
        'green startups',
        'decarbonization costs',
        'energy innovation hubs',
        'sustainable manufacturing',
        'corporate sustainability',
        'carbon credits',
        'clean tech jobs',
        'sustainable packaging',
        'climate entrepreneurship',
        'green construction',
        'environmental education',
        'climate awareness',
        'youth climate action',
        'environmental stewardship',
        'green technology',
        'local adaptation',
        'eco-friendly schools',
        'sustainable cities',
        'climate literacy',
        'community energy',
        'grassroots movements',
        'local sustainability',
        'public engagement',
        'advocacy groups',
        'environmental campaigns',
        'renewable outreach',
        'civic engagement',
        'energy cooperatives',
        'sustainable development',
        'community resilience',
        'afforestation projects',
        'rewilding initiatives',
        'nature restoration',
        'mangrove planting',
        'wetland conservation',
        'forest carbon sinks',
        'green infrastructure',
        'soil carbon storage',
        'ecosystem services',
        'natural flood defenses',
        'biodiversity corridors',
        'sustainable forestry',
        'agroecological farming',
        'urban greening',
        'coastal resilience',
        'river restoration',
        'wildlife corridors',
        'organic farming',
        'forest regeneration',
        'habitat restoration',
      ],
      callbacks: {
        add: console.log, // callback when adding a tag
        remove: console.log, // callback when removing a tag
      },
    });

  const save_button = $('#proceed-btn');
  const button_text = $('#proceed-button-text');
  const button_loader = $('#proceed-button-loader');
  save_button.on('click', function () {
    const target_country = $('#country').val();
    const start_year = $('#start-year').val();
    const end_year = $('#end-year').val();
    const pdf_source_id = $('#pdf_source_id').val() || null;
    

    const tagsArray = tagify.value.map((tag) => tag.value);
    const keywords = tagsArray.join(',');

    const has_any_pdfs =
      files.length > 0 || $('#file-list-server').children('li').length > 0;

    const selected_social_media = [];
    $('.media-checkbox:checked').each(function () {
      selected_social_media.push($(this).val());
    });

    const selected_print_media = [];
    $('.printed-media-checkbox:checked').each(function () {
      selected_print_media.push($(this).val());
    });

    const project_id = $('#project_id').val();
    const social_media_source_id = $('#social_media_source_id').val() || null;
    const printed_media_source_id = $('#printed_media_source_id').val() || null;

    const predefinedPdfIds = [];
    $('.predefined-pdf-checkbox:checked').each(function () {
      predefinedPdfIds.push(parseInt($(this).val()));
    });

    const use_ontology = $('#use-ontology-checkbox').is(':checked') ? 1 : 0;
    const isValid = validate({
      keywords,
      selected_social_media,
      selected_print_media,
      project_id,
      target_country,
      start_year,
      end_year,
      has_any_pdfs,
      predefinedPdfIds,
      use_ontology,
    });

    if (!isValid) return;

    const formData = new FormData();
    files.forEach((file) => formData.append('files[]', file));

    if (pdf_source_id) {
      formData.append('pdf_source_id', pdf_source_id);
    }
    if (social_media_source_id) {
      formData.append('social_media_source_id', social_media_source_id);
    }
    if (printed_media_source_id) {
      formData.append('printed_media_source_id', printed_media_source_id);
    }

    if (predefinedPdfIds.length > 0) {
      formData.append('predefined_pdf_ids', JSON.stringify(predefinedPdfIds));
    }

    formData.append('target_country', target_country);
    formData.append('start_year', start_year);
    formData.append('end_year', end_year);
    formData.append('project_id', project_id);
    formData.append('use_ontology', use_ontology);
    formData.append(
      'selected_social_media',
      JSON.stringify(selected_social_media)
    );
    formData.append(
      'selected_print_media',
      JSON.stringify(selected_print_media)
    );


    formData.append('keywords', keywords);
    button_text.hide();
    button_loader.show();
    axios
      .post('/cep_agenda_generation/process_data', formData)
      .then((response) => {
        window.location.href = `/cep_agenda_generation/${project_id}/output`;
      })
      .catch((error) => {
        alert('An error occurred');
      });
  });
});

$(document).ready(function () {
  // Form validation function
  function validateForm() {
    let isValid = true;
    const title = $('#edit-project-title').val().trim();
    const description = $('#edit-project-description').val().trim();

    // Reset previous error states
    $('#edit-title-error, #edit-description-error').text('');
    $('#edit-project-title, #edit-project-description').removeClass(
      'is-invalid'
    );

    // Validate title
    if (!title) {
      $('#edit-project-title').addClass('is-invalid');
      $('#edit-title-error').text('Title is required');
      isValid = false;
    } else if (title.length > 100) {
      $('#edit-project-title').addClass('is-invalid');
      $('#edit-title-error').text('Title must be at most 100 characters');
      isValid = false;
    }

    // Validate description
    if (!description) {
      $('#edit-project-description').addClass('is-invalid');
      $('#edit-description-error').text('Description is required');
      isValid = false;
    } else if (description.length > 500) {
      $('#edit-project-description').addClass('is-invalid');
      $('#edit-description-error').text(
        'Description must be at most 500 characters'
      );
      isValid = false;
    }

    return isValid;
  }

  // Handle edit project form submission
  $('#edit-project-form').on('submit', function (e) {
    e.preventDefault();

    if (!validateForm()) {
      return false;
    }

    const projectId = $('#project_id').val();
    const formData = {
      'project-title': $('#edit-project-title').val().trim(),
      'project-description': $('#edit-project-description').val().trim(),
    };

    // Show loading state
    const $submitBtn = $('#edit-project-submit');
    const $spinner = $('#edit-project-spinner');
    $submitBtn.prop('disabled', true);
    $submitBtn.find('span').hide();
    $spinner.show();

    // Make API call
    axios
      .post(`/cep_agenda_generation/${projectId}/edit`, formData)
      .then(function (response) {
        if (response.data.status) {
          // Update page title and project title in the UI
          $('.heading-4')
            .first()
            .contents()
            .first()
            .replaceWith(formData.title);
          $('#editProjectModal').modal('hide');
          window.location.reload();
        }
      })
      .catch(function (error) {
        console.error('Error:', error);

        // Show error message
        const errorMessage =
          error.response?.data?.message ||
          error.message ||
          'Failed to update project';
        const errorAlert = $('<div>')
          .addClass('alert alert-danger alert-dismissible fade show')
          .attr('role', 'alert').html(`
                      ${errorMessage}
                      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  `);

        $('.main-content').prepend(errorAlert);
      })
      .finally(function () {
        // Reset button state
        $submitBtn.prop('disabled', false);
        $submitBtn.find('span').show();
        $spinner.hide();
      });
  });

  // Reset form validation state when modal is hidden
  $('#editProjectModal').on('hidden.bs.modal', function () {
    $('#edit-project-title, #edit-project-description').removeClass(
      'is-invalid'
    );
    $('#edit-title-error, #edit-description-error').text('');
  });
});

$(document).ready(function () {
  // Initialize the selected count when the page loads
  updateSelectedCount();

  // Event handler for "Select All" button
  $('#select-all-pdfs').on('click', function () {
    $('.predefined-pdf-checkbox').prop('checked', true);
    updateSelectedCount();
  });

  // Event handler for "Deselect All" button
  $('#deselect-all-pdfs').on('click', function () {
    $('.predefined-pdf-checkbox').prop('checked', false);
    updateSelectedCount();
  });

  // Event handler for checkbox changes
  $('.predefined-pdf-checkbox').on('change', function () {
    updateSelectedCount();
  });

  // Function to update the selected PDFs count
  function updateSelectedCount() {
    let count = $('.predefined-pdf-checkbox:checked').length;
    $('#selected-pdfs-count').text(count);
  }
});
