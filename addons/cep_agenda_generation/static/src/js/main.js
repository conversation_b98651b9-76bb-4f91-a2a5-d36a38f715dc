$(document).ready(function () {
    // Handle submenu toggle
    $('#create-project-menu').click(function(e) {
        e.preventDefault();
        const $submenu = $(this).siblings('.submenu');
        const $icon = $(this).find('.submenu-icon');
        $submenu.toggleClass('open');
        $icon.toggleClass('open');
    });

    const project_id = $('#project_id').val();

    const saved_project_id = localStorage.getItem('project_id');
    const active_tab_header = localStorage.getItem('active_tab_header');
    if(saved_project_id === project_id){
        if (active_tab_header ) {
            $(`#${active_tab_header}, #${active_tab_header}-tab`).addClass('active show');
        }else{
            $('#keywords-tab, #keywords').addClass('active show');
        }
    }else {
        localStorage.removeItem('project_id');
        localStorage.removeItem('active_tab_header');
        $('#keywords-tab, #keywords').addClass('active show');

    }
    




    $('#sidebar-toggle').click(function () {
        const $sidebar = $('.sidebar');
        const $icon = $('#sidebar-icon');

        // Toggle the 'open' class for the sidebar
        $sidebar.toggleClass('open');

        // Toggle between 'fa-bars' and 'fa-close' icons
        $icon.toggleClass('fa-bars fa-close');
    });



    $('.project-favorite').on('click', function(e) {
        e.preventDefault();
        
        const $btn = $(this);
        const $icon = $btn.find('i');
        const project_id = $('#project_id').val();

        
        axios.get(`/cep_agenda_generation/project/${project_id}/favorite`)
        .then(function(response) {
            if (response.data.status) {
                // Toggle star icon
                if ($icon.hasClass('fa-star-o')) {
                    $icon.removeClass('fa-star-o').addClass('fa-star');
                } else {
                    $icon.removeClass('fa-star').addClass('fa-star-o');
                 
                }
            } 
        })
        .catch(function(error) {
            console.error('Error:', error);
            alert('Failed to update favorite status');
        });
    });

    $('.delete-project').click(function (e) {
        e.preventDefault();
    
        const title = $(this).closest('tr').find('.title').text().trim();
        const href = $(this).attr('href');
        const bs_target = $(this).data('bs-target');
    
        $(bs_target).find('.modal-footer a').attr('href', href);
        $(bs_target).find('.modal-body span').text(title);
      });
});



$(document).ready(function() {
    $('#dilemma-upload-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = new FormData(this);
        var messageDiv = $('#upload-messages');
        var spinner = $('#loading-spinner');
        
        // Show spinner
        spinner.removeClass('d-none');
        messageDiv.empty();
        
        $.ajax({
            url: '/cep_agenda_generation/upload_dilemmas',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                spinner.addClass('d-none');
                var result = JSON.parse(response);
                
                if (result.status === 'success') {
                    messageDiv.html('<div class="alert alert-success">' + result.message + '</div>');
                } else {
                    messageDiv.html('<div class="alert alert-danger">' + result.message + '</div>');
                }
            },
            error: function() {
                spinner.addClass('d-none');
                messageDiv.html('<div class="alert alert-danger">An error occurred during upload. Please try again.</div>');
            }
        });
    });

    let timeout;

  // Mouse events for desktop (using event delegation)
  $(document).on('mouseenter', '.js-tooltip', function () {
    clearTimeout(timeout);
    $(this).addClass('active');
  });

  $(document).on('mouseleave', '.js-tooltip', function () {
    const $tooltip = $(this);
    timeout = setTimeout(() => {
      $tooltip.removeClass('active');
    }, 100);
  });

  // Touch events for mobile (using event delegation)
  $(document).on('touchstart', '.js-tooltip', function (e) {
    e.preventDefault();
    $(this).toggleClass('active');
  });

  // Close tooltip when touching outside
  $(document).on('touchstart', function (e) {
    if (!$(e.target).closest('.js-tooltip').length) {
      $('.js-tooltip').removeClass('active');
    }
  });
});