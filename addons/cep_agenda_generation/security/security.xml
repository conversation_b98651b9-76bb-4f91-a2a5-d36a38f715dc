<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Portal Access Rules -->
        <record id="agenda_portal_access_rule" model="ir.rule">
            <field name="name">Portal Access to Agenda Projects</field>
            <field name="model_id" ref="model_cep_ag_project"/>
            <field name="domain_force">[('create_uid', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]" />
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <record id="agenda_source_portal_access_rule" model="ir.rule">
            <field name="name">Portal Access to Agenda Sources</field>
            <field name="model_id" ref="model_cep_ag_agenda_source"/>
            <field name="domain_force">[('project_id.create_uid', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]" />
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <record id="agenda_result_portal_access_rule" model="ir.rule">
            <field name="name">Portal Access to Agenda Results</field>
            <field name="model_id" ref="model_cep_ag_result"/>
            <field name="domain_force">[('project_id.create_uid', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]" />
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
    </data>
</odoo>