<odoo>
    <data noupdate="1">
        <!-- Public Access to Agenda Attachments Rule -->
        <record id="agenda_attachment_public_rule" model="ir.rule">
            <field name="name">Public Access to Agenda Attachments</field>
            <field name="model_id" ref="base.model_ir_attachment"/>
            <field name="domain_force">[('res_model', '=', 'cep.ag.agenda_source'), ('public', '=', True)]</field>
            <field name="groups" eval="[(4, ref('base.group_public'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
    </data>
</odoo>
