# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_agenda_generation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20250520\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 09:12+0000\n"
"PO-Revision-Date: 2025-05-23 09:12+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-bar-chart me-2\"/> Keyword Frequency Over Years"
msgstr "Fréquence des mots clés au fil des ans"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Climate Assembly"
msgstr "<i class=\"fa fa-circle-o\"/> Assemblée pour le climat"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Climate Dialogue"
msgstr "<i class=\"fa fa-circle-o\"/> Dialogue sur le climat"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Just Transition Dialogue"
msgstr "Dialogue sur la transition juste"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"<i class=\"fa fa-heart\"/>\n"
"                            Favorites"
msgstr ""
"<i class=\"fa fa-heart\"/>\n"
"Favoris"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-heart\"/> Favorites"
msgstr "<i class=\"fa fa-heart\"/> Favoris"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-home\"/> Home"
msgstr "Accueil"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-line-chart me-2\"/> Most common words frequency"
msgstr "<i class=\"fa fa-line-chart me-2\"/> Fréquence des mots les plus courants"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-list-alt me-2\"/> List of Agendas"
msgstr "<i class=\"fa fa-list-alt me-2\"/> Liste des ordres du jour"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid ""
"<i class=\"fa fa-plus\"/> Create Agenda\n"
"                                <i class=\"fa fa-chevron-down submenu-icon\"/>"
msgstr ""
"Créer un agenda\n"
"<i class=\"fa fa-chevron-down submenu-icon\"/>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<i class=\"fa fa-plus\"/> Create Agenda <i class=\"fa fa-chevron-down "
"submenu-icon\"/>"
msgstr ""
"<i class=\"fa fa-plus\"/> Créer un agenda <i class=\"fa fa-chevron-down "
"submenu-icon\"/>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<i class=\"fa fa-question-circle me-2\"/> List of\n"
"                                                Dilemmas"
msgstr "<i class=\"fa fa-question-circle me-2\"/> Liste des dilemmes"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid ""
"<i class=\"fa fa-save\"/>\n"
"                            Project list"
msgstr ""
"<i class=\"fa fa-save\"/>\n"
"Liste des projets"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"<i class=\"fa fa-save\"/> \n"
"                            Project list"
msgstr ""
"<i class=\"fa fa-save\"/> \n"
"Liste des projets"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-save\"/> Project list"
msgstr "<i class=\"fa fa-save\"/> Liste des projets"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<option value=\"\">Select a country</option>"
msgstr "<option value=\"\">Sélectionnez un pays</option>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid ""
"<small class=\"text-muted\">Please upload an Excel file with the following "
"columns: Dilemma ID, Title, Description, Questions, ENV, ECO, SOC, "
"TECH</small>"
msgstr ""
"<small class=\"text-muted\">Veuillez télécharger un fichier Excel avec les "
"colonnes suivantes : ID du dilemme, Titre, Description, Questions, ENV, ECO,"
" SOC, TECH</small>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "<span class=\"badge badge-pill badge-secondary\">NA</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">BBC</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">Nature</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The Guardian</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The New York Times</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The Washington Post</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">Twitter</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">YouTube</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-book\"/> Upload PDFs of relevant document (Academic Paper/Journal/Report) </span>"
msgstr ""
"<span class=\"fw-bold\">\n"
"<i class=\"fa fa-book\"/> Télécharger les PDF des documents pertinents (article universitaire, revue, rapport) </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-newspaper-o\"/> Media </span>"
msgstr ""
"<span class=\"fw-bold\">\n"
"<i class=\"fa fa-newspaper-o\"/> Médias </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "<span class=\"visually-hidden\">Loading...</span>"
msgstr "<span class=\"visually-hidden\">Chargement...</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span id=\"proceed-button-text\">Proceed</span>\n"
"                                            <span id=\"proceed-button-loader\" class=\"spinner-border spinner-border-sm text-light\" style=\"display: none;\" role=\"status\">\n"
"                                                <span class=\"visually-hidden\">Loading...</span>\n"
"                                            </span>"
msgstr ""
"<span id=\"proceed-button-text\">Continuer</span>\n"
"<span id=\"proceed-button-loader\" class=\"spinner-border spinner-border-sm text-light\" style=\"display: none;\" role=\"status\">\n"
"<span class=\"visually-hidden\">Chargement…</span>\n"
"</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>Delete</span>"
msgstr "<span>Supprimer</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>Edit</span>"
msgstr "<span>Modifier</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span>Save Changes</span>\n"
"                                            <span id=\"edit-project-spinner\" class=\"spinner-border spinner-border-sm\" role=\"status\" style=\"display: none;\">\n"
"                                                <span class=\"visually-hidden\">Loading...</span>\n"
"                                            </span>"
msgstr ""
"<span>Enregistrer les modifications</span>\n"
"<span id=\"edit-project-spinner\" class=\"spinner-border spinner-border-sm\" role=\"status\" style=\"display: none;\">\n"
"<span class=\"visually-hidden\">Chargement…</span>\n"
"</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>View results</span>"
msgstr "<span>Voir les résultats</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<strong> Note: The sum of all the scores of criteria\n"
"                                                    must equal 1.</strong>"
msgstr ""
"Remarque : la somme de tous les scores des critères doit être égale à 1."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<strong>0.9</strong> represents the <strong>maximum\n"
"                                                impact</strong> that should be Prioritised, while <strong>\n"
"                                                0.1</strong> indicates <strong>minimum\n"
"                                                    impact</strong> according to the criteria."
msgstr ""
"<strong>0,9</strong> représente l'<strong>impact maximal</strong> à "
"prioriser, tandis que <strong>0,1</strong> indique l'<strong>impact "
"minimal</strong> selon les critères."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "AGENDA"
msgstr "ORDRE DU JOUR"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Action"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Add Keywords"
msgstr "Ajouter des mots-clés"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__agenda
msgid "Agenda"
msgstr "Ordre du jour"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Agenda Dashboard"
msgstr "Tableau de bord de l'ordre du jour"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_root
msgid "Agenda Generation"
msgstr "Génération d'agenda"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_project_action
msgid "Agenda Project"
msgstr "Projet Agenda"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_result_action
msgid "Agenda Result"
msgstr "Résultat de l'ordre du jour"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_agenda_source_action
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_agenda_source
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_agenda_source
msgid "Agenda Source"
msgstr "Source de l'ordre du jour"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Agenda Source Saved Successfully."
msgstr "Source de l'agenda enregistrée avec succès."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"All\n"
"                                                    country"
msgstr "Tous les pays"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.menu_cep_ag_dilemma
msgid "All Dilemmas"
msgstr "Tous les dilemmes"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "An error occurred while submitting data."
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "An error occurred. Please try again."
msgstr "Une erreur s'est produite. Veuillez réessayer."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__analyzer_status
msgid "Analyzer Status"
msgstr "État de l'analyseur"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Are you sure you want to delete this Project <span/>?"
msgstr "Êtes-vous sûr de vouloir supprimer ce projet <span/> ?"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__attachment
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Attachment"
msgstr "Pièce jointe"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__attachment_ids
msgid "Attachments"
msgstr "Pièces jointes"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_dilemma
msgid "CEP Agenda Generation Dilemma"
msgstr "Le dilemme de la génération de l'agenda du CEP"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_search
msgid "CEP Agenda Project"
msgstr "Projet d'agenda du CEP"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_result_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_result_search
msgid "CEP Agenda Result"
msgstr "Résultats de l'ordre du jour du CEP"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_search
msgid "CEP Agenda Source"
msgstr "Source de l'ordre du jour du CEP"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "CLIMAS Logo"
msgstr "Logo CLIMAS"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Cancel"
msgstr "Annuler"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__category
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Category"
msgstr "Catégorie"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__dilemma_ids
msgid "Child Dilemmas"
msgstr "Dilemmes des enfants"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Choose a time range (e.g., 2020–2024) to extract agenda\n"
"                                            topics based on\n"
"                                            specific years."
msgstr ""
"Choisissez une période (par exemple, 2020-2024) pour extraire les sujets de "
"l’ordre du jour en fonction d’années spécifiques."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Click \"Proceed\" to start the analysis."
msgstr "Cliquez sur « Continuer » pour démarrer l’analyse."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Click \"Upload a file/folder\" to add PDFs containing\n"
"                                            research papers or\n"
"                                            reports."
msgstr ""
"Cliquez sur « Télécharger un fichier/dossier » pour ajouter des fichiers PDF"
" contenant des articles de recherche ou des rapports."

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__climate_assembly
msgid "Climate Assembly"
msgstr "Assemblée pour le climat"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__climate_dialogue
msgid "Climate Dialogue"
msgstr "Dialogue sur le climat"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Close"
msgstr "Fermer"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__column_a
msgid "Column A"
msgstr "Colonne A"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__column_b
msgid "Column B"
msgstr "Colonne B"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__completed
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__completed
msgid "Completed"
msgstr "Complété"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Countries"
msgstr "Pays"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__target_country
msgid "Country"
msgstr "Pays"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"Create\n"
"                                    Agenda"
msgstr "Créer un agenda"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Create Agenda for"
msgstr "Créer un agenda pour"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Create Date"
msgstr "Date de création"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_agenda_source_action
msgid "Create a new Agenda Source"
msgstr "Créer une nouvelle source d'agenda"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_project_action
msgid "Create a new Project"
msgstr "Créer un nouveau projet"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_result_action
msgid "Create a new Result"
msgstr "Créer un nouveau résultat"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.action_cep_ag_dilemma
msgid "Create your first dilemma"
msgstr "Créez votre premier dilemme"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__create_date
msgid "Created on"
msgstr "Créé le"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__criteria_weights
msgid "Criteria Weights"
msgstr "Critères de pondération"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Criterion"
msgstr "Critère"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__description
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__description
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__description
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Description"
msgstr ""

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__dilemma
msgid "Dilemma"
msgstr "Dilemme"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__dilemma_serial_id
msgid "Dilemma Serial"
msgstr "Série Dilemme"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.action_cep_ag_dilemma
msgid "Dilemmas"
msgstr "Dilemmes"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"Do you want to prioritise the list of\n"
"                                            dilemmas under\n"
"                                            criteria?"
msgstr "Voulez-vous hiérarchiser la liste des dilemmes selon des critères ?"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__draft
msgid "Draft"
msgstr "Brouillon"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__eco
msgid "ECO Score"
msgstr "Score ECO"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__environment
msgid "ENV Score"
msgstr "Score ENV"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Economic Factors"
msgstr "Facteurs économiques"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Edit Project"
msgstr "Modifier le projet"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__end_year
msgid "End Year"
msgstr "Fin d'année"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Enter weight"
msgstr "Entrez le poids"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Environmental Impact"
msgstr "Impact environnemental"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__error_message
msgid "Error Message"
msgstr "Message d'erreur"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Error Reason"
msgstr "Raison de l'erreur"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__failed
msgid "Failed"
msgstr "Échoué"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__favorite
msgid "Favorite"
msgstr "Préféré"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
msgid "Favorites"
msgstr "Favoris"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__frequency_analysis
msgid "Frequency Analysis"
msgstr "Analyse de fréquence"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Generating frequency analysis..."
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Get Frequency"
msgstr "Obtenir la fréquence"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__id
msgid "ID"
msgstr "IDENTIFIANT"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__idle
msgid "Idle"
msgstr "Inactif"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__in_progress
msgid "In Progress"
msgstr "En cours"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Instructions"
msgstr ""

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__just_transition_dialogue
msgid "Just Transition Dialogue"
msgstr "Dialogue sur la transition juste"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__keyword
msgid "Keyword"
msgstr "Mot-clé"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Keyword Frequency Over Years"
msgstr "Fréquence des mots-clés au fil des ans"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__keywords
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Keywords"
msgstr "Mots-clés"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Last modified:"
msgstr "Dernière modification :"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "List of Agendas"
msgstr "Liste des ordres du jour"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "List of Dilemmas"
msgstr "Liste des dilemmes"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Loading N-gram visualization..."
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Loading agendas..."
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Loading dilemmas..."
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Mark as Favorite"
msgstr "Marquer comme favori"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__metadata
msgid "Metadata"
msgstr "Métadonnées"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Most common words frequency"
msgstr "Fréquence des mots les plus courants"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__n_gram
msgid "N Gram"
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "N-gram Visualization"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "No"
msgstr "Non"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Note: Twitter does not support filtering by year."
msgstr "Remarque : Twitter ne prend pas en charge le filtrage par année."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Output"
msgstr "Sortir"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__owner_id
msgid "Owner"
msgstr "Propriétaire"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__pdf
#, python-format
msgid "PDF"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Papers"
msgstr "Papiers"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__parent_id
msgid "Parent Agenda"
msgstr "Agenda des parents"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Print Media"
msgstr "Presse écrite"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__printed_media
msgid "Printed Media"
msgstr "Médias imprimés"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Printed media"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Prioritise"
msgstr "Prioriser"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Prioritise according to your criteria."
msgstr "Priorisez selon vos critères."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__priority
msgid "Priority"
msgstr "Priorité"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_project
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__project_id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__project_id
msgid "Project"
msgstr "Projet"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Project Delete Confirmation"
msgstr "Confirmation de suppression du projet"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Project Description"
msgstr "Description du projet"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Project Title"
msgstr "Titre du projet"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Project description"
msgstr "Description du projet"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Project:"
msgstr "Projet:"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_project
msgid "Projects"
msgstr "Projets"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__questions
msgid "Questions"
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Questions:"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Recent Projects"
msgstr "Projets récents"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__related_dilemma_id
msgid "Related Dilemma"
msgstr "Dilemme connexe"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_result
msgid "Result"
msgstr "Résultat"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__result_ids
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_result
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Results"
msgstr "Résultats"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__running
msgid "Running"
msgstr "En cours d'exécution"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "SL"
msgstr ""

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__soc
msgid "SOC Score"
msgstr "Score SOC"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Saved Projects"
msgstr "Projets enregistrés"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select from predefined print media sources (BBC, The New\n"
"                                            York Times, etc.)."
msgstr ""
"Choisissez parmi des sources de médias imprimés prédéfinies (BBC, The New "
"York Times, etc.)."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select specific countries to focus on country-specific\n"
"                                            agenda topics."
msgstr ""
"Sélectionnez des pays spécifiques pour vous concentrer sur des sujets "
"d’ordre du jour spécifiques à chaque pays."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select the social media platforms (YouTube, Twitter,\n"
"                                            Facebook, etc.) where\n"
"                                            you want to search for related data."
msgstr ""
"Sélectionnez les plateformes de médias sociaux (YouTube, Twitter, Facebook, "
"etc.) sur lesquelles vous souhaitez rechercher des données connexes."

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Show Less"
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Show More"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Social Equity"
msgstr "Équité sociale"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__social_media
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Social Media"
msgstr "Réseaux sociaux"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Social media"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Source"
msgstr ""

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__agenda_source_ids
msgid "Sources"
msgstr ""

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__start_year
msgid "Start Year"
msgstr "Année de début"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__status
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Status"
msgstr "Statut"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Submit"
msgstr "Soumettre"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__tech
msgid "TECH Score"
msgstr "Score TECH"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Technology & Innovation"
msgstr "Technologie et innovation"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "The process may take 3 to 5 minutes to complete."
msgstr "Le processus peut prendre de 3 à 5 minutes."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"The system will process your inputs and extract key\n"
"                                            topics and agendas."
msgstr ""
"Le système traitera vos entrées et extraira les sujets et ordres du jour "
"clés."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__title
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__title
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__title
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Title"
msgstr "Titre"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "To"
msgstr "À"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__type
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__type
msgid "Type"
msgstr "Taper"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Type in the keywords relevant to your agenda (e.g.,\n"
"                                            \"climate change,\"\n"
"                                            \"carbon emissions\")."
msgstr ""
"Saisissez les mots-clés pertinents pour votre programme (par exemple, "
"« changement climatique », « émissions de carbone »)."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__urls
msgid "URLs"
msgstr "URL"

#. module: cep_agenda_generation
#: model:ir.model.fields,help:cep_agenda_generation.field_cep_ag_result__dilemma_serial_id
msgid ""
"Unique identifier to link with cep.ag.dilemma model's dilemma_serial_id "
"field"
msgstr ""
"Identifiant unique à lier au champ dilemma_serial_id du modèle "
"cep.ag.dilemma"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "Upload Dilemmas"
msgstr "Dilemmes de téléchargement"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "Upload Excel File"
msgstr "Télécharger un fichier Excel"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Upload a files"
msgstr "Télécharger un fichier"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Uploaded\n"
"                                                    Papers"
msgstr "Documents téléchargés"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__year
msgid "Year"
msgstr "Année"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Years"
msgstr "Années"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Yes"
msgstr "Oui"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"You will receive an output containing agenda topics\n"
"                                            categorized by country,\n"
"                                            year, and frequency trends."
msgstr ""
"Vous recevrez un rapport contenant les sujets de l’ordre du jour classés par"
" pays, année et tendances de fréquence."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "cloud upload"
msgstr "téléchargement dans le cloud"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__dilemma_serial_id
msgid "dilemma_serial_id"
msgstr "dilemme_serial_id"
