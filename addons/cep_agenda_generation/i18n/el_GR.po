# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_agenda_generation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20250520\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 09:11+0000\n"
"PO-Revision-Date: 2025-05-23 09:11+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-bar-chart me-2\"/> Keyword Frequency Over Years"
msgstr "<i class=\"fa fa-bar-chart me-2\"/> Συχνότητα λέξεων-κλειδιών σε χρόνια"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Climate Assembly"
msgstr "<i class=\"fa fa-circle-o\"/> Συνέλευση για το κλίμα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Climate Dialogue"
msgstr "<i class=\"fa fa-circle-o\"/> Διάλογος για το κλίμα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Just Transition Dialogue"
msgstr "<i class=\"fa fa-circle-o\"/> Διάλογος Just Transition"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"<i class=\"fa fa-heart\"/>\n"
"                            Favorites"
msgstr ""
"<i class=\"fa fa-heart\"/>\n"
"                            Αγαπημένα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-heart\"/> Favorites"
msgstr "<i class=\"fa fa-heart\"/> Αγαπημένα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-home\"/> Home"
msgstr "<i class=\"fa fa-home\"/> Αρχική σελίδα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-line-chart me-2\"/> Most common words frequency"
msgstr "<i class=\"fa fa-line-chart me-2\"/> Συχνότητα συχνότερων λέξεων"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-list-alt me-2\"/> List of Agendas"
msgstr "<i class=\"fa fa-list-alt me-2\"/> Λίστα ατζέντας"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid ""
"<i class=\"fa fa-plus\"/> Create Agenda\n"
"                                <i class=\"fa fa-chevron-down submenu-icon\"/>"
msgstr ""
"<i class=\"fa fa-plus\"/> Δημιουργία ατζέντας\n"
"                                <i class=\"fa fa-chevron-down submenu-icon\"/>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<i class=\"fa fa-plus\"/> Create Agenda <i class=\"fa fa-chevron-down "
"submenu-icon\"/>"
msgstr ""
"<i class=\"fa fa-plus\"/> Δημιουργία ατζέντας <i class=\"fa fa-chevron-down "
"submenu-icon\"/>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<i class=\"fa fa-question-circle me-2\"/> List of\n"
"                                                Dilemmas"
msgstr ""
"<i class=\"fa fa-question-circle me-2\"/> Λίστα με\n"
"                                                Διλήμματα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid ""
"<i class=\"fa fa-save\"/>\n"
"                            Project list"
msgstr ""
"<i class=\"fa fa-save\"/>\n"
"                            Λίστα έργων"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"<i class=\"fa fa-save\"/> \n"
"                            Project list"
msgstr ""
"<i class=\"fa fa-save\"/> \n"
"                            Λίστα έργων"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-save\"/> Project list"
msgstr "<i class=\"fa fa-save\"/> Λίστα έργων"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<option value=\"\">Select a country</option>"
msgstr "<option value=\"\">Επιλέξτε μια χώρα</option>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid ""
"<small class=\"text-muted\">Please upload an Excel file with the following "
"columns: Dilemma ID, Title, Description, Questions, ENV, ECO, SOC, "
"TECH</small>"
msgstr ""
"<small class=\"text-muted\">Ανεβάστε ένα αρχείο Excel με τις ακόλουθες "
"στήλες: Αναγνωριστικό διλήμματος, Τίτλος, Περιγραφή, Ερωτήσεις, ENV, ECO, "
"SOC, TECH</small>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "<span class=\"badge badge-pill badge-secondary\">NA</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">BBC</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">Nature</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The Guardian</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The New York Times</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The Washington Post</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">Twitter</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">YouTube</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-book\"/> Upload PDFs of relevant document (Academic Paper/Journal/Report) </span>"
msgstr ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-book\"/> Μεταφόρτωση αρχείων PDF σχετικού εγγράφου (Ακαδημαϊκή εργασία/Περιοδικό/Αναφορά) </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-newspaper-o\"/> Media </span>"
msgstr ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-newspaper-o\"/> Μέσα </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "<span class=\"visually-hidden\">Loading...</span>"
msgstr "<span class=\"visually-hidden\">Φόρτωση...</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span id=\"proceed-button-text\">Proceed</span>\n"
"                                            <span id=\"proceed-button-loader\" class=\"spinner-border spinner-border-sm text-light\" style=\"display: none;\" role=\"status\">\n"
"                                                <span class=\"visually-hidden\">Loading...</span>\n"
"                                            </span>"
msgstr ""
"<span id=\"proceed-button-text\">Συνέχεια</span>\n"
"                                            <span id=\"proceed-button-loader\" class=\"spinner-border spinner-border-sm text-light\" style=\"display: none;\" role=\"status\">\n"
"                                                <span class=\"visually-hidden\">Φόρτωση...</span>\n"
"                                            </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>Delete</span>"
msgstr "<span>Διαγραφή</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>Edit</span>"
msgstr "<span>Επεξεργασία</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span>Save Changes</span>\n"
"                                            <span id=\"edit-project-spinner\" class=\"spinner-border spinner-border-sm\" role=\"status\" style=\"display: none;\">\n"
"                                                <span class=\"visually-hidden\">Loading...</span>\n"
"                                            </span>"
msgstr ""
"<span>Αποθήκευση αλλαγών</span>\n"
"                                            <span id=\"edit-project-spinner\" class=\"spinner-border spinner-border-sm\" role=\"status\" style=\"display: none;\">\n"
"                                                <span class=\"visually-hidden\">Φόρτωση...</span>\n"
"                                            </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>View results</span>"
msgstr "<span>Προβολή αποτελεσμάτων</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<strong> Note: The sum of all the scores of criteria\n"
"                                                    must equal 1.</strong>"
msgstr ""
"<strong> Σημείωση: Το άθροισμα όλων των βαθμολογιών των κριτηρίων\n"
"                                                    πρέπει να ισούται με 1.</strong>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<strong>0.9</strong> represents the <strong>maximum\n"
"                                                impact</strong> that should be Prioritised, while <strong>\n"
"                                                0.1</strong> indicates <strong>minimum\n"
"                                                    impact</strong> according to the criteria."
msgstr ""
"Το <strong>0,9</strong> αντιπροσωπεύει το <strong>μέγιστο\n"
"                                                αντίκτυπο</strong> που πρέπει να δοθεί προτεραιότητα, ενώ <strong>\n"
"                                                0,1</strong> υποδηλώνει <strong>ελάχιστο\n"
"                                                    αντίκτυπο</strong>σύμφωνα με τα κριτήρια."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "AGENDA"
msgstr "ΗΜΕΡΗΣΙΑ ΔΙΑΤΑΞΗ"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Action"
msgstr "Δράση"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Add Keywords"
msgstr "Προσθήκη λέξεων-κλειδιών"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__agenda
msgid "Agenda"
msgstr "Ημερήσια διάταξη"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Agenda Dashboard"
msgstr "Πίνακας ελέγχου ατζέντας"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_root
msgid "Agenda Generation"
msgstr "Γενιά Ατζέντας"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_project_action
msgid "Agenda Project"
msgstr "Έργο Ατζέντας"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_result_action
msgid "Agenda Result"
msgstr "Αποτέλεσμα Ατζέντας"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_agenda_source_action
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_agenda_source
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_agenda_source
msgid "Agenda Source"
msgstr "Πηγή Ατζέντας"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Agenda Source Saved Successfully."
msgstr "Η πηγή ατζέντας αποθηκεύτηκε με επιτυχία."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"All\n"
"                                                    country"
msgstr ""
"Ολοι\n"
"                                                    χώρα"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.menu_cep_ag_dilemma
msgid "All Dilemmas"
msgstr "Όλα τα διλήμματα"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "An error occurred while submitting data."
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "An error occurred. Please try again."
msgstr "Παρουσιάστηκε σφάλμα. Δοκιμάστε ξανά."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__analyzer_status
msgid "Analyzer Status"
msgstr "Κατάσταση αναλυτή"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Are you sure you want to delete this Project <span/>?"
msgstr "Είστε βέβαιοι ότι θέλετε να διαγράψετε αυτό το έργο <span/>;"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__attachment
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Attachment"
msgstr "Προσάρτημα"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__attachment_ids
msgid "Attachments"
msgstr "Συνημμένα"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_dilemma
msgid "CEP Agenda Generation Dilemma"
msgstr "Δίλημμα CEP Agenda Generation"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_search
msgid "CEP Agenda Project"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_result_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_result_search
msgid "CEP Agenda Result"
msgstr "Αποτέλεσμα Ατζέντας CEP"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_search
msgid "CEP Agenda Source"
msgstr "Πηγή Ατζέντας CEP"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "CLIMAS Logo"
msgstr "Λογότυπο CLIMAS"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Cancel"
msgstr "Ματαίωση"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__category
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Category"
msgstr "Κατηγορία"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__dilemma_ids
msgid "Child Dilemmas"
msgstr "Παιδικά Διλήμματα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Choose a time range (e.g., 2020–2024) to extract agenda\n"
"                                            topics based on\n"
"                                            specific years."
msgstr ""
"Επιλέξτε ένα χρονικό εύρος (π.χ. 2020–2024) για να εξαγάγετε την ατζέντα\n"
"                                            θέματα που βασίζονται σε\n"
"                                            συγκεκριμένα έτη."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Click \"Proceed\" to start the analysis."
msgstr "Κάντε κλικ στο «Συνέχεια» για να ξεκινήσει η ανάλυση."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Click \"Upload a file/folder\" to add PDFs containing\n"
"                                            research papers or\n"
"                                            reports."
msgstr ""
"Κάντε κλικ στο \"Μεταφόρτωση αρχείου/φάκελου\" για να προσθέσετε αρχεία PDF που περιέχουν\n"
"                                            ερευνητικές εργασίες ή\n"
"                                            εκθέσεις."

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__climate_assembly
msgid "Climate Assembly"
msgstr "Συνέλευση για το κλίμα"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__climate_dialogue
msgid "Climate Dialogue"
msgstr "Διάλογος για το κλίμα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Close"
msgstr "Κοντά"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__column_a
msgid "Column A"
msgstr "Στήλη Α"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__column_b
msgid "Column B"
msgstr "Στήλη Β"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__completed
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__completed
msgid "Completed"
msgstr "Ολοκληρώθηκε το"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Countries"
msgstr "χωρών"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__target_country
msgid "Country"
msgstr "Χώρα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"Create\n"
"                                    Agenda"
msgstr ""
"Δημιουργώ\n"
"                                    Ημερήσια διάταξη"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Create Agenda for"
msgstr "Δημιουργία ατζέντας για"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Create Date"
msgstr "Δημιουργία ημερομηνίας"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_agenda_source_action
msgid "Create a new Agenda Source"
msgstr "Δημιουργήστε μια νέα πηγή ατζέντας"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_project_action
msgid "Create a new Project"
msgstr "Δημιουργήστε ένα νέο έργο"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_result_action
msgid "Create a new Result"
msgstr "Δημιουργήστε ένα νέο αποτέλεσμα"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.action_cep_ag_dilemma
msgid "Create your first dilemma"
msgstr "Δημιουργήστε το πρώτο σας δίλημμα"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__criteria_weights
msgid "Criteria Weights"
msgstr "Βάρη κριτηρίων"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Criterion"
msgstr "Κριτήριο"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__description
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__description
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__description
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Description"
msgstr "Περιγραφή"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__dilemma
msgid "Dilemma"
msgstr "Δίλημμα"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__dilemma_serial_id
msgid "Dilemma Serial"
msgstr "Σειρά διλήμματος"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.action_cep_ag_dilemma
msgid "Dilemmas"
msgstr "Διλήμματα"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__display_name
msgid "Display Name"
msgstr "Εμφανιζόμενο όνομα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"Do you want to prioritise the list of\n"
"                                            dilemmas under\n"
"                                            criteria?"
msgstr ""
"Θέλετε να δώσετε προτεραιότητα στη λίστα των\n"
"                                            διλήμματα κάτω από\n"
"                                            κριτήρια;"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__draft
msgid "Draft"
msgstr "Προσχέδιο"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__eco
msgid "ECO Score"
msgstr "Βαθμολογία ECO"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__environment
msgid "ENV Score"
msgstr "Βαθμολογία ENV"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Economic Factors"
msgstr "Οικονομικοί Παράγοντες"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Edit Project"
msgstr "Επεξεργασία έργου"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__end_year
msgid "End Year"
msgstr "Τέλος Έτους"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Enter weight"
msgstr "Εισαγάγετε βάρος"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Environmental Impact"
msgstr "Περιβαλλοντικές Επιπτώσεις"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__error_message
msgid "Error Message"
msgstr "Μήνυμα σφάλματος"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Error Reason"
msgstr "Λόγος σφάλματος"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__failed
msgid "Failed"
msgstr "Αποτυχημένος"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__favorite
msgid "Favorite"
msgstr "Ευνοούμενος"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
msgid "Favorites"
msgstr "Αγαπημένα"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__frequency_analysis
msgid "Frequency Analysis"
msgstr "Ανάλυση Συχνότητας"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Generating frequency analysis..."
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Get Frequency"
msgstr "Λάβετε Συχνότητα"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__id
msgid "ID"
msgstr "ταυτότητα"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__idle
msgid "Idle"
msgstr "Αεργος"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__in_progress
msgid "In Progress"
msgstr "Σε εξέλιξη"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Instructions"
msgstr "Οδηγίες"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__just_transition_dialogue
msgid "Just Transition Dialogue"
msgstr "Διάλογος Just Transition"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__keyword
msgid "Keyword"
msgstr "Λέξη-κλειδί"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Keyword Frequency Over Years"
msgstr "Συχνότητα λέξεων-κλειδιών σε χρόνια"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__keywords
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Keywords"
msgstr "Λέξεις-κλειδιά"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result____last_update
msgid "Last Modified on"
msgstr "Τελευταία Τροποποίηση στις"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__write_uid
msgid "Last Updated by"
msgstr "Τελευταία ενημέρωση από"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__write_date
msgid "Last Updated on"
msgstr "Τελευταία ενημέρωση στις"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Last modified:"
msgstr "Τελευταία τροποποίηση:"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "List of Agendas"
msgstr "Κατάλογος Ημερήσιας Διάταξης"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "List of Dilemmas"
msgstr "Κατάλογος Διλημμάτων"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Loading N-gram visualization..."
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Loading agendas..."
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Loading dilemmas..."
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Mark as Favorite"
msgstr "Επισήμανση ως Αγαπημένο"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__metadata
msgid "Metadata"
msgstr "Μεταδεδομένα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Most common words frequency"
msgstr "Συχνότητα των πιο συνηθισμένων λέξεων"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__n_gram
msgid "N Gram"
msgstr "Ν γραμμ"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "N-gram Visualization"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "No"
msgstr "Οχι"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Note: Twitter does not support filtering by year."
msgstr "Σημείωση: Το Twitter δεν υποστηρίζει φιλτράρισμα ανά έτος."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Output"
msgstr "Παραγωγή"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__owner_id
msgid "Owner"
msgstr "Ιδιοκτήτης"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__pdf
#, python-format
msgid "PDF"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Papers"
msgstr "Χαρτιά"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__parent_id
msgid "Parent Agenda"
msgstr "Ατζέντα Γονέων"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Print Media"
msgstr "Έντυπα Μέσα"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__printed_media
msgid "Printed Media"
msgstr "Έντυπα Μέσα"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Printed media"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Prioritise"
msgstr "Δώστε προτεραιότητα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Prioritise according to your criteria."
msgstr "Δώστε προτεραιότητα σύμφωνα με τα κριτήριά σας."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__priority
msgid "Priority"
msgstr "Προτεραιότητα"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_project
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__project_id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__project_id
msgid "Project"
msgstr "Σχέδιο"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Project Delete Confirmation"
msgstr "Επιβεβαίωση διαγραφής έργου"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Project Description"
msgstr "Περιγραφή Έργου"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Project Title"
msgstr "Τίτλος Έργου"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Project description"
msgstr "Περιγραφή έργου"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Project:"
msgstr "Σχέδιο:"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_project
msgid "Projects"
msgstr "Έργα"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__questions
msgid "Questions"
msgstr "Ερωτήσεις"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Questions:"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Recent Projects"
msgstr "Πρόσφατα Έργα"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__related_dilemma_id
msgid "Related Dilemma"
msgstr "Σχετικό Δίλημμα"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_result
msgid "Result"
msgstr "Αποτέλεσμα"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__result_ids
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_result
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Results"
msgstr "Αποτελέσματα"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__running
msgid "Running"
msgstr "Τρέξιμο"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "SL"
msgstr ""

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__soc
msgid "SOC Score"
msgstr "Σκορ SOC"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Saved Projects"
msgstr "Αποθηκευμένα έργα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select from predefined print media sources (BBC, The New\n"
"                                            York Times, etc.)."
msgstr ""
"Επιλέξτε από προκαθορισμένες πηγές μέσων εκτύπωσης (BBC, The New\n"
"                                            York Times, κ.λπ.)."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select specific countries to focus on country-specific\n"
"                                            agenda topics."
msgstr ""
"Επιλέξτε συγκεκριμένες χώρες για να εστιάσετε σε συγκεκριμένες χώρες\n"
"                                            θέματα ατζέντας."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select the social media platforms (YouTube, Twitter,\n"
"                                            Facebook, etc.) where\n"
"                                            you want to search for related data."
msgstr ""
"Επιλέξτε τις πλατφόρμες κοινωνικών μέσων (YouTube, Twitter,\n"
"                                            Facebook κ.λπ.) όπου\n"
"                                            θέλετε να αναζητήσετε σχετικά δεδομένα."

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Show Less"
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Show More"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Social Equity"
msgstr "Κοινωνική Ισότητα"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__social_media
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Social Media"
msgstr "Μέσα κοινωνικής δικτύωσης"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Social media"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Source"
msgstr "Πηγή"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__agenda_source_ids
msgid "Sources"
msgstr "Πηγές"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__start_year
msgid "Start Year"
msgstr "Έτος έναρξης"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__status
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Status"
msgstr "Κατάσταση"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Submit"
msgstr "Υποτάσσομαι"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__tech
msgid "TECH Score"
msgstr "Βαθμολογία TECH"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Technology & Innovation"
msgstr "Τεχνολογία & Καινοτομία"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "The process may take 3 to 5 minutes to complete."
msgstr "Η διαδικασία μπορεί να διαρκέσει 3 έως 5 λεπτά για να ολοκληρωθεί."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"The system will process your inputs and extract key\n"
"                                            topics and agendas."
msgstr ""
"Το σύστημα θα επεξεργαστεί τις εισόδους σας και θα εξαγάγει το κλειδί\n"
"                                            θέματα και ατζέντα."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__title
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__title
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__title
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Title"
msgstr "Τίτλος"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "To"
msgstr "Να"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__type
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__type
msgid "Type"
msgstr "Τύπος"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Type in the keywords relevant to your agenda (e.g.,\n"
"                                            \"climate change,\"\n"
"                                            \"carbon emissions\")."
msgstr ""
"Πληκτρολογήστε τις λέξεις-κλειδιά που σχετίζονται με την ατζέντα σας (π.χ.\n"
"                                            \"κλιματική αλλαγή\",\n"
"                                            \"εκπομπές άνθρακα\")."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__urls
msgid "URLs"
msgstr "διευθύνσεις URL"

#. module: cep_agenda_generation
#: model:ir.model.fields,help:cep_agenda_generation.field_cep_ag_result__dilemma_serial_id
msgid ""
"Unique identifier to link with cep.ag.dilemma model's dilemma_serial_id "
"field"
msgstr ""
"Μοναδικό αναγνωριστικό για σύνδεση με το πεδίο dilemma_serial_id του "
"μοντέλου cep.ag.dilemma"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "Upload Dilemmas"
msgstr "Ανεβάστε Διλήμματα"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "Upload Excel File"
msgstr "Ανεβάστε αρχείο Excel"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Upload a files"
msgstr "Ανεβάστε ένα αρχείο"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Uploaded\n"
"                                                    Papers"
msgstr ""
"Μεταφορτώθηκε\n"
"                                                    Χαρτιά"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__year
msgid "Year"
msgstr "Ετος"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Years"
msgstr "Χρόνια"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Yes"
msgstr "Ναί"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"You will receive an output containing agenda topics\n"
"                                            categorized by country,\n"
"                                            year, and frequency trends."
msgstr ""
"Θα λάβετε ένα αποτέλεσμα που θα περιέχει θέματα ατζέντας\n"
"                                            κατηγοριοποιούνται ανά χώρα,\n"
"                                            έτος και τάσεις συχνότητας."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "cloud upload"
msgstr "μεταφόρτωση στο σύννεφο"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__dilemma_serial_id
msgid "dilemma_serial_id"
msgstr ""
