# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_agenda_generation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20250520\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 09:00+0000\n"
"PO-Revision-Date: 2025-05-23 09:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-bar-chart me-2\"/> Keyword Frequency Over Years"
msgstr "<i class=\"fa fa-bar-chart me-2\"/> Raktinių žodžių da<PERSON> per metus"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Climate Assembly"
msgstr "<i class=\"fa fa-circle-o\"/> Klimato asamblėja"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Climate Dialogue"
msgstr "<i class=\"fa fa-circle-o\"/> Klimato dialogas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Just Transition Dialogue"
msgstr "<i class=\"fa fa-circle-o\"/> Tiesioginio perėjimo dialogas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"<i class=\"fa fa-heart\"/>\n"
"                            Favorites"
msgstr ""
"<i class=\"fa fa-heart\"/>\n"
"                            Mėgstamiausi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-heart\"/> Favorites"
msgstr "<i class=\"fa fa-heart\"/> Mėgstamiausi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-home\"/> Home"
msgstr "<i class=\"fa fa-home\"/> Pagrindinis puslapis"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-line-chart me-2\"/> Most common words frequency"
msgstr ""
"<i class=\"fa fa-line-chart me-2\"/> Dažniausiai pasitaikančių žodžių dažnis"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-list-alt me-2\"/> List of Agendas"
msgstr "<i class=\"fa fa-list-alt me-2\"/> Darbotvarkių sąrašas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid ""
"<i class=\"fa fa-plus\"/> Create Agenda\n"
"                                <i class=\"fa fa-chevron-down submenu-icon\"/>"
msgstr ""
"<i class=\"fa fa-plus\"/> Sukurkite darbotvarkę\n"
"                                <i class=\"fa fa-chevron-down submenu-icon\"/>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<i class=\"fa fa-plus\"/> Create Agenda <i class=\"fa fa-chevron-down "
"submenu-icon\"/>"
msgstr ""
"<i class=\"fa fa-plus\"/> Sukurti darbotvarkę <i class=\"fa fa-chevron-down "
"submenu-icon\"/>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<i class=\"fa fa-question-circle me-2\"/> List of\n"
"                                                Dilemmas"
msgstr ""
"<i class=\"fa fa-question-circle me-2\"/> Sąrašas\n"
"                                                Dilemos"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid ""
"<i class=\"fa fa-save\"/>\n"
"                            Project list"
msgstr ""
"<i class=\"fa fa-save\"/>\n"
"                            Projektų sąrašas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"<i class=\"fa fa-save\"/> \n"
"                            Project list"
msgstr ""
"<i class=\"fa fa-save\"/> \n"
"                            Projektų sąrašas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-save\"/> Project list"
msgstr "<i class=\"fa fa-save\"/> Projektų sąrašas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<option value=\"\">Select a country</option>"
msgstr "<option value=\"\">Pasirinkite šalį</option>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid ""
"<small class=\"text-muted\">Please upload an Excel file with the following "
"columns: Dilemma ID, Title, Description, Questions, ENV, ECO, SOC, "
"TECH</small>"
msgstr ""
"<small class=\"text-muted\">Įkelkite Excel failą su šiais stulpeliais: "
"Dilemos ID, pavadinimas, aprašymas, klausimai, ENV, ECO, SOC, TECH</small>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "<span class=\"badge badge-pill badge-secondary\">NA</span>"
msgstr "<span class=\"badge badge-pill badge-secondary\">Ne</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">BBC</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">Nature</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The Guardian</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The New York Times</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The Washington Post</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">Twitter</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">YouTube</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-book\"/> Upload PDFs of relevant document (Academic Paper/Journal/Report) </span>"
msgstr ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-book\"/> Įkelkite atitinkamo dokumento PDF failus (akademinis darbas / žurnalas / ataskaita) </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-newspaper-o\"/> Media </span>"
msgstr ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-newspaper-o\"/> Žiniasklaida </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "<span class=\"visually-hidden\">Loading...</span>"
msgstr "<span class=\"visually-hidden\">Įkeliama...</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span id=\"proceed-button-text\">Proceed</span>\n"
"                                            <span id=\"proceed-button-loader\" class=\"spinner-border spinner-border-sm text-light\" style=\"display: none;\" role=\"status\">\n"
"                                                <span class=\"visually-hidden\">Loading...</span>\n"
"                                            </span>"
msgstr ""
"<span id=\"proceed-button-text\">Tęsti</span>\n"
"                                            <span id=\"proceed-button-loader\" class=\"spinner-border spinner-border-sm text-light\" style=\"display: none;\" role=\"status\">\n"
"                                                <span class=\"visually-hidden\">Įkeliama...</span>\n"
"                                            </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>Delete</span>"
msgstr "<span>Ištrinti</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>Edit</span>"
msgstr "<span>Redaguoti</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span>Save Changes</span>\n"
"                                            <span id=\"edit-project-spinner\" class=\"spinner-border spinner-border-sm\" role=\"status\" style=\"display: none;\">\n"
"                                                <span class=\"visually-hidden\">Loading...</span>\n"
"                                            </span>"
msgstr ""
"<span>Išsaugoti pakeitimus</span>\n"
"                                            <span id=\"edit-project-spinner\" class=\"spinner-border spinner-border-sm\" role=\"status\" style=\"display: none;\">\n"
"                                                <span class=\"visually-hidden\">Įkeliama...</span>\n"
"                                            </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>View results</span>"
msgstr "<span>Peržiūrėti rezultatus</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<strong> Note: The sum of all the scores of criteria\n"
"                                                    must equal 1.</strong>"
msgstr ""
"<strong> Pastaba: visų kriterijų balų suma\n"
"                                                    turi būti lygus 1.</strong>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<strong>0.9</strong> represents the <strong>maximum\n"
"                                                impact</strong> that should be Prioritised, while <strong>\n"
"                                                0.1</strong> indicates <strong>minimum\n"
"                                                    impact</strong> according to the criteria."
msgstr ""
"<strong>0,9</strong> reiškia <strong>maksimumą\n"
"                                                poveikis</strong>, kuriam turėtų būti suteikta pirmenybė, o <strong>\n"
"                                                0,1</strong> reiškia <strong>minimalų\n"
"                                                    poveikio</strong>pagal kriterijus."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "AGENDA"
msgstr "DARBOTVARKĖ"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Action"
msgstr "Veiksmas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Add Keywords"
msgstr "Pridėti raktinių žodžių"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__agenda
msgid "Agenda"
msgstr "Dienotvarkė"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Agenda Dashboard"
msgstr "Darbotvarkės prietaisų skydelis"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_root
msgid "Agenda Generation"
msgstr "Darbotvarkės karta"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_project_action
msgid "Agenda Project"
msgstr "Darbotvarkės projektas"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_result_action
msgid "Agenda Result"
msgstr "Darbotvarkės rezultatas"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_agenda_source_action
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_agenda_source
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_agenda_source
msgid "Agenda Source"
msgstr "Dienotvarkės šaltinis"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Agenda Source Saved Successfully."
msgstr "Darbotvarkės šaltinis sėkmingai išsaugotas."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"All\n"
"                                                    country"
msgstr ""
"Visi\n"
"                                                    šalis"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.menu_cep_ag_dilemma
msgid "All Dilemmas"
msgstr "Visos dilemos"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "An error occurred while submitting data."
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "An error occurred. Please try again."
msgstr "Įvyko klaida. Bandykite dar kartą."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__analyzer_status
msgid "Analyzer Status"
msgstr "Analizatoriaus būsena"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Are you sure you want to delete this Project <span/>?"
msgstr "Ar tikrai norite ištrinti šį projektą <span/>?"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__attachment
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Attachment"
msgstr "Priedas"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__attachment_ids
msgid "Attachments"
msgstr "Priedai"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_dilemma
msgid "CEP Agenda Generation Dilemma"
msgstr "CEP darbotvarkės generavimo dilema"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_search
msgid "CEP Agenda Project"
msgstr "CEP darbotvarkės projektas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_result_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_result_search
msgid "CEP Agenda Result"
msgstr "CEP darbotvarkės rezultatas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_search
msgid "CEP Agenda Source"
msgstr "CEP darbotvarkės šaltinis"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "CLIMAS Logo"
msgstr "CLIMAS logotipas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Cancel"
msgstr "Atšaukti"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__category
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Category"
msgstr "Kategorija"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__dilemma_ids
msgid "Child Dilemmas"
msgstr "Vaiko dilemos"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Choose a time range (e.g., 2020–2024) to extract agenda\n"
"                                            topics based on\n"
"                                            specific years."
msgstr ""
"Pasirinkite laiko intervalą (pvz., 2020–2024 m.), kad ištrauktumėte darbotvarkę\n"
"                                            temos remiantis\n"
"                                            konkrečių metų."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Click \"Proceed\" to start the analysis."
msgstr "Spustelėkite „Tęsti“, kad pradėtumėte analizę."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Click \"Upload a file/folder\" to add PDFs containing\n"
"                                            research papers or\n"
"                                            reports."
msgstr ""
"Spustelėkite „Įkelti failą / aplanką“, kad pridėtumėte PDF failus\n"
"                                            mokslinių darbų arba\n"
"                                            pranešimus."

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__climate_assembly
msgid "Climate Assembly"
msgstr "Klimato asamblėja"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__climate_dialogue
msgid "Climate Dialogue"
msgstr "Klimato dialogas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Close"
msgstr "Uždaryti"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__column_a
msgid "Column A"
msgstr "A stulpelis"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__column_b
msgid "Column B"
msgstr "B stulpelis"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__completed
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__completed
msgid "Completed"
msgstr "Užbaigta"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Countries"
msgstr "Šalys"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__target_country
msgid "Country"
msgstr "Šalis"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"Create\n"
"                                    Agenda"
msgstr ""
"Sukurti\n"
"                                    Dienotvarkė"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Create Agenda for"
msgstr "Sukurti darbotvarkę"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Create Date"
msgstr "Sukurti datą"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_agenda_source_action
msgid "Create a new Agenda Source"
msgstr "Sukurkite naują darbotvarkės šaltinį"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_project_action
msgid "Create a new Project"
msgstr "Sukurti naują projektą"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_result_action
msgid "Create a new Result"
msgstr "Sukurti naują rezultatą"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.action_cep_ag_dilemma
msgid "Create your first dilemma"
msgstr "Sukurkite savo pirmąją dilemą"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__criteria_weights
msgid "Criteria Weights"
msgstr "Kriteriniai svoriai"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Criterion"
msgstr "Kriterijus"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__description
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__description
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__description
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Description"
msgstr "Aprašymas"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__dilemma
msgid "Dilemma"
msgstr "Dilema"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__dilemma_serial_id
msgid "Dilemma Serial"
msgstr "Dilemų serija"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.action_cep_ag_dilemma
msgid "Dilemmas"
msgstr "Dilemos"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"Do you want to prioritise the list of\n"
"                                            dilemmas under\n"
"                                            criteria?"
msgstr ""
"Ar norite teikti pirmenybę sąrašui\n"
"                                            dilemos pagal\n"
"                                            kriterijai?"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__draft
msgid "Draft"
msgstr "Juodraštis"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__eco
msgid "ECO Score"
msgstr "EKO balas"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__environment
msgid "ENV Score"
msgstr "ENV balas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Economic Factors"
msgstr "Ekonominiai veiksniai"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Edit Project"
msgstr "Redaguoti projektą"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__end_year
msgid "End Year"
msgstr "Metų pabaiga"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Enter weight"
msgstr "Įveskite svorį"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Environmental Impact"
msgstr "Poveikis aplinkai"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__error_message
msgid "Error Message"
msgstr "Klaidos pranešimas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Error Reason"
msgstr "Klaidos priežastis"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__failed
msgid "Failed"
msgstr "Nepavyko"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__favorite
msgid "Favorite"
msgstr "Mėgstamiausias"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
msgid "Favorites"
msgstr "Mėgstamiausi"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__frequency_analysis
msgid "Frequency Analysis"
msgstr "Dažnio analizė"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Generating frequency analysis..."
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Get Frequency"
msgstr "Gaukite dažnį"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__id
msgid "ID"
msgstr ""

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__idle
msgid "Idle"
msgstr "Tuščia eiga"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__in_progress
msgid "In Progress"
msgstr "Vykdoma"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Instructions"
msgstr "Instrukcijos"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__just_transition_dialogue
msgid "Just Transition Dialogue"
msgstr "Tiesiog pereinamasis dialogas"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__keyword
msgid "Keyword"
msgstr "raktinis žodis"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Keyword Frequency Over Years"
msgstr "Raktinių žodžių dažnis per metus"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__keywords
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Keywords"
msgstr "Raktažodžiai"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą modifikuota"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Last modified:"
msgstr "Paskutinį kartą pakeista:"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "List of Agendas"
msgstr "Darbotvarkių sąrašas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "List of Dilemmas"
msgstr "Dilemų sąrašas"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Loading N-gram visualization..."
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Loading agendas..."
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Loading dilemmas..."
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Mark as Favorite"
msgstr "Pažymėti kaip mėgstamiausią"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__metadata
msgid "Metadata"
msgstr "Metaduomenys"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Most common words frequency"
msgstr "Dažniausiai pasitaikančių žodžių dažnis"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__n_gram
msgid "N Gram"
msgstr "N gramas"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "N-gram Visualization"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "No"
msgstr "Nr"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Note: Twitter does not support filtering by year."
msgstr "Pastaba: „Twitter“ nepalaiko filtravimo pagal metus."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Output"
msgstr "Išvestis"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__owner_id
msgid "Owner"
msgstr "Savininkas"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__pdf
#, python-format
msgid "PDF"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Papers"
msgstr "Popieriai"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__parent_id
msgid "Parent Agenda"
msgstr "Tėvų dienotvarkė"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Print Media"
msgstr "Spausdinimo laikmena"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__printed_media
msgid "Printed Media"
msgstr "Spausdintos laikmenos"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Printed media"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Prioritise"
msgstr "Suteikite pirmenybę"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Prioritise according to your criteria."
msgstr "Suteikite pirmenybę pagal savo kriterijus."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__priority
msgid "Priority"
msgstr "Pirmenybė"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_project
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__project_id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__project_id
msgid "Project"
msgstr "Projektas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Project Delete Confirmation"
msgstr "Projekto ištrynimo patvirtinimas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Project Description"
msgstr "Projekto aprašymas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Project Title"
msgstr "Projekto pavadinimas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Project description"
msgstr "Projekto aprašymas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Project:"
msgstr "Projektas:"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_project
msgid "Projects"
msgstr "Projektai"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__questions
msgid "Questions"
msgstr "Klausimai"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Questions:"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Recent Projects"
msgstr "Naujausi projektai"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__related_dilemma_id
msgid "Related Dilemma"
msgstr "Susijusi dilema"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_result
msgid "Result"
msgstr "Rezultatas"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__result_ids
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_result
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Results"
msgstr "Rezultatai"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__running
msgid "Running"
msgstr "Bėgimas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "SL"
msgstr ""

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__soc
msgid "SOC Score"
msgstr "SOC rezultatas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Saved Projects"
msgstr "Išsaugoti projektai"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select from predefined print media sources (BBC, The New\n"
"                                            York Times, etc.)."
msgstr ""
"Pasirinkite iš iš anksto nustatytų spausdinimo laikmenų šaltinių (BBC, The New\n"
"                                            York Times ir kt.)."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select specific countries to focus on country-specific\n"
"                                            agenda topics."
msgstr ""
"Pasirinkite konkrečias šalis, kad sutelktumėte dėmesį į konkrečias šalis\n"
"                                            dienotvarkės temos."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select the social media platforms (YouTube, Twitter,\n"
"                                            Facebook, etc.) where\n"
"                                            you want to search for related data."
msgstr ""
"Pasirinkite socialinės žiniasklaidos platformas (YouTube, Twitter,\n"
"                                            Facebook ir kt.) kur\n"
"                                            norite ieškoti susijusių duomenų."

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Show Less"
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Show More"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Social Equity"
msgstr "Socialinis teisingumas"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__social_media
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Social Media"
msgstr "Socialinė žiniasklaida"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Social media"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Source"
msgstr "Šaltinis"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__agenda_source_ids
msgid "Sources"
msgstr "Šaltiniai"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__start_year
msgid "Start Year"
msgstr "Pradžios metai"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__status
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Status"
msgstr "Būsena"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Submit"
msgstr "Pateikti"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__tech
msgid "TECH Score"
msgstr "TECH balas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Technology & Innovation"
msgstr "Technologijos ir inovacijos"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "The process may take 3 to 5 minutes to complete."
msgstr "Procesas gali užtrukti nuo 3 iki 5 minučių."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"The system will process your inputs and extract key\n"
"                                            topics and agendas."
msgstr ""
"Sistema apdoros jūsų įvestis ir ištrauks raktą\n"
"                                            temas ir darbotvarkes."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__title
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__title
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__title
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Title"
msgstr "Pavadinimas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "To"
msgstr "Į"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__type
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__type
msgid "Type"
msgstr "Tipas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Type in the keywords relevant to your agenda (e.g.,\n"
"                                            \"climate change,\"\n"
"                                            \"carbon emissions\")."
msgstr ""
"Įveskite raktinius žodžius, susijusius su jūsų darbotvarke (pvz.,\n"
"                                            „klimato kaita“,\n"
"                                            „anglies išmetimas“)."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__urls
msgid "URLs"
msgstr "URL"

#. module: cep_agenda_generation
#: model:ir.model.fields,help:cep_agenda_generation.field_cep_ag_result__dilemma_serial_id
msgid ""
"Unique identifier to link with cep.ag.dilemma model's dilemma_serial_id "
"field"
msgstr ""
"Unikalus identifikatorius, skirtas susieti su cep.ag.dilemma modelio lauku "
"dilemma_serial_id"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "Upload Dilemmas"
msgstr "Įkelti Dilemas"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "Upload Excel File"
msgstr "Įkelti Excel failą"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Upload a files"
msgstr "Įkelti failus"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Uploaded\n"
"                                                    Papers"
msgstr ""
"Įkelta\n"
"                                                    Popieriai"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__year
msgid "Year"
msgstr "Metai"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Years"
msgstr "Metai"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Yes"
msgstr "Taip"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"You will receive an output containing agenda topics\n"
"                                            categorized by country,\n"
"                                            year, and frequency trends."
msgstr ""
"Jūs gausite išvestį su darbotvarkės temomis\n"
"                                            suskirstyta pagal šalį,\n"
"                                            metų ir dažnumo tendencijas."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "cloud upload"
msgstr "debesies įkėlimas"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__dilemma_serial_id
msgid "dilemma_serial_id"
msgstr ""
