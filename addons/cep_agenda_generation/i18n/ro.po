# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_agenda_generation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20250520\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 08:56+0000\n"
"PO-Revision-Date: 2025-05-23 08:56+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-bar-chart me-2\"/> Keyword Frequency Over Years"
msgstr ""
"<i class=\"fa fa-bar-chart me-2\"/> Frecvența cuvintelor cheie de-a lungul "
"anilor"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Climate Assembly"
msgstr "<i class=\"fa fa-circle-o\"/> Adunarea Climei"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Climate Dialogue"
msgstr "<i class=\"fa fa-circle-o\"/> Dialogul climatic"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Just Transition Dialogue"
msgstr "<i class=\"fa fa-circle-o\"/> Dialog de tranziție doar"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"<i class=\"fa fa-heart\"/>\n"
"                            Favorites"
msgstr ""
"<i class=\"fa fa-heart\"/>\n"
"                            Favorite"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-heart\"/> Favorites"
msgstr "<i class=\"fa fa-heart\"/> Favorite"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-home\"/> Home"
msgstr "<i class=\"fa fa-home\"/> Acasă"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-line-chart me-2\"/> Most common words frequency"
msgstr ""
"<i class=\"fa fa-line-chart me-2\"/> Frecvența celor mai frecvente cuvinte"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-list-alt me-2\"/> List of Agendas"
msgstr "<i class=\"fa fa-list-alt me-2\"/> Lista agendelor"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid ""
"<i class=\"fa fa-plus\"/> Create Agenda\n"
"                                <i class=\"fa fa-chevron-down submenu-icon\"/>"
msgstr ""
"<i class=\"fa fa-plus\"/> Creați agendă\n"
"                                <i class=\"fa fa-chevron-down submeniu-icon\"/>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<i class=\"fa fa-plus\"/> Create Agenda <i class=\"fa fa-chevron-down "
"submenu-icon\"/>"
msgstr ""
"<i class=\"fa fa-plus\"/> Creați agendă <i class=\"fa fa-chevron-down "
"submenu-icon\"/>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<i class=\"fa fa-question-circle me-2\"/> List of\n"
"                                                Dilemmas"
msgstr ""
"<i class=\"fa fa-question-circle me-2\"/> Lista de\n"
"                                                Dileme"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid ""
"<i class=\"fa fa-save\"/>\n"
"                            Project list"
msgstr ""
"<i class=\"fa fa-save\"/>\n"
"                            Lista proiectelor"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"<i class=\"fa fa-save\"/> \n"
"                            Project list"
msgstr ""
"<i class=\"fa fa-save\"/> \n"
"                            Lista proiectelor"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-save\"/> Project list"
msgstr "<i class=\"fa fa-save\"/> Lista de proiecte"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<option value=\"\">Select a country</option>"
msgstr "<option value=\"\">Selectați o țară</option>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid ""
"<small class=\"text-muted\">Please upload an Excel file with the following "
"columns: Dilemma ID, Title, Description, Questions, ENV, ECO, SOC, "
"TECH</small>"
msgstr ""
"<small class=\"text-muted\">Vă rugăm să încărcați un fișier Excel cu "
"următoarele coloane: ID dilemă, Titlu, Descriere, Întrebări, ENV, ECO, SOC, "
"TECH</small>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "<span class=\"badge badge-pill badge-secondary\">NA</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">BBC</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">Nature</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The Guardian</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The New York Times</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The Washington Post</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">Twitter</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">YouTube</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-book\"/> Upload PDFs of relevant document (Academic Paper/Journal/Report) </span>"
msgstr ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-book\"/> Încărcați PDF-uri ale documentului relevant (Lucrări academice/Jurnal/Raport) </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-newspaper-o\"/> Media </span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "<span class=\"visually-hidden\">Loading...</span>"
msgstr "<span class=\"visually-hidden\">Se încarcă...</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span id=\"proceed-button-text\">Proceed</span>\n"
"                                            <span id=\"proceed-button-loader\" class=\"spinner-border spinner-border-sm text-light\" style=\"display: none;\" role=\"status\">\n"
"                                                <span class=\"visually-hidden\">Loading...</span>\n"
"                                            </span>"
msgstr ""
"<span id=\"proceed-button-text\">Continuați</span>\n"
"                                            <span id=\"proceed-button-loader\" class=\"spinner-border spinner-border-sm text-light\" style=\"display: none;\" rol=\"status\">\n"
"                                                <span class=\"visually-hidden\">Se încarcă...</span>\n"
"                                            </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>Delete</span>"
msgstr "<span>Ștergeți</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>Edit</span>"
msgstr "<span>Editați</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span>Save Changes</span>\n"
"                                            <span id=\"edit-project-spinner\" class=\"spinner-border spinner-border-sm\" role=\"status\" style=\"display: none;\">\n"
"                                                <span class=\"visually-hidden\">Loading...</span>\n"
"                                            </span>"
msgstr ""
"<span>Salvați modificările</span>\n"
"                                            <span id=\"edit-project-spinner\" class=\"spinner-border spinner-border-sm\" role=\"status\" style=\"display: none;\">\n"
"                                                <span class=\"visually-hidden\">Se încarcă...</span>\n"
"                                            </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>View results</span>"
msgstr "<span>Vedeți rezultatele</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<strong> Note: The sum of all the scores of criteria\n"
"                                                    must equal 1.</strong>"
msgstr ""
"<strong> Notă: suma tuturor scorurilor criteriilor\n"
"                                                    trebuie să fie egal cu 1.</strong>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<strong>0.9</strong> represents the <strong>maximum\n"
"                                                impact</strong> that should be Prioritised, while <strong>\n"
"                                                0.1</strong> indicates <strong>minimum\n"
"                                                    impact</strong> according to the criteria."
msgstr ""
"<strong>0,9</strong> reprezintă <strong>maximul\n"
"                                                impact</strong> care ar trebui prioritizat, în timp ce <strong>\n"
"                                                0,1</strong> indică <strong>minimum\n"
"                                                    impact</strong>conform criteriilor."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "AGENDA"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Action"
msgstr "Acţiune"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Add Keywords"
msgstr "Adăugați cuvinte cheie"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__agenda
msgid "Agenda"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Agenda Dashboard"
msgstr "Tabloul de bord agendei"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_root
msgid "Agenda Generation"
msgstr "Generarea agendei"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_project_action
msgid "Agenda Project"
msgstr "Proiect Agenda"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_result_action
msgid "Agenda Result"
msgstr "Rezultatul agendei"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_agenda_source_action
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_agenda_source
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_agenda_source
msgid "Agenda Source"
msgstr "Sursa agendei"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Agenda Source Saved Successfully."
msgstr "Sursa agendei a fost salvată cu succes."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"All\n"
"                                                    country"
msgstr ""
"Toate\n"
"                                                    ţară"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.menu_cep_ag_dilemma
msgid "All Dilemmas"
msgstr "Toate Dilemele"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "An error occurred while submitting data."
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "An error occurred. Please try again."
msgstr "A apărut o eroare. Vă rugăm să încercați din nou."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__analyzer_status
msgid "Analyzer Status"
msgstr "Starea analizorului"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Are you sure you want to delete this Project <span/>?"
msgstr "Sigur doriți să ștergeți acest proiect <span/>?"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__attachment
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Attachment"
msgstr "Atașament"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__attachment_ids
msgid "Attachments"
msgstr "Atasamente"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_dilemma
msgid "CEP Agenda Generation Dilemma"
msgstr "Dilema generarii agendei CEP"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_search
msgid "CEP Agenda Project"
msgstr "Proiectul Agenda CEP"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_result_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_result_search
msgid "CEP Agenda Result"
msgstr "Rezultatul agendei CEP"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_search
msgid "CEP Agenda Source"
msgstr "Sursa agendei CEP"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "CLIMAS Logo"
msgstr "Logo CLIMAS"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Cancel"
msgstr "Anula"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__category
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Category"
msgstr "Categorie"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__dilemma_ids
msgid "Child Dilemmas"
msgstr "Dileme ale copiilor"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Choose a time range (e.g., 2020–2024) to extract agenda\n"
"                                            topics based on\n"
"                                            specific years."
msgstr ""
"Alegeți un interval de timp (de exemplu, 2020–2024) pentru a extrage agenda\n"
"                                            subiecte bazate pe\n"
"                                            anumiți ani."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Click \"Proceed\" to start the analysis."
msgstr "Faceți clic pe „Continuați” pentru a începe analiza."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Click \"Upload a file/folder\" to add PDFs containing\n"
"                                            research papers or\n"
"                                            reports."
msgstr ""
"Faceți clic pe „Încărcați un fișier/dosar” pentru a adăuga fișiere PDF care conțin\n"
"                                            lucrări de cercetare sau\n"
"                                            rapoarte."

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__climate_assembly
msgid "Climate Assembly"
msgstr "Adunarea Climei"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__climate_dialogue
msgid "Climate Dialogue"
msgstr "Dialogul Climatic"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Close"
msgstr "Aproape"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__column_a
msgid "Column A"
msgstr "Coloana A"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__column_b
msgid "Column B"
msgstr "Coloana B"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__completed
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__completed
msgid "Completed"
msgstr "Terminat"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Countries"
msgstr "Țări"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__target_country
msgid "Country"
msgstr "Ţară"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"Create\n"
"                                    Agenda"
msgstr ""
"Crea\n"
"                                    Agenda"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Create Agenda for"
msgstr "Creați agendă pentru"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Create Date"
msgstr "Creare Data"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_agenda_source_action
msgid "Create a new Agenda Source"
msgstr "Creați o nouă sursă de agendă"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_project_action
msgid "Create a new Project"
msgstr "Creați un proiect nou"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_result_action
msgid "Create a new Result"
msgstr "Creați un rezultat nou"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.action_cep_ag_dilemma
msgid "Create your first dilemma"
msgstr "Creează-ți prima dilemă"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__criteria_weights
msgid "Criteria Weights"
msgstr "Criterii Greutăți"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Criterion"
msgstr "Criteriu"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__description
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__description
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__description
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Description"
msgstr "Descriere"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__dilemma
msgid "Dilemma"
msgstr "Dilemă"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__dilemma_serial_id
msgid "Dilemma Serial"
msgstr "Dilema Serial"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.action_cep_ag_dilemma
msgid "Dilemmas"
msgstr "Dileme"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__display_name
msgid "Display Name"
msgstr "Numele de afișare"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"Do you want to prioritise the list of\n"
"                                            dilemmas under\n"
"                                            criteria?"
msgstr ""
"Doriți să acordați prioritate listei de\n"
"                                            dileme sub\n"
"                                            criterii?"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__draft
msgid "Draft"
msgstr "Proiect"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__eco
msgid "ECO Score"
msgstr "Scor ECO"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__environment
msgid "ENV Score"
msgstr "Scor ENV"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Economic Factors"
msgstr "Factori economici"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Edit Project"
msgstr "Editați proiectul"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__end_year
msgid "End Year"
msgstr "Anul de sfârșit"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Enter weight"
msgstr "Introduceți greutatea"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Environmental Impact"
msgstr "Impactul asupra mediului"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__error_message
msgid "Error Message"
msgstr "Mesaj de eroare"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Error Reason"
msgstr "Motivul erorii"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__failed
msgid "Failed"
msgstr "A eșuat"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__favorite
msgid "Favorite"
msgstr "Favorit"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
msgid "Favorites"
msgstr "Favorite"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__frequency_analysis
msgid "Frequency Analysis"
msgstr "Analiza Frecvenței"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Generating frequency analysis..."
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Get Frequency"
msgstr "Obțineți frecvență"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__id
msgid "ID"
msgstr ""

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__idle
msgid "Idle"
msgstr "Inactiv"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__in_progress
msgid "In Progress"
msgstr "În curs"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Instructions"
msgstr "Instrucţiuni"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__just_transition_dialogue
msgid "Just Transition Dialogue"
msgstr "Dialog de tranziție doar"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__keyword
msgid "Keyword"
msgstr "Cuvânt cheie"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Keyword Frequency Over Years"
msgstr "Frecvența cuvintelor cheie de-a lungul anilor"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__keywords
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Keywords"
msgstr "Cuvinte cheie"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result____last_update
msgid "Last Modified on"
msgstr "Ultima modificare pe"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare de către"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Last modified:"
msgstr "Ultima modificare:"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "List of Agendas"
msgstr "Lista agendelor"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "List of Dilemmas"
msgstr "Lista de dileme"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Loading N-gram visualization..."
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Loading agendas..."
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Loading dilemmas..."
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Mark as Favorite"
msgstr "Marcați ca favorit"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__metadata
msgid "Metadata"
msgstr "Metadate"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Most common words frequency"
msgstr "Frecvența celor mai comune cuvinte"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__n_gram
msgid "N Gram"
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "N-gram Visualization"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "No"
msgstr "Nu"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Note: Twitter does not support filtering by year."
msgstr "Notă: Twitter nu acceptă filtrarea pe an."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Output"
msgstr "Ieșire"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__owner_id
msgid "Owner"
msgstr "Proprietar"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__pdf
#, python-format
msgid "PDF"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Papers"
msgstr "Hârtii"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__parent_id
msgid "Parent Agenda"
msgstr "Agenda Părinte"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Print Media"
msgstr "Media de imprimare"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__printed_media
msgid "Printed Media"
msgstr "Media tipărite"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Printed media"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Prioritise"
msgstr "Prioritizează"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Prioritise according to your criteria."
msgstr "Prioritizează în funcție de criteriile tale."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__priority
msgid "Priority"
msgstr "Prioritate"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_project
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__project_id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__project_id
msgid "Project"
msgstr "Proiect"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Project Delete Confirmation"
msgstr "Confirmare de ștergere a proiectului"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Project Description"
msgstr "Descrierea proiectului"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Project Title"
msgstr "Titlul proiectului"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Project description"
msgstr "Descrierea proiectului"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Project:"
msgstr "Proiect:"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_project
msgid "Projects"
msgstr "Proiecte"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__questions
msgid "Questions"
msgstr "Întrebări"

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Questions:"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Recent Projects"
msgstr "Proiecte recente"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__related_dilemma_id
msgid "Related Dilemma"
msgstr "Dilema legata"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_result
msgid "Result"
msgstr "Rezultat"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__result_ids
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_result
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Results"
msgstr "Rezultate"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__running
msgid "Running"
msgstr "Funcţionare"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "SL"
msgstr ""

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__soc
msgid "SOC Score"
msgstr "Scor SOC"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Saved Projects"
msgstr "Proiecte salvate"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select from predefined print media sources (BBC, The New\n"
"                                            York Times, etc.)."
msgstr ""
"Selectați dintre sursele media de imprimare predefinite (BBC, The New\n"
"                                            York Times etc.)."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select specific countries to focus on country-specific\n"
"                                            agenda topics."
msgstr ""
"Selectați anumite țări pentru a vă concentra pe anumite țări\n"
"                                            subiecte de pe ordinea de zi."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select the social media platforms (YouTube, Twitter,\n"
"                                            Facebook, etc.) where\n"
"                                            you want to search for related data."
msgstr ""
"Selectați platformele de social media (YouTube, Twitter,\n"
"                                            Facebook etc.) unde\n"
"                                            doriți să căutați date asociate."

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Show Less"
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Show More"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Social Equity"
msgstr "Echitatea socială"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__social_media
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Social Media"
msgstr ""

#. module: cep_agenda_generation
#. odoo-javascript
#: code:addons/cep_agenda_generation/static/src/js/results.js:0
#, python-format
msgid "Social media"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Source"
msgstr "Sursă"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__agenda_source_ids
msgid "Sources"
msgstr "Surse"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__start_year
msgid "Start Year"
msgstr "Anul de început"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__status
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Status"
msgstr "Stare"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Submit"
msgstr "Trimiteți"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__tech
msgid "TECH Score"
msgstr "Scor TECH"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Technology & Innovation"
msgstr "Tehnologie și inovație"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "The process may take 3 to 5 minutes to complete."
msgstr "Procesul poate dura între 3 și 5 minute."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"The system will process your inputs and extract key\n"
"                                            topics and agendas."
msgstr ""
"Sistemul vă va procesa intrările și va extrage cheia\n"
"                                            subiecte și agende."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__title
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__title
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__title
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Title"
msgstr "Titlu"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "To"
msgstr "La"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__type
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__type
msgid "Type"
msgstr "Tip"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Type in the keywords relevant to your agenda (e.g.,\n"
"                                            \"climate change,\"\n"
"                                            \"carbon emissions\")."
msgstr ""
"Introduceți cuvintele cheie relevante pentru agenda dvs. (de ex.,\n"
"                                            „schimbări climatice”,\n"
"                                            „emisii de carbon”)."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__urls
msgid "URLs"
msgstr "URL-uri"

#. module: cep_agenda_generation
#: model:ir.model.fields,help:cep_agenda_generation.field_cep_ag_result__dilemma_serial_id
msgid ""
"Unique identifier to link with cep.ag.dilemma model's dilemma_serial_id "
"field"
msgstr ""
"Identificator unic de conectat cu câmpul dilemma_serial_id al modelului "
"cep.ag.dilemma"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "Upload Dilemmas"
msgstr "Încărcați Dileme"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "Upload Excel File"
msgstr "Încărcați fișierul Excel"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Upload a files"
msgstr "Încărcați un fișier"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Uploaded\n"
"                                                    Papers"
msgstr ""
"Încărcat\n"
"                                                    Hârtii"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__year
msgid "Year"
msgstr "An"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Years"
msgstr "De ani"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Yes"
msgstr "Da"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"You will receive an output containing agenda topics\n"
"                                            categorized by country,\n"
"                                            year, and frequency trends."
msgstr ""
"Veți primi un rezultat care conține subiecte de pe agendă\n"
"                                            clasificate în funcție de țară,\n"
"                                            anul și tendințele de frecvență."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "cloud upload"
msgstr "încărcare în cloud"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__dilemma_serial_id
msgid "dilemma_serial_id"
msgstr "dilema_serial_id"
