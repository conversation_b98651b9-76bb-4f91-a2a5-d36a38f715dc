# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_agenda_generation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20250520\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-22 13:07+0000\n"
"PO-Revision-Date: 2025-05-22 13:07+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-bar-chart me-2\"/> Keyword Frequency Over Years"
msgstr "<i class=\"fa fa-bar-chart me-2\"/> Yıllara Göre Anahtar <PERSON>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Climate Assembly"
msgstr "<i class=\"fa fa-circle-o\"/> İklim Meclisi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Climate Dialogue"
msgstr "<i class=\"fa fa-circle-o\"/> İklim Diyaloğu"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-circle-o\"/> Just Transition Dialogue"
msgstr "<i class=\"fa fa-circle-o\"/> Adil Geçiş Diyaloğu"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"<i class=\"fa fa-heart\"/>\n"
"                            Favorites"
msgstr ""
"<i class=\"fa fa-heart\"/>\n"
"Favoriler"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-heart\"/> Favorites"
msgstr "<i class=\"fa fa-heart\"/> Favoriler"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<i class=\"fa fa-home\"/> Home"
msgstr "<i class=\"fa fa-home\"/> Ana Sayfa"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-line-chart me-2\"/> Most common words frequency"
msgstr "<i class=\"fa fa-line-chart me-2\"/> En yaygın kelimelerin sıklığı"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-list-alt me-2\"/> List of Agendas"
msgstr "<i class=\"fa fa-list-alt me-2\"/> Gündem Listesi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid ""
"<i class=\"fa fa-plus\"/> Create Agenda\n"
"                                <i class=\"fa fa-chevron-down submenu-icon\"/>"
msgstr ""
"<i class=\"fa fa-plus\"/> Gündem Oluştur\n"
"<i class=\"fa fa-chevron-down submenu-icon\"/>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<i class=\"fa fa-plus\"/> Create Agenda <i class=\"fa fa-chevron-down "
"submenu-icon\"/>"
msgstr ""
"<i class=\"fa fa-plus\"/> Gündem Oluştur <i class=\"fa fa-chevron-down "
"submenu-icon\"/>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<i class=\"fa fa-question-circle me-2\"/> List of\n"
"                                                Dilemmas"
msgstr ""
"<i class=\"fa fa-question-circle me-2\"/> İkilemler\n"
"Listesi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid ""
"<i class=\"fa fa-save\"/>\n"
"                            Project list"
msgstr ""
"<i class=\"fa fa-save\"/>\n"
"Proje listesi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"<i class=\"fa fa-save\"/> \n"
"                            Project list"
msgstr ""
"<i class=\"fa fa-save\"/> \n"
"Proje listesi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "<i class=\"fa fa-save\"/> Project list"
msgstr "<i class=\"fa fa-save\"/> Proje listesi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<option value=\"\">Select a country</option>"
msgstr "<option value=\"\">Bir ülke seçin</option>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid ""
"<small class=\"text-muted\">Please upload an Excel file with the following "
"columns: Dilemma ID, Title, Description, Questions, ENV, ECO, SOC, "
"TECH</small>"
msgstr ""
"<small class=\"text-muted\">Lütfen aşağıdaki sütunları içeren bir Excel "
"dosyası yükleyin: Dilemma ID, Title, Description, Questions, ENV, ECO, SOC, "
"TECH</small>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "<span class=\"badge badge-pill badge-secondary\">NA</span>"
msgstr "<span class=\"badge rozet-hap rozet-ikincil\">Yok</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">BBC</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">Nature</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The Guardian</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The New York Times</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">The Washington Post</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">Twitter</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "<span class=\"checkbox-text\">YouTube</span>"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-book\"/> Upload PDFs of relevant document (Academic Paper/Journal/Report) </span>"
msgstr ""
"<span class=\"fw-bold\">\n"
"<i class=\"fa fa-book\"/> İlgili belgenin (Akademik Makale/Dergi/Rapor) PDF'lerini yükleyin </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span class=\"fw-bold\">\n"
"                                                <i class=\"fa fa-newspaper-o\"/> Media </span>"
msgstr ""
"<span class=\"fw-bold\">\n"
"<i class=\"fa fa-newspaper-o\"/> Medya </span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "<span class=\"visually-hidden\">Loading...</span>"
msgstr "<span class=\"visually-hidden\">Yükleniyor...</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span id=\"proceed-button-text\">Proceed</span>\n"
"                                            <span id=\"proceed-button-loader\" class=\"spinner-border spinner-border-sm text-light\" style=\"display: none;\" role=\"status\">\n"
"                                                <span class=\"visually-hidden\">Loading...</span>\n"
"                                            </span>"
msgstr ""
"<span id=\"proceed-button-text\">Devam et</span>\n"
"<span id=\"proceed-button-loader\" class=\"spinner-border spinner-border-sm text-light\" style=\"display: none;\" role=\"status\">\n"
"<span class=\"visually-hidden\">Yükleniyor...</span>\n"
"</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>Delete</span>"
msgstr "<span>Sil</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>Edit</span>"
msgstr "<span>Düzenle</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"<span>Save Changes</span>\n"
"                                            <span id=\"edit-project-spinner\" class=\"spinner-border spinner-border-sm\" role=\"status\" style=\"display: none;\">\n"
"                                                <span class=\"visually-hidden\">Loading...</span>\n"
"                                            </span>"
msgstr ""
"<span>Değişiklikleri Kaydet</span>\n"
"<span id=\"edit-project-spinner\" class=\"spinner-border spinner-border-sm\" role=\"status\" style=\"display: none;\">\n"
"<span class=\"visually-hidden\">Yükleniyor...</span>\n"
"</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "<span>View results</span>"
msgstr "<span>Sonuçları görüntüle</span>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<strong> Note: The sum of all the scores of criteria\n"
"                                                    must equal 1.</strong>"
msgstr ""
"<strong> Not: Kriterlerin tüm puanlarının toplamı\n"
"1'e eşit olmalıdır.</strong>"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"<strong>0.9</strong> represents the <strong>maximum\n"
"                                                impact</strong> that should be Prioritised, while <strong>\n"
"                                                0.1</strong> indicates <strong>minimum\n"
"                                                    impact</strong> according to the criteria."
msgstr ""
"<strong>0,9</strong>, Önceliklendirilmesi gereken <strong>maksimum\n"
"etkiyi</strong> temsil ederken, <strong>0,1</strong>, kriterlere göre <strong>minimum\n"
"etkiyi</strong> ifade eder."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "AGENDA"
msgstr "GÜNDEM"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Action"
msgstr "Aksiyon"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Add Keywords"
msgstr "Anahtar Kelimeler Ekle"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__agenda
msgid "Agenda"
msgstr "Gündem"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Agenda Dashboard"
msgstr "Gündem Panosu"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_root
msgid "Agenda Generation"
msgstr "Gündem Oluşturma"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_project_action
msgid "Agenda Project"
msgstr "Gündem Projesi"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_result_action
msgid "Agenda Result"
msgstr "Gündem Sonucu"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.cep_ag_agenda_source_action
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_agenda_source
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_agenda_source
msgid "Agenda Source"
msgstr "Gündem Kaynağı"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Agenda Source Saved Successfully."
msgstr "Gündem Kaynağı Başarıyla Kaydedildi."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"All\n"
"                                                    country"
msgstr ""
"Tüm\n"
"ülke"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.menu_cep_ag_dilemma
msgid "All Dilemmas"
msgstr "Tüm İkilemler"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "An error occurred. Please try again."
msgstr "Bir hata oluştu. Lütfen tekrar deneyin."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__analyzer_status
msgid "Analyzer Status"
msgstr "Analizör Durumu"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Are you sure you want to delete this Project <span/>?"
msgstr "Bu <span/> Projesi'ni silmek istediğinizden emin misiniz?"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__attachment
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Attachment"
msgstr "EK"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__attachment_ids
msgid "Attachments"
msgstr "Ekler"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_dilemma
msgid "CEP Agenda Generation Dilemma"
msgstr "CEP Gündem Oluşturma İkilemi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_search
msgid "CEP Agenda Project"
msgstr "CEP Gündem Projesi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_result_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_result_search
msgid "CEP Agenda Result"
msgstr "CEP Gündem Sonucu"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_form
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_agenda_source_search
msgid "CEP Agenda Source"
msgstr "CEP Gündem Kaynağı"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "CLIMAS Logo"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Cancel"
msgstr "İptal etmek"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__category
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Category"
msgstr "Kategori"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__dilemma_ids
msgid "Child Dilemmas"
msgstr "Çocuk İkilemleri"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Choose a time range (e.g., 2020–2024) to extract agenda\n"
"                                            topics based on\n"
"                                            specific years."
msgstr ""
"Belirli yıllara dayalı gündem konularını çıkarmak için bir zaman aralığı "
"(örneğin 2020–2024) seçin."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Click \"Proceed\" to start the analysis."
msgstr "Analizi başlatmak için \"Devam\" butonuna tıklayın."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Click \"Upload a file/folder\" to add PDFs containing\n"
"                                            research papers or\n"
"                                            reports."
msgstr ""
"Araştırma makaleleri veya\n"
"raporlar içeren PDF'leri eklemek için \"Dosya/klasör yükle\"ye tıklayın."

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__climate_assembly
msgid "Climate Assembly"
msgstr "İklim Meclisi"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__climate_dialogue
msgid "Climate Dialogue"
msgstr "İklim Diyaloğu"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Close"
msgstr "Kapalı"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__column_a
msgid "Column A"
msgstr "Sütun A"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__column_b
msgid "Column B"
msgstr "Sütun B"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__completed
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__completed
msgid "Completed"
msgstr "Tamamlanmış"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Countries"
msgstr "Ülkeler"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__target_country
msgid "Country"
msgstr "Ülke"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid ""
"Create\n"
"                                    Agenda"
msgstr ""
"Oluştur\n"
"Gündem"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Create Agenda for"
msgstr "Gündem Oluştur"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Create Date"
msgstr "Oluşturma Tarihi"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_agenda_source_action
msgid "Create a new Agenda Source"
msgstr "Yeni bir Gündem Kaynağı Oluştur"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_project_action
msgid "Create a new Project"
msgstr "Yeni bir Proje Oluştur"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.cep_ag_result_action
msgid "Create a new Result"
msgstr "Yeni bir Sonuç Oluştur"

#. module: cep_agenda_generation
#: model_terms:ir.actions.act_window,help:cep_agenda_generation.action_cep_ag_dilemma
msgid "Create your first dilemma"
msgstr "İlk ikileminizi yaratın"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__create_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__create_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__criteria_weights
msgid "Criteria Weights"
msgstr "Kriter Ağırlıkları"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Criterion"
msgstr "Kriter"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__description
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__description
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__description
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Description"
msgstr "Tanım"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__dilemma
msgid "Dilemma"
msgstr "İkilem"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__dilemma_serial_id
msgid "Dilemma Serial"
msgstr "İkilem Serisi"

#. module: cep_agenda_generation
#: model:ir.actions.act_window,name:cep_agenda_generation.action_cep_ag_dilemma
msgid "Dilemmas"
msgstr "İkilemler"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__display_name
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__display_name
msgid "Display Name"
msgstr "Ekran adı"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid ""
"Do you want to prioritise the list of\n"
"                                            dilemmas under\n"
"                                            criteria?"
msgstr ""
"Kriterler altındaki\n"
"ikilemlerin\n"
"listesini önceliklendirmek ister misiniz?"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__draft
msgid "Draft"
msgstr "Taslak"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__eco
msgid "ECO Score"
msgstr "EKO Puanı"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__environment
msgid "ENV Score"
msgstr "ENV Puanı"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Economic Factors"
msgstr "Ekonomik Faktörler"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Edit Project"
msgstr "Projeyi Düzenle"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__end_year
msgid "End Year"
msgstr "Yıl Sonu"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Enter weight"
msgstr "Ağırlığı girin"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Environmental Impact"
msgstr "Çevresel Etki"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__error_message
msgid "Error Message"
msgstr "Hata Mesajı"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Error Reason"
msgstr "Hata Nedeni"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__failed
msgid "Failed"
msgstr "Arızalı"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__favorite
msgid "Favorite"
msgstr "Favori"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
msgid "Favorites"
msgstr "Favoriler"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__frequency_analysis
msgid "Frequency Analysis"
msgstr "Frekans Analizi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Get Frequency"
msgstr "Frekans Al"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__id
msgid "ID"
msgstr "İD"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__idle
msgid "Idle"
msgstr "Boşta"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__status__in_progress
msgid "In Progress"
msgstr "Devam etmekte"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Instructions"
msgstr "Talimatlar"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__category__just_transition_dialogue
msgid "Just Transition Dialogue"
msgstr "Adil Geçiş Diyaloğu"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__keyword
msgid "Keyword"
msgstr "Anahtar kelime"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Keyword Frequency Over Years"
msgstr "Yıllara Göre Anahtar Kelime Sıklığı"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__keywords
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Keywords"
msgstr "Anahtar kelimeler"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project____last_update
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result____last_update
msgid "Last Modified on"
msgstr "Son Değiştirilme Tarihi"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__write_uid
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleme:"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__write_date
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme Tarihi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Last modified:"
msgstr "Son değiştirilme tarihi:"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "List of Agendas"
msgstr "Gündem Listesi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "List of Dilemmas"
msgstr "İkilemler Listesi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Mark as Favorite"
msgstr "Favori olarak işaretle"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__metadata
msgid "Metadata"
msgstr "Meta veri"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Most common words frequency"
msgstr "En yaygın kelimelerin sıklığı"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_result__type__n_gram
msgid "N Gram"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "No"
msgstr "HAYIR"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Note: Twitter does not support filtering by year."
msgstr "Not: Twitter yıla göre filtrelemeyi desteklemiyor."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Output"
msgstr "Çıktı"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__owner_id
msgid "Owner"
msgstr "Mal sahibi"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__pdf
msgid "PDF"
msgstr ""

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Papers"
msgstr "Makaleler"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__parent_id
msgid "Parent Agenda"
msgstr "Ebeveyn Gündemi"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Print Media"
msgstr "Basılı Medya"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__printed_media
msgid "Printed Media"
msgstr "Basılı Medya"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Prioritise"
msgstr "Önceliklendirme"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Prioritise according to your criteria."
msgstr "Kriterlerinize göre öncelik sırası belirleyin."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__priority
msgid "Priority"
msgstr "Öncelik"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_project
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__project_id
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__project_id
msgid "Project"
msgstr "Proje"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Project Delete Confirmation"
msgstr "Proje Silme Onayı"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Project Description"
msgstr "Proje Açıklaması"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Project Title"
msgstr "Proje Başlığı"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_favorite
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Project description"
msgstr "Proje açıklaması"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Project:"
msgstr "Proje:"

#. module: cep_agenda_generation
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_project
msgid "Projects"
msgstr "Projeler"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__questions
msgid "Questions"
msgstr "Sorular"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_homepage
msgid "Recent Projects"
msgstr "Son Projeler"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__related_dilemma_id
msgid "Related Dilemma"
msgstr "İlgili İkilem"

#. module: cep_agenda_generation
#: model:ir.model,name:cep_agenda_generation.model_cep_ag_result
msgid "Result"
msgstr "Sonuç"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__result_ids
#: model:ir.ui.menu,name:cep_agenda_generation.cep_ag_result
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Results"
msgstr "Sonuçlar"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_project__analyzer_status__running
msgid "Running"
msgstr "Koşma"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "SL"
msgstr ""

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__soc
msgid "SOC Score"
msgstr "SOC Puanı"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Saved Projects"
msgstr "Kaydedilen Projeler"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select from predefined print media sources (BBC, The New\n"
"                                            York Times, etc.)."
msgstr ""
"Önceden tanımlanmış basılı medya kaynaklarından (BBC, The New York Times, "
"vb.) seçim yapın."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select specific countries to focus on country-specific\n"
"                                            agenda topics."
msgstr "Ülkeye özgü gündem konularına odaklanmak için belirli ülkeleri seçin."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Select the social media platforms (YouTube, Twitter,\n"
"                                            Facebook, etc.) where\n"
"                                            you want to search for related data."
msgstr ""
"İlgili verileri aramak istediğiniz sosyal medya platformlarını (YouTube, Twitter,\n"
"Facebook, vb.) seçin."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Social Equity"
msgstr "Sosyal Adalet"

#. module: cep_agenda_generation
#: model:ir.model.fields.selection,name:cep_agenda_generation.selection__cep_ag_agenda_source__type__social_media
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Social Media"
msgstr "Sosyal Medya"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.view_cep_ag_project_form
msgid "Source"
msgstr "Kaynak"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__agenda_source_ids
msgid "Sources"
msgstr "Kaynaklar"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__start_year
msgid "Start Year"
msgstr "Başlangıç ​​Yılı"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__status
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Status"
msgstr "Durum"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_create_project
msgid "Submit"
msgstr "Göndermek"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__tech
msgid "TECH Score"
msgstr "TECH Puanı"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.output
msgid "Technology & Innovation"
msgstr "Teknoloji ve Yenilik"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "The process may take 3 to 5 minutes to complete."
msgstr "İşlemin tamamlanması 3 ila 5 dakika sürebilir."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"The system will process your inputs and extract key\n"
"                                            topics and agendas."
msgstr ""
"Sistem girdilerinizi işleyecek ve temel konuları ve gündemleri çıkaracaktır."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__title
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_project__title
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__title
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Title"
msgstr "Başlık"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "To"
msgstr "İle"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__type
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__type
msgid "Type"
msgstr "Tip"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Type in the keywords relevant to your agenda (e.g.,\n"
"                                            \"climate change,\"\n"
"                                            \"carbon emissions\")."
msgstr ""
"Gündeminizle ilgili anahtar kelimeleri yazın (örneğin,\n"
"\"iklim değişikliği\",\n"
"\"karbon emisyonları\")."

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_agenda_source__urls
msgid "URLs"
msgstr "URL'ler"

#. module: cep_agenda_generation
#: model:ir.model.fields,help:cep_agenda_generation.field_cep_ag_result__dilemma_serial_id
msgid ""
"Unique identifier to link with cep.ag.dilemma model's dilemma_serial_id "
"field"
msgstr ""
"cep.ag.dilemma modelinin dilemma_serial_id alanıyla bağlantı kurmak için "
"benzersiz tanımlayıcı"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "Upload Dilemmas"
msgstr "Yükleme İkilemleri"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.cep_ag_dilemma_upload
msgid "Upload Excel File"
msgstr "Excel Dosyasını Yükle"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Upload a files"
msgstr "Dosyaları yükleyin"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"Uploaded\n"
"                                                    Papers"
msgstr ""
"Yüklendi\n"
"Makaleler"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_result__year
msgid "Year"
msgstr "Yıl"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "Years"
msgstr "Yıllar"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.project_list
msgid "Yes"
msgstr "Evet"

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid ""
"You will receive an output containing agenda topics\n"
"                                            categorized by country,\n"
"                                            year, and frequency trends."
msgstr ""
"Ülke, yıl ve sıklık eğilimlerine göre kategorize edilmiş gündem konularını "
"içeren bir çıktı alacaksınız."

#. module: cep_agenda_generation
#: model_terms:ir.ui.view,arch_db:cep_agenda_generation.ag_add_keyword
msgid "cloud upload"
msgstr "bulut yükleme"

#. module: cep_agenda_generation
#: model:ir.model.fields,field_description:cep_agenda_generation.field_cep_ag_dilemma__dilemma_serial_id
msgid "dilemma_serial_id"
msgstr "ikilem_seri_kimliği"





#. module: cep_agenda_generation
#. javascript: cep_agenda_generation/static/src/js/results.js
msgid "Loading dilemmas..."
msgstr "İkilemler yükleniyor..."

#. module: cep_agenda_generation
#. javascript: cep_agenda_generation/static/src/js/results.js
msgid "Loading agendas..."
msgstr "Gündemler yükleniyor..."

#. module: cep_agenda_generation
#. javascript: cep_agenda_generation/static/src/js/results.js
msgid "Loading N-gram visualization..."
msgstr "N-gram görselleştirmesi yükleniyor..."

#. module: cep_agenda_generation
#. javascript: cep_agenda_generation/static/src/js/results.js
msgid "Show More"
msgstr "Daha Fazlasını Göster"

#. module: cep_agenda_generation
#. javascript: cep_agenda_generation/static/src/js/results.js
msgid "Show Less"
msgstr "Daha Az Göster"

#. module: cep_agenda_generation
#. javascript: cep_agenda_generation/static/src/js/results.js
msgid "Questions:"
msgstr "Sorular:"

#. module: cep_agenda_generation
#. javascript: cep_agenda_generation/static/src/js/results.js
msgid "Grouped Dilemmas for the respective Agenda:"
msgstr "İlgili Gündem için Gruplandırılmış İkilemler:"

#. module: cep_agenda_generation
#. javascript: cep_agenda_generation/static/src/js/results.js
msgid "Reference"
msgstr "Referans"

#. module: cep_agenda_generation
#. javascript: cep_agenda_generation/static/src/js/results.js
msgid "PDF"
msgstr "PDF"

#. module: cep_agenda_generation
#. javascript: cep_agenda_generation/static/src/js/results.js
msgid "Printed media"
msgstr "Basılı medya"

#. module: cep_agenda_generation
#. javascript: cep_agenda_generation/static/src/js/results.js
msgid "Social media"
msgstr "Sosyal medya"

#. module: cep_agenda_generation
#. javascript: cep_agenda_generation/static/src/js/results.js
msgid "N-gram Visualization"
msgstr "N-gram Görselleştirme"