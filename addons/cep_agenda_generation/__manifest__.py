{ 
    'name': "CEP Agenda Generation", 
    'summary': "Module of CEP Climate Assembly Agenda Generation", 
    'description': """TODO""", 
    'author': "Technovative Solutions LTD", 
    'license': "AGPL-3", 
    'website': "https://www.technovativesolutions.co.uk", 
    'category': 'Tools', 
    'version': '0.0.1', 
    'depends': [
        'base',
        'website',
        'rabbitmq',
        'portal'
    ],
    'data': [
        'security/ir.model.access.csv',
        'security/attachment_rule.xml',
        'security/security.xml',
        'views/cep_ag_project_view.xml',
        'views/cep_ag_agenda_source_view.xml',
        'views/cep_ag_result_view.xml',
        'views/website/cep_ag_homepage.xml',
        'views/website/cep_ag_favorite.xml',
        'views/website/cep_ag_create_project.xml',
        'views/website/cep_ag_add_keyword.xml',
        'views/website/cep_ag_project_list.xml',
        'views/website/cep_ag_output.xml',
        'views/website/cep_ag_dilemma_upload.xml',
        'views/cep_ag_dilemma_view.xml',
        'views/cep_ag_pre_defined_pdf_view.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'cep_agenda_generation/static/src/css/backend.css',
        ],
        'cep_agenda_generation.ag_assets': [
            
            'cep_agenda_generation/static/src/css/common.css',
            'cep_agenda_generation/static/src/css/sidebar.css',
            'cep_agenda_generation/static/src/css/output.css',
            'cep_agenda_generation/static/src/css/prioritize.css',
            'cep_agenda_generation/static/src/css/saved-projects.css',
            'cep_agenda_generation/static/src/css/new-project.css',
            'https://code.jquery.com/jquery-3.7.1.min.js',
            # 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
            'https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js',
            'cep_agenda_generation/static/src/js/keyword.js',
            'cep_agenda_generation/static/src/js/main.js',
        ],
        "cep_agenda_generation.ag_tagify": [
            'cep_agenda_generation/static/src/css/tagify.css',   
            'cep_agenda_generation/static/src/js/tagify.js',
        ],
        'cep_agenda_generation.ag_results': [
        'cep_agenda_generation/static/src/js/plotly.min.js',
        'cep_agenda_generation/static/src/js/results.js',
        ]
      
    },
   
    'installable': True,
    'application': True,
    'auto_install': False,
    
}
