<?xml version="1.0" encoding="utf-8"?>
<odoo>


    <!-- Search (Filter) View -->
    <record id="view_cep_ag_project_search" model="ir.ui.view">
        <field name="name">cep.ag.project.search</field>
        <field name="model">cep.ag.project</field>
        <field name="arch" type="xml">
            <search string="CEP Agenda Project">
                <field name="title"/>
                <field name="description"/>
                <field name="target_country"/>
                <field name="start_year"/>
                <field name="end_year"/>
                <field name="status"/>
            </search>
        </field>
    </record>


    <!-- Form View -->
    <record id="view_cep_ag_project_form" model="ir.ui.view">
        <field name="name">cep.ag.project.form</field>
        <field name="model">cep.ag.project</field>
        <field name="arch" type="xml">
            <form string="CEP Agenda Project">
                <sheet>
                    <group>
                        <field name="title"/>
                        <field name="description"/>
                        <field name="target_country"/>
                        <field name="start_year" widget="char"/>
                        <field name="end_year" widget="char"/>
                        <field name="status"/>
                    </group>


                    <notebook>
                        <page name="agenda_source" string="Source">
                            <field name="agenda_source_ids" widget="one2many_list"
                                options="{'no_create': True, 'no_open': True}" readonly="1">
                                <tree editable="top">
                                    <field name="type"/>
                                    <field name="urls"/>
                                    <field name="attachment_ids"/>
                                </tree>
                                <form>
                                    <sheet>
                                        <group>
                                            <field name="type"/>
                                            <field name="urls"/>
                                        </group>
                                        <notebook>
                                            <page name="attachments" string="Attachment">

                                                <field name="attachment_ids" widget="one2many_list"
                                                    options="{'no_create': True, 'no_open': True}" readonly="1">
                                                    <tree editable="top">
                                                        <field name="name"/>
                                                        <field name="create_date"/>
                                                    </tree>
                                                    <form>

                                                        <sheet>
                                                            <group>
                                                                <field name="type"/>
                                                                <field name="datas" filename="name"
                                                                    attrs="{'invisible':[('type','=','url')]}"/>
                                                                <field name="url" widget="url"
                                                                    attrs="{'invisible':[('type','=','binary')]}"/>
                                                                <field name="mimetype" groups="base.group_no_one"/>
                                                            </group>

                                                        </sheet>

                                                    </form>
                                                </field>
                                            </page>
                                        </notebook>
                                    </sheet>
                                </form>
                            </field>
                        </page>


                        <page name="results" string="Results">
                            <field name="result_ids" widget="one2many_list"
                                options="{'no_create': True, 'no_open': True}" readonly="1">
                                <tree editable="top">
                                    <field name="type"/>
                                    <field name="title"/>
                                </tree>
                                <form>
                                    <sheet>
                                        <group>
                                            <field name="type"/>
                                            <field name="title"/>
                                            <field name="description"/>
                                        </group>
                                    </sheet>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>


    <!-- List (Tree) View -->
    <record id="view_cep_ag_project_tree" model="ir.ui.view">
        <field name="name">cep.ag.project.tree</field>
        <field name="model">cep.ag.project</field>
        <field name="arch" type="xml">
            <tree>
                <field name="title"/>
                <field name="description"/>
                <field name="target_country"/>
                <field name="start_year"/>

                <field name="end_year"/>

                <field name="status"/>
            </tree>
        </field>
    </record>


    <!-- action -->
    <record id='cep_ag_project_action' model='ir.actions.act_window'>
        <field name="name">Agenda Project</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.ag.project</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new Project
            </p>
        </field>
    </record>

    <menuitem id="cep_ag_root" name="Agenda Generation" sequence="1"/>
    <menuitem id="cep_ag_project" parent="cep_ag_root" name="Projects" action="cep_ag_project_action"/>
</odoo>