<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="cep_agenda_generation.ag_homepage" name="Agenda Generation Homepage">
        <t t-call="website.layout">
            <t t-set="head">
                <!-- Include custom assets -->
                <t t-call-assets="web.assets_frontend_minimal"/>
                <t t-call-assets="cep_agenda_generation.ag_assets"/>
            </t>
            <t t-set="title"> Agenda Dashboard</t>
            <div id="wrapwrap" class="homepage">

                <aside class="sidebar">
                    <div class="climas-logo">
                        <img src="/cep_agenda_generation/static/src/img/CLIMAS_logo.png" alt="CLIMAS Logo"/>
                    </div>
                    
                    <div class="header">
                        <h4>AGENDA</h4>
                    </div>
                    <div class="divider"></div>
                    <div class="menu">
                        <a href="/cep_agenda_generation" class="menu-item active"><i class="fa fa-home"></i> Home</a>
                        <div class="menu-item-group">
                            <a href="#" class="menu-item has-submenu" id="create-project-menu">
                                <i class="fa fa-plus"></i> Create Agenda
                                <i class="fa fa-chevron-down submenu-icon"></i>
                            </a>
                            <div class="submenu">
                                <a href="/cep_agenda_generation/create_project?type=climate_assembly" class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Climate Assembly
                                </a>
                                <a href="/cep_agenda_generation/create_project?type=climate_dialogue" class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Climate Dialogue
                                </a>
                                <a href="/cep_agenda_generation/create_project?type=just_transition_dialogue" class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Just Transition Dialogue
                                </a>
                            </div>
                        </div>
                        <a href="/cep_agenda_generation/list" class="menu-item"><i class="fa fa-save"></i> 
                            Project list</a>
                        <a href="/cep_agenda_generation/project/favorites" class="menu-item"><i class="fa fa-heart"></i>
                            Favorites</a>
                        <!-- <a href="settings.html" class="menu-item"><i class="fa fa-cog"></i> Settings</a> -->
                    </div>
                </aside>

                <!-- Button to toggle sidebar for small screens -->
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i id="sidebar-icon" class="fa fa-bars"></i>
                </button>

                <!-- Main Content -->
                <main class="main-content homepage">
                    <!-- Main Heading -->

                    <!-- Container -->
                    <div class="container-fluid mt-4">
                        <!-- Create Project Section -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <button class="btn btn-primary"
                                    onclick="window.location.href='/cep_agenda_generation/create_project?type=climate_assembly'">Create
                                    Agenda</button>
                            </div>
                        </div>

                        <!-- Saved Projects Section -->
                        <div class="row mt-4">
                            <h5 class="subheading">Recent Projects</h5>

                            <!-- Project 1 -->
                            <!-- <div class="col-12">
                                <p class="title">1. Title: Agenda Creation for UK Climate Assembly</p>
                                <div class="d-flex flex-wrap gap-3">
                                    <span class="badge badge-pill badge-secondary">NA</span>
                                    <span class="badge badge-pill badge-danger">Completion: 49%</span>
                                    <span class="badge badge-pill badge-primary">Last modified: January 9, 2025</span>
                                </div>
                            </div> -->

                            <t t-foreach="projects" t-as="project">


                                <!-- Project 2 -->
                                <div class="col-12 mt-3">
                                    <div class="card" style="box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1); border: 1px solid rgba(0, 0, 0, 0.1)">
                                        <div class="card-body" >
                                            <p class="title">
                                                <a t-att-href="'/cep_agenda_generation/%d/view' % project.id">
                                                    <t t-esc="project_index + 1"/>. <t t-esc="project.title"/>
                                                </a> 
                                            </p>
                                            <div class="d-flex flex-wrap gap-3">
                                                <t t-if="project.target_country">
                                                    <span class="badge badge-pill badge-primary">
                                                        <t t-esc="project.target_country"/>
                                                    </span>

                                                </t>

                                                <t t-else="">
                                                    <span class="badge badge-pill badge-secondary">NA</span>
                                                </t>


                                                <span class="badge badge-pill badge-secondary"
                                                    t-att-class="'badge badge-pill ' + {
                                                                                    'draft': 'badge-secondary',
                                                                                    'in_progress': 'badge-primary',
                                                                                    'completed': 'badge-success',
                                                                                    'failed': 'badge-danger'
                                                                                }.get(project.status, 'badge-dark')">
                                                    <t
                                                        t-esc="dict(project._fields['status'].selection).get(project.status)"/>
                                                </span>


                                                <span class="badge badge-pill badge-primary">Last modified: <t t-out="project.write_date" t-options="{'widget': 'datetime', 'format': 'MMMM dd, yyyy h:mm:ss a'}"/></span>
                                            </div>
                                            <div class="mt-3">
                                                <h5 class="title">Project description</h5>

                                                <p class="project-description">
                                                    <t t-esc="project.description"/>
                                                </p>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </div>
                </main>


            </div>
        </t>
    </template>
</odoo>