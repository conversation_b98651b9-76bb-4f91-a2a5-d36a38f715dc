<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <template id="cep_agenda_generation.ag_add_keyword" name="Agenda Generation Create Project">
        <t t-call="website.layout">

            <t t-set="head">

                <!-- Include custom assets -->
                <t t-call-assets="web.assets_frontend_minimal"/>
                <t t-call-assets="cep_agenda_generation.ag_tagify"/>
                <t t-call-assets="cep_agenda_generation.ag_assets"/>
            </t>

            <t t-set="title"> Agenda Dashboard</t>
            <div id="wrapwrap" class="homepage">

                <aside class="sidebar">
                    <div class="climas-logo">
                        <img src="/cep_agenda_generation/static/src/img/CLIMAS_logo.png" alt="CLIMAS Logo"/>
                    </div>
                    <div class="header">
                        <h4>AGENDA</h4>
                    </div>
                    <div class="divider"></div>
                    <div class="menu">
                        <a href="/cep_agenda_generation" class="menu-item active"><i class="fa fa-home"></i> Home</a>
                        <div class="menu-item-group">
                            <a href="#" class="menu-item has-submenu" id="create-project-menu">
                                <i class="fa fa-plus"></i> Create Agenda <i class="fa fa-chevron-down submenu-icon"></i>
                            </a>
                            <div class="submenu">
                                <a href="/cep_agenda_generation/create_project?type=climate_assembly"
                                    class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Climate Assembly </a>
                                <a href="/cep_agenda_generation/create_project?type=climate_dialogue"
                                    class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Climate Dialogue </a>
                                <a href="/cep_agenda_generation/create_project?type=just_transition_dialogue"
                                    class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Just Transition Dialogue </a>
                            </div>
                        </div>
                        <a href="/cep_agenda_generation/list" class="menu-item"><i class="fa fa-save"></i> Project list</a>
                        <a href="/cep_agenda_generation/project/favorites" class="menu-item"><i class="fa fa-heart"></i>
                            Favorites</a>
                    </div>
                </aside>

                <!-- Main Content -->
                <main class="main-content new-project-main-content">
                    <h4 class="heading-4">
                        <t t-esc="project.title"/>

                        <button type="button" class="btn btn-outline-primary  ms-2 me-0 project-edit"
                            data-bs-toggle="modal" data-bs-target="#editProjectModal" title="Edit Project">
                            <i class="fa fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-outline-warning  ms-1 project-favorite" id="favorite-btn"
                            title="Mark as Favorite">
                            <i t-attf-class="fa #{project.favorite and 'fa-star' or 'fa-star-o'}"></i>
                        </button>


                    </h4>
                    <hr/>
                    <div class="container-fluid mt-4">

                        <div class="row justify-content-start">
                            <div class="col-xl-12 col-md-12 col-xxl-8">
                                <input type='hidden' name='project' id='project_id' t-att-value="project.id"/>

                                <t t-if="social_media and social_media.id">
                                    <input type='hidden' name='id' id='social_media_source_id'
                                        t-att-value="social_media.id"/>
                                </t>
                                <t t-if="printed_media and printed_media.id">
                                    <input type='hidden' name='id' id='printed_media_source_id'
                                        t-att-value="printed_media.id"/>
                                </t>
                                <t t-if="pdf and pdf.id">
                                    <input type='hidden' name='id' id='pdf_source_id' t-att-value="pdf.id"/>
                                </t>


                                <div class="keywords-section my-4 row">
                                    <div class="col-lg-10 col-xl-8">
                                        <h5 class="section-heading"> Keywords <div class="js-tooltip">
                                                <span class="info-symbol ms-1">
                                                    <i class="fa fa-info"></i>
                                                </span>
                                                <div class="tooltip-content">
                                                    Enter climate-related keywords (e.g., "climate change", "carbon emissions") that will guide the analysis. You can select from predefined keywords or add your own custom terms. These keywords help identify relevant content from your selected data sources.
                                                </div>
                                            </div>
                                        </h5>
                                        <!-- Search Input Field -->
                                        <textarea id="keywords-input" type="text" class="" col="4"
                                            placeholder="Add Keywords">
                                            <t t-esc="project.keywords" />
                                        </textarea>
                                        <div class="invalid-feedback" id="keywords-error"></div>
                                        <small class="form-text text-muted mt-1">For better results, please add at least
                                            3 relevant keywords.</small>
                                    </div>

                                </div>
                                <div class="row">
                                    <div class="col-xl-6 col-lg-8">

                                        <div class="form-group">
                                            <label class="form-label"> Years <div class="js-tooltip">
                                                    <span class="info-symbol ms-1">
                                                        <i class="fa fa-info"></i>
                                                    </span>
                                                    <div class="tooltip-content">
                                                        Define the time range (e.g., 2020-2024) for data collection and analysis. This determines which content from your selected sources will be included in the dilemma identification and agenda generation process.
                                                    </div>
                                                </div>

                                            </label>
                                            <div class="years-input-group align-items-center">
                                                <div class=" w-100">
                                                    <select name="year" required="1" class="year-input w-100"
                                                        id="start-year">
                                                        <t t-foreach="range(2000, datetime.datetime.now().year + 1)"
                                                            t-as="year">
                                                            <option t-att-value="year"
                                                                t-att-selected="'selected' if project.start_year and int(project.start_year) == year else None">
                                                                <t t-esc="year"/>
                                                            </option>
                                                        </t>
                                                    </select>
                                                    <div class="invalid-feedback" id="start-year-error"></div>


                                                </div>
                                                <label> To </label>
                                                <div class="w-100">

                                                    <select name="year" required="1" class="year-input w-100"
                                                        id="end-year">
                                                        <t t-foreach="range(2000, datetime.datetime.now().year + 1)"
                                                            t-as="year">
                                                            <option t-att-value="year"
                                                                t-att-selected="'selected' if project.end_year and int(project.end_year) == year else None">
                                                                <t t-esc="year"/>
                                                            </option>
                                                        </t>
                                                    </select>
                                                    <div class="invalid-feedback" id="end-year-error"></div>

                                                </div>

                                            </div>
                                        </div>


                                        <!-- <div class="form-group checkbox-group py-3"
                                        id="default-data-checkbox-container">
                                            <label class="checkbox-label">
                                                <input type="checkbox" class="custom-checkbox" value="true"
                                                    id="default-data-checkbox"/>
                                                <span>
                                                    Do you want to enhance your input by integrating default data?
                                                </span>
                                            </label>
                                            <div class="invalid-feedback" id="default-data-checkbox-error"></div>

                                        </div> -->
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-8 mt-3">
                                        <div class="form-group checkbox-group">
                                            <label class="checkbox-label" for="use-ontology-checkbox"
                                                id="ontology-checkbox-label">
                                                <input type="checkbox" class="custom-checkbox" value="true"
                                                    t-att-checked="true if project.use_ontology else null"
                                                    id="use-ontology-checkbox"/>
                                                <span> Use Ontology
                                                     <div class="js-tooltip">
                                                        <span class="info-symbol ms-1">
                                                            <i class="fa fa-info"></i>
                                                        </span>
                                                        <div class="tooltip-content">
                                                            Enable ontology integration to enhance the analysis with structured knowledge relationships. This improves the identification of climate-related dilemmas and agenda items by using semantic understanding of climate concepts.
                                                        </div>
                                                    </div>
                                                </span>
                                            </label>
                                            <div class="invalid-feedback" id="use-ontology-error"></div>
                                        </div>
                                    </div>
                                </div>


                                <div class=" row">

                                    <div class="col-12">
                                        <hr></hr>
                                    </div>
                                    <!-- Print Media Tab Content -->
                                    <ul class="nav nav-tabs my-3 ms-2" id="myTab" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <span class="fw-bold">
                                                <i class="fa fa-newspaper-o"></i> Media 

                                                 
                                            </span>
                                        </li>

                                    </ul>
                                    <!-- Checkboxes Section -->
                                    <div class="checkbox-section col-lg-6">
                                        <h5 class="section-heading">Print Media

                                            <div class="js-tooltip">
                                                        <span class="info-symbol ms-1">
                                                            <i class="fa fa-info"></i>
                                                        </span>
                                                        <div class="tooltip-content">
                                                           Select print media sources (newspapers, magazines) to analyze for climate-related content. The tool will search these sources for articles containing your keywords within the specified time period to identify climate dilemmas and generate agenda items.
                                                        </div>
                                                    </div>
                                        </h5>
                                        <!-- First Checkbox Row -->
                                        <div class="checkbox-row">
                                            <label class="checkbox-label">
                                                <input type="checkbox"
                                                    t-att-checked="true if printed_media and 'bbc.com' in printed_media.urls else null"
                                                    class="printed-media-checkbox custom-checkbox" value="bbc.com"/>
                                                <span class="checkbox-text">BBC</span>
                                            </label>
                                        </div>
                                        <!-- Second Checkbox Row -->
                                        <div class="checkbox-row">
                                            <label class="checkbox-label">
                                                <input type="checkbox"
                                                    t-att-checked="true if printed_media and 'newyorktimes.com' in printed_media.urls else null"
                                                    class="printed-media-checkbox custom-checkbox"
                                                    value="newyorktimes.com"/>
                                                <span class="checkbox-text">The New York Times</span>
                                            </label>
                                        </div>
                                        <!-- Third Checkbox Row -->
                                        <div class="checkbox-row">
                                            <label class="checkbox-label">
                                                <input type="checkbox"
                                                    t-att-checked="true if printed_media and 'nature.com' in printed_media.urls else null"
                                                    class="printed-media-checkbox custom-checkbox" value="nature.com"/>
                                                <span class="checkbox-text">Nature</span>
                                            </label>
                                        </div>
                                        <!-- Fourth Checkbox Row -->
                                        <div class="checkbox-row">
                                            <label class="checkbox-label">
                                                <input
                                                    t-att-checked="true if printed_media and 'theguardian.com' in printed_media.urls else null"
                                                    type="checkbox" class="printed-media-checkbox custom-checkbox"
                                                    value="theguardian.com"/>
                                                <span class="checkbox-text">The Guardian</span>
                                            </label>
                                        </div>
                                        <div class="checkbox-row">
                                            <label class="checkbox-label">
                                                <input
                                                    t-att-checked="true if printed_media and 'washingtonpost.com' in printed_media.urls else null"
                                                    type="checkbox" class="printed-media-checkbox custom-checkbox"
                                                    value="washingtonpost.com"/>
                                                <span class="checkbox-text">The Washington Post</span>
                                            </label>
                                        </div>
                                        <div class="text-danger" id="media-error"></div>

                                    </div>

                                    <div class="checkbox-section  col-lg-6">
                                        <h5 class="section-heading"> Social Media

                                            <div class="js-tooltip">
                                                        <span class="info-symbol ms-1">
                                                            <i class="fa fa-info"></i>
                                                        </span>
                                                        <div class="tooltip-content">
                                                           Choose social media platforms to analyze for climate discussions and public sentiment. The tool will extract posts, comments, and discussions related to your keywords to understand public opinion and identify emerging climate issues.
                                                        </div>
                                                    </div>
                                        </h5>
                                        <!-- First Checkbox Row -->
                                        <div class="checkbox-row">
                                            <label class="checkbox-label">
                                                <input type="checkbox"
                                                    t-att-checked="true if social_media and  'https://www.youtube.com' in social_media.urls else null"
                                                    class="custom-checkbox media-checkbox"
                                                    value="https://www.youtube.com" id="youtube-check"/>
                                                <span class="checkbox-text">YouTube</span>
                                            </label>
                                        </div>
                                        <!-- Second Checkbox Row -->
                                        <div class="checkbox-row">
                                            <label class="checkbox-label">
                                                <input type="checkbox"
                                                    t-att-checked="true if  social_media and 'https://www.twitter.com' in social_media.urls else null"
                                                    class="custom-checkbox media-checkbox"
                                                    value="https://www.twitter.com" id="twitter-check"/>
                                                <span class="checkbox-text">Twitter</span>
                                            </label>
                                        </div>
                                        <!-- Third Checkbox Row -->
                                        <div class="checkbox-row">
                                            <label class="checkbox-label">
                                                <input type="checkbox"
                                                    t-att-checked="true if social_media and 'bluesky' in social_media.urls else null"
                                                    class="custom-checkbox media-checkbox" value="bluesky"
                                                    id="facebook-check"/>
                                                <span class="checkbox-text">blusky</span>
                                            </label>
                                        </div>
                                        <!-- Fourth Checkbox Row -->
                                        <!-- <div class="checkbox-row">
                                            <label class="checkbox-label">
                                                <input type="checkbox"
                                                    t-att-checked="true if social_media and 'https://https://scholar.google.com/' in social_media.urls
                                        else null"
                                                    class="custom-checkbox media-checkbox"
                                                    value="https://scholar.google.com/" id="google-scholar-check"/>
                                                <span class="checkbox-text">Google Scholar</span>
                                            </label>
                                        </div> -->
                                    </div>

                                    <!-- Heading for Print Media Section -->
                                    <!-- <div class="col-md-6">

                                        <h5 class="section-heading">Web Address</h5>
                                        <input type="hidden" id="urls"
                                            t-att-value="printed_media and printed_media.urls"/>
                                        <div class="search-row-container">
                                            <div class="search-row " id="search-inputs-container">

                                            </div>
                                            <div>
                                                <button class="add-btn" id="add-btn">
                                                    <i class="fa fa-plus"></i>
                                                </button>
                                            </div>

                                        </div>
                                        <div class="" id="search-inputs-container-additional">

                                        </div>
                                        <div class="text-danger mt-1" id="search-error"></div>
                                    </div>


                                    <div class="col-md-6">
                                        <button id="print-media-save" class="save-btn mt-4">
                                            <span id="print-button-text">Save Changes</span>
                                            <span id="print-button-loader"
                                                class="spinner-border spinner-border-sm text-light"
                                                style="display: none;" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </span>
                                        </button>
                                    </div>

                                     -->
                                </div>


                                <div class=" row">
                                    <!-- Academic Paper  Content -->

                                    <ul class="nav nav-tabs my-3 ms-2" id="myTab" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <span class="fw-bold">
                                                <i class="fa fa-book"></i> Relevant documents (Academic
                                                Paper/Journal/Report) 

                                                <div class="js-tooltip">
                                                        <span class="info-symbol ms-1">
                                                            <i class="fa fa-info"></i>
                                                        </span>
                                                        <div class="tooltip-content">
                                                           Upload relevant documents (PDFs, reports, studies) that contain climate-related information. These documents will be analyzed alongside media sources to provide comprehensive insights for dilemma identification and agenda generation. You can also select from predefined climate documents.
                                                        </div>
                                                    </div>
                                            </span>
                                        </li>
                                    </ul>

                                    <!-- Tab navigation -->
                                    <ul class="nav nav-tabs" id="pdfTabs" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="upload-tab" data-bs-toggle="tab"
                                                data-bs-target="#upload-content" type="button" role="tab"
                                                aria-controls="upload-content" aria-selected="true">
                                                Upload PDFs
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="predefined-tab" data-bs-toggle="tab"
                                                data-bs-target="#predefined-content" type="button" role="tab"
                                                aria-controls="predefined-content" aria-selected="false">
                                                Select Predefined PDFs
                                            </button>
                                        </li>
                                    </ul>

                                    <div class="row">
                                        <div class="col-md-6 mt-3 p-0">
                                            <div class="form-group">
                                                <label for="country" class="form-label">Countries</label>
                                                <select id="country" class="dropdown">
                                                    <option value="">Select a country</option>
                                                    <option value="all"
                                                        t-att-selected="'selected' if project.target_country == 'all' else None">All
                                                        country</option>
                                                    <t t-foreach="countries" t-as="country">
                                                        <option t-att-value="country.name"
                                                            t-att-selected="'selected' if project.target_country == country.name else None">
                                                            <t t-esc="country.name"/>
                                                        </option>
                                                    </t>
                                                </select>
                                                <div class="invalid-feedback" id="country-error"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Tab content -->
                                    <div class="tab-content" id="pdfTabsContent">
                                        <!-- Upload PDFs Tab -->
                                        <div class="tab-pane fade show active" id="upload-content" role="tabpanel"
                                            aria-labelledby="upload-tab">
                                            <div class="row ">
                                                <div class="col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label for="upload-paper" class="form-label">Upload Papers</label>
                                                        <div class="upload-field d-flex align-items-center">
                                                            <!-- Hidden file input -->
                                                            <input type="file" id="upload-paper" class="upload-input"
                                                                accept=".pdf" multiple=""/>

                                                            <!-- Custom file upload text -->
                                                            <div class="upload-text" id="upload-text">
                                                                Upload files
                                                            </div>

                                                            <!-- Upload button with icon -->
                                                            <button type="button" class="btn btn-link upload-btn"
                                                                id="upload-button">
                                                                <img
                                                                    src="/cep_agenda_generation/static/src/image/cloud_upload.png"
                                                                    width="36" height="27" alt="cloud upload"/>
                                                            </button>
                                                        </div>

                                                        <ul class="file-list" id="file-list"></ul>

                                                        <t t-if="pdf and pdf.attachment_ids">
                                                            <label for="upload-paper" class="form-label">Uploaded
                                                                Papers</label>
                                                            <ul class="file-list" id="file-list-server">
                                                                <t t-foreach="pdf.attachment_ids" t-as="attachment">
                                                                    <li>
                                                                        <t t-esc="attachment.name"/>
                                                                    </li>
                                                                </t>
                                                            </ul>
                                                        </t>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Predefined PDFs Tab -->
                                        <div class="tab-pane fade" id="predefined-content" role="tabpanel"
                                            aria-labelledby="predefined-tab">
                                            <div class="row ">
                                                <div class="col-md-8">
                                                    <div class="form-group">
                                                        <label for="predefined-pdfs" class="form-label">Select
                                                            Predefined PDFs</label>
                                                        <div class="predefined-pdfs-container">
                                                            <!-- This section is populated with predefined PDFs from the
                                                            controller -->
                                                            <t t-if="predefined_pdfs">
                                                                <div class="mb-3">
                                                                    <button type="button"
                                                                        class="btn btn-sm btn-outline-secondary"
                                                                        id="select-all-pdfs">Select All</button>
                                                                    <button type="button"
                                                                        class="btn btn-sm btn-outline-secondary ms-2"
                                                                        id="deselect-all-pdfs">Deselect All</button>
                                                                </div>
                                                                <div class="predefined-pdfs-list"
                                                                    style="max-height: 300px; overflow-y: auto;">
                                                                    <div class="form-check" t-foreach="predefined_pdfs"
                                                                        t-as="pre_pdf">
                                                                        <input
                                                                            class="form-check-input predefined-pdf-checkbox"
                                                                            type="checkbox" t-att-value="pre_pdf.id"
                                                                            t-att-id="'predefined-pdf-' + str(pre_pdf.id)"
                                                                            t-att-checked="pre_pdf.id in pdf.pre_defined_pdf_ids.ids"/>
                                                                        <label class="form-check-label"
                                                                            t-att-for="'predefined-pdf-' + str(pre_pdf.id)">
                                                                            <t t-esc="pre_pdf.name"/>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                                <div class="mt-2">
                                                                    <small class="text-muted">Selected: <span
                                                                            id="selected-pdfs-count">0</span> PDFs</small>
                                                                </div>
                                                            </t>
                                                            <div t-if="not predefined_pdfs" class="no-pdfs-message mt-2">
                                                                <p class="text-muted">No predefined PDFs available.</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="row">

                                    <div class="col-md-4 mt-3">
                                        <button id="proceed-btn" class="proceed-btn">
                                            <span id="proceed-button-text">Proceed</span>
                                            <span id="proceed-button-loader"
                                                class="spinner-border spinner-border-sm text-light"
                                                style="display: none;" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </span>
                                        </button>
                                    </div>
                                </div>


                            </div>

                            <div class="col-xl-4 col-md-12 justify-content-center mt-md-4">
                                <div class="card instruction-box p-3"
                                    style="background-color: #e7f1ff; 
            border: 2px solid #e7f1ff; 
            border-radius: 5px;">
                                    <h5 class="card-title">Instructions</h5>
                                    <ol>

                                        <li class="mb-1">
                                            Type in the keywords relevant to your agenda (e.g.,
                                            "climate change,"
                                            "carbon emissions").

                                        </li>
                                        <li class="mb-1">
                                            Choose a time range (e.g., 2020–2024) to extract agenda
                                            topics based on
                                            specific years.

                                        </li>
                                        <!-- <li class="mb-1">
                                            Check the box if you want to enhance your input with default data for better
                                            insights.

                                        </li> -->
                                        <li class="mb-1">
                                            Select the social media platforms (YouTube, Twitter,
                                            Facebook, etc.) where
                                            you want to search for related data.

                                        </li>
                                        <li class="mb-1">
                                            Select from predefined print media sources (BBC, The New
                                            York Times, etc.).

                                        </li>
                                        <li class="mb-1">
                                            Select specific countries to focus on country-specific
                                            agenda topics.
                                        </li>


                                        <li class="mb-1">
                                            Click "Upload a file/folder" to add PDFs containing
                                            research papers or
                                            reports.

                                        </li>

                                        <li class="mb-1">
                                            Click "Proceed" to start the analysis.

                                        </li>
                                        <li class="mb-1">
                                            The system will process your inputs and extract key
                                            topics and agendas.


                                        </li>
                                        <li class="mb-1">
                                            The process may take 3 to 5 minutes to complete.


                                        </li>
                                        <li class="mb-1">
                                            You will receive an output containing agenda topics
                                            categorized by country,
                                            year, and frequency trends.


                                        </li>
                                        <li class="mb-1">
                                            Note: Twitter does not support filtering by year.


                                        </li>
                                    </ol>
                                </div>

                            </div>
                        </div>


                        <div class="toast-container position-fixed top-0 end-0 p-3">
                            <div id="success-message" class="toast align-items-center bg-success border-0" role="alert"
                                aria-live="assertive" aria-atomic="true">
                                <div class="d-flex">
                                    <div class="text-bg-success" style="padding: 0.75rem; word-wrap: break-word">
                                        Agenda Source Saved Successfully.
                                    </div>
                                    <button type="button" class="btn-close btn-close-white me-2 m-auto"
                                        data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                            </div>

                            <div id="error-message" class="toast align-items-center bg-danger border-0 fade "
                                role="alert" aria-live="assertive" aria-atomic="true">
                                <div class="d-flex">
                                    <div id="toast-error-text" class="text-bg-danger"
                                        style="padding: 0.75rem; word-wrap: break-word">
                                        An error occurred. Please try again.
                                    </div>
                                    <button type="button" class="btn-close btn-close-white me-2 m-auto"
                                        data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="modal fade" id="editProjectModal" tabindex="-1" aria-labelledby="editProjectModalLabel"
                        aria-hidden="true">
                        <div class="modal-dialog" style="padding: 4.75rem 0">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="editProjectModalLabel">Edit Project</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <form id="edit-project-form">
                                    <div class="modal-body">
                                        <div class="mb-3">
                                            <label for="edit-project-title" class="form-label">Title</label>
                                            <input type="text" class="form-control" id="edit-project-title"
                                                t-att-value="project.title"/>
                                            <div class="invalid-feedback" id="edit-title-error"></div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="edit-project-description" class="form-label">
                                                Description</label>
                                            <textarea class="form-control" id="edit-project-description" rows="3"><t t-esc="project.description" /></textarea>
                                            <div class="invalid-feedback" id="edit-description-error"></div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="submit" class="btn btn-primary" id="edit-project-submit">
                                            <span>Save Changes</span>
                                            <span id="edit-project-spinner" class="spinner-border spinner-border-sm"
                                                role="status" style="display: none;">
                                                <span class="visually-hidden">Loading...</span>
                                            </span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                </main>
            </div>
        </t>
    </template>
</odoo>