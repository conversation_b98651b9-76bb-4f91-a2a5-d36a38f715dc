<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="cep_agenda_generation.output" name="Agenda Generation Output">
        <t t-call="website.layout">
            <t t-set="head">
                <!-- Include required Odoo assets -->
                <t t-call-assets="web.assets_frontend_minimal"/>
                <!-- Include custom assets -->
                <t t-call-assets="cep_agenda_generation.ag_assets"/>
                <t t-call-assets="cep_agenda_generation.ag_results"/>

            </t>
            <t t-set="title"> Agenda Dashboard</t>
            <div id="wrapwrap" class="homepage">


                <aside class="sidebar">
                    <div class="climas-logo">
                        <img src="/cep_agenda_generation/static/src/img/CLIMAS_logo.png" alt="CLIMAS Logo"/>
                    </div>
                    <div class="header">
                        <h4>AGENDA</h4>

                    </div>
                    <div class="divider"></div>
                    <div class="menu">
                        <a href="/cep_agenda_generation" class="menu-item active"><i class="fa fa-home"></i> Home</a>
                        <div class="menu-item-group">
                            <a href="#" class="menu-item has-submenu" id="create-project-menu">
                                <i class="fa fa-plus"></i> Create Agenda <i class="fa fa-chevron-down submenu-icon"></i>
                            </a>
                            <div class="submenu">
                                <a href="/cep_agenda_generation/create_project?type=climate_assembly"
                                    class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Climate Assembly </a>
                                <a href="/cep_agenda_generation/create_project?type=climate_dialogue"
                                    class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Climate Dialogue </a>
                                <a href="/cep_agenda_generation/create_project?type=just_transition_dialogue"
                                    class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Just Transition Dialogue </a>
                            </div>
                        </div>
                        <a href="/cep_agenda_generation/list" class="menu-item"><i class="fa fa-save"></i> Project list</a>
                        <a href="/cep_agenda_generation/project/favorites" class="menu-item"><i class="fa fa-heart"></i>
                            Favorites</a>
                    </div>
                </aside>


                <!-- Button to toggle sidebar for small screens -->
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i id="sidebar-icon" class="fa fa-bars"></i>
                </button>

                <!-- Main Content -->
                <main class="main-content output-main-content">
                    <div class="container-fluid">
                        <div class="row flex-wrap-reverse flex-lg-nowrap">


                            <!-- Contents -->
                            <div class="col-12 col-lg-9">
                                <!-- Header Section -->
                                <div class="row">
                                    <h4 class="heading-3">Project: <t t-esc="project.title"/></h4>
                                    <p>
                                        <t t-esc="project.description"/>
                                    </p>
                                    <hr/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid" id="main-content">
                        <div class="row flex-wrap-reverse flex-lg-nowrap">
                            <!-- Contents -->
                            <div class="col-12 col-lg-9">


                                <!-- Header Section -->
                                <div class="row">
                                    <h3 class="heading-4" id="output-header">Output</h3>
                                </div>
                                <input type="hidden" id="project_id" t-att-value="project.id"/>
                                <input type="hidden" id="youtube_source"
                                    t-att-value="1 if 'https://www.youtube.com' in parsed_sources['social_media'] else 0"/>
                                <input type="hidden" id="project_status" t-att-value="project.status"/>
                                <input type="hidden" id="dilemma_status" t-att-value="project.dilemma_status"/>
                                <input type="hidden" id="agenda_status" t-att-value="project.agenda_status"/>
                                <input type="hidden" id="ngram_status" t-att-value="project.ngram_status"/>
                                <input type="hidden" id="analyzer_status" t-att-value="project.analyzer_status"/>
                                <input type="hidden" id="sentiment_analyzer_status"
                                    t-att-value="project.sentiment_analyzer_status"/>
                                <input type="hidden" id="dilemma_error_message"
                                    t-att-value="project.dilemma_error_message or ''"/>
                                <input type="hidden" id="agenda_error_message"
                                    t-att-value="project.agenda_error_message or ''"/>
                                <input type="hidden" id="ngram_error_message"
                                    t-att-value="project.ngram_error_message or ''"/>
                                <input type="hidden" id="analyzer_error_message"
                                    t-att-value="project.analyzer_error_message or ''"/>
                                <input type="hidden" id="sentiment_analyzer_error_message"
                                    t-att-value="project.sentiment_analyzer_error_message or ''"/>


                                <ul class="nav  flex-lg-row flex-column" id="pills-tab" role="tablist">
                                    <li class="nav-item mb-2 d-block d-lg-none " role="presentation">
                                        <button class="output-tab" id="input-summary-tab" data-bs-toggle="pill"
                                            data-bs-target="#input-summary" type="button" role="tab"
                                            aria-controls="input-summary" aria-selected="true">
                                            Input Summary</button>
                                    </li>
                                    <li class="nav-item mb-2" role="presentation">
                                        <button class="output-tab active" id="pills-dilemmas-tab" data-bs-toggle="pill"
                                            data-bs-target="#pills-dilemmas" type="button" role="tab"
                                            aria-controls="pills-dilemmas" aria-selected="true">
                                            Dilemmas</button>
                                    </li>
                                    <li class="nav-item mb-2" role="presentation">
                                        <button class="output-tab" id="pills-agendas-tab" data-bs-toggle="pill"
                                            data-bs-target="#pills-agendas" type="button" role="tab"
                                            aria-controls="pills-agendas" aria-selected="false">
                                            Agendas</button>
                                    </li>
                                    <li class="nav-item mb-2" role="presentation">
                                        <button class="output-tab" id="pills-frequency-tab" data-bs-toggle="pill"
                                            data-bs-target="#pills-frequency" type="button" role="tab"
                                            aria-controls="pills-frequency" aria-selected="false">Frequency
                                            Analysis</button>
                                    </li>

                                    <li class="nav-item mb-2" role="presentation">
                                        <button class="output-tab" id="pills-sentiment-tab" data-bs-toggle="pill"
                                            data-bs-target="#pills-sentiment" type="button" role="tab"
                                            aria-controls="pills-sentiment" aria-selected="false">Sentiment
                                            Analysis</button>
                                    </li>

                                </ul>
                                <div class="tab-content" id="pills-tabContent">
                                    <div class="tab-pane fade show active" id="pills-dilemmas" role="tabpanel"
                                        aria-labelledby="pills-dilemmas-tab" tabindex="0">

                                        <div id="dilemmas" class="row mt-5">
                                            <div class="col-12">
                                                <div id="dilemma-container">
                                                    <h5 class="header">List of Dilemmas
                                                    </h5>
                                                    <ol class="list-group list-group-numbered" id="dilemmas-list">


                                                    </ol>

                                                </div>
                                            </div>


                                            <div class="col-12 mt-3 d-none" id="dilemma_priritise">
                                                <p class="text-dark">Do you want to prioritise the
                                                    list of
                                                    dilemmas under
                                                    criteria?</p>
                                                <!-- Proceed Button -->
                                                <button type="submit" class="btn btn-secondary" data-bs-toggle="modal"
                                                    data-bs-target="#prioritize-modal">
                                                    Prioritise
                                                </button>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="tab-pane fade" id="pills-agendas" role="tabpanel"
                                        aria-labelledby="pills-agendas-tab" tabindex="0">

                                        <div id="agendas" class="row mt-4">


                                            <div id="agenda-container">
                                                <h5 class="header">List of Agendas 

                                                </h5>
                                                <div class="accordion" id="agendaAccordion">

                                                </div>
                                            </div>
                                        </div>


                                    </div>
                                    <div class="tab-pane fade" id="pills-frequency" role="tabpanel"
                                        aria-labelledby="pills-frequency-tab" tabindex="0">


                                        <!-- N-Gram Analysis Section -->
                                        <div id="words_frequency" class="row mt-4">
                                            <h5 class="header">Most common words frequency <div class="js-tooltip">
                                                    <span class="info-symbol ms-1">
                                                        <i class="fa fa-info"></i>
                                                    </span>
                                                    <div class="tooltip-content">Analysis of the most frequently occurring words and phrases in your selected data sources. This helps identify key themes and topics being discussed in climate-related content.</div>
                                                </div>


                                            </h5>
                                            <div class="col-12">
                                                <div id="ngram-container">

                                                </div>
                                            </div>
                                        </div>

                                        <!-- Keyword Frequency Section -->
                                        <div id="trends" class="row mt-3">
                                            <h5 class="header">Keyword Frequency Over Years <div class="js-tooltip">
                                                    <span class="info-symbol ms-1">
                                                        <i class="fa fa-info"></i>
                                                    </span>
                                                    <div class="tooltip-content">Visual representation showing how frequently your input keywords appear in the data sources over your specified time period. This helps track trends and changes in climate discourse over time.</div>
                                                </div>
                                            </h5>
                                            <div class="col-12">
                                                <div id="frequency-container">

                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="tab-pane fade" id="pills-sentiment" role="tabpanel"
                                        aria-labelledby="pills-sentiment-tab" tabindex="0">
                                        <section id="trends" class="row mt-3">
                                            <h5 class="header">Sentiment analysis <div class="js-tooltip">
                                                    <span class="info-symbol ms-1">
                                                        <i class="fa fa-info"></i>
                                                    </span>
                                                    <div class="tooltip-content">Analysis of emotional tone and attitudes expressed in climate-related content from your data sources. Shows whether discussions are positive, negative, or neutral, helping understand public sentiment toward climate issues.</div>
                                                </div>
                                            </h5>
                                            <div class="col-12">
                                                <t t-if="'https://www.youtube.com' in parsed_sources['social_media']">
                                                    <div id="sentiment-container">

                                                    </div>
                                                </t>
                                                <t t-else="">
                                                    <div class="alert alert-warning">
                                                        Cannot get sentiment analysis - YouTube
                                                        source not selected
                                                    </div>
                                                </t>
                                            </div>
                                        </section>
                                    </div>
                                    <div class="tab-pane fade" id="input-summary" role="tabpanel"
                                        aria-labelledby="input-summary-tab" tabindex="0">
                                        <div class="input-summary-card mt-3">

                                            <h4 class="card-title">Input Summary <div class="js-tooltip">
                                                    <span class="info-symbol ms-1">
                                                        <i class="fa fa-info"></i>
                                                    </span>
                                                    <div class="tooltip-content">Summary of all your project inputs including keywords, time period, selected data sources, and configuration settings. This provides a complete overview of your analysis parameters.</div>
                                                </div>

                                            </h4>


                                            <!-- Keywords Section -->
                                            <div class="summary-section">
                                                <div class="section-header">
                                                    <i class="fa fa-key section-icon"></i>
                                                    <span class="section-title">Keywords</span>
                                                </div>
                                                <div class="keywords-container">
                                                    <t t-if="project.keywords">
                                                        <t t-foreach="project.keywords.split(',')" t-as="keyword">
                                                            <span class="keyword-tag" t-esc="keyword.strip()"></span>
                                                        </t>
                                                    </t>
                                                </div>
                                            </div>

                                            <!-- Date Range Section -->
                                            <div class="summary-section">
                                                <div class="section-header">
                                                    <i class="fa fa-calendar section-icon"></i>
                                                    <span class="section-title">Date Range</span>
                                                </div>
                                                <div class="date-range">
                                                    <span class="date-value" t-esc="project.start_year or '2000'"></span>
                                                    <span class="date-separator">to</span>
                                                    <span class="date-value" t-esc="project.end_year or '2005'"></span>
                                                </div>
                                            </div>

                                            <!-- Print Media Section -->
                                            <div class="summary-section">
                                                <div class="section-header">
                                                    <i class="fa fa-newspaper-o section-icon"></i>
                                                    <span class="section-title">Print Media</span>
                                                </div>
                                                <div class="media-list">
                                                    <t t-if="parsed_sources['print_media']">
                                                        <div class="media-item">
                                                            <t t-foreach="parsed_sources['print_media']" t-as="url">
                                                                <span class="print-media-tag" t-esc="url"></span>
                                                            </t>
                                                        </div>
                                                    </t>
                                                    <t t-if="not parsed_sources['print_media']">
                                                        <div class="media-item">No print media sources</div>
                                                    </t>
                                                </div>
                                            </div>

                                            <!-- Social Media Section -->
                                            <div class="summary-section">
                                                <div class="section-header">
                                                    <i class="fa fa-share-alt section-icon"></i>
                                                    <span class="section-title">Social Media</span>
                                                </div>
                                                <div class="media-list">
                                                    <t t-if="parsed_sources['social_media']">
                                                        <div class="media-item">
                                                            <t t-foreach="parsed_sources['social_media']" t-as="url">
                                                                <span class="social-media-tag" t-esc="url"></span>
                                                            </t>
                                                        </div>
                                                    </t>
                                                    <t t-if="not parsed_sources['social_media']">
                                                        <div class="media-item">No social media sources</div>
                                                    </t>
                                                </div>
                                            </div>

                                            <!-- Uploaded PDFs Section -->
                                            <div class="summary-section">
                                                <div class="section-header">
                                                    <i class="fa fa-file-pdf-o section-icon"></i>
                                                    <span class="section-title">Uploaded PDFs</span>
                                                </div>
                                                <div class="pdf-list">
                                                    <t t-if="parsed_sources['pdf_files']">
                                                        <div class="pdf-item">
                                                            <t t-foreach="parsed_sources['pdf_files']" t-as="pdf">
                                                                <div class="pdf-single-item">
                                                                    <i class="fa fa-file-pdf-o pdf-icon"></i>
                                                                    <span class="pdf-name ms-1" t-esc="pdf['name']"></span>
                                                                </div>
                                                            </t>
                                                        </div>
                                                    </t>
                                                    <t t-if="not parsed_sources['pdf_files']">
                                                        <div class="pdf-item">
                                                            <i class="fa fa-file-pdf-o pdf-icon"></i>
                                                            <span class="pdf-name">No PDF files uploaded</span>
                                                        </div>
                                                    </t>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>


                            </div>


                            <!-- Sidebar Section -->
                            <div class="col-12 col-lg-3 sidebar-container shadow px-3 py-4 d-none d-lg-block">

                                <!-- Input Summary Card -->
                                <div class="input-summary-card">

                                    <h4 class="card-title" data-tooltip="Summary of all your project inputs including keywords, time period, selected data sources, and configuration settings. This provides a complete overview of your analysis parameters.">Input Summary <div class="js-tooltip">
                                            <span class="info-symbol ms-1">
                                                <i class="fa fa-info"></i>
                                            </span>
                                            <div class="tooltip-conten"></div>
                                        </div>
                                    </h4>


                                    <!-- Keywords Section -->
                                    <div class="summary-section">
                                        <div class="section-header">
                                            <i class="fa fa-key section-icon"></i>
                                            <span class="section-title">Keywords</span>
                                        </div>
                                        <div class="keywords-container">
                                            <t t-if="project.keywords">
                                                <t t-foreach="project.keywords.split(',')" t-as="keyword">
                                                    <span class="keyword-tag" t-esc="keyword.strip()"></span>
                                                </t>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Date Range Section -->
                                    <div class="summary-section">
                                        <div class="section-header">
                                            <i class="fa fa-calendar section-icon"></i>
                                            <span class="section-title">Date Range</span>
                                        </div>
                                        <div class="date-range">
                                            <span class="date-value" t-esc="project.start_year or '2000'"></span>
                                            <span class="date-separator">to</span>
                                            <span class="date-value" t-esc="project.end_year or '2005'"></span>
                                        </div>
                                    </div>

                                    <!-- Print Media Section -->
                                    <div class="summary-section">
                                        <div class="section-header">
                                            <i class="fa fa-newspaper-o section-icon"></i>
                                            <span class="section-title">Print Media</span>
                                        </div>
                                        <div class="media-list">
                                            <t t-if="parsed_sources['print_media']">
                                                <div class="media-item">
                                                    <t t-foreach="parsed_sources['print_media']" t-as="url">
                                                        <span class="print-media-tag" t-esc="url"></span>
                                                    </t>
                                                </div>
                                            </t>
                                            <t t-if="not parsed_sources['print_media']">
                                                <div class="media-item">No print media sources</div>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Social Media Section -->
                                    <div class="summary-section">
                                        <div class="section-header">
                                            <i class="fa fa-share-alt section-icon"></i>
                                            <span class="section-title">Social Media</span>
                                        </div>
                                        <div class="media-list">
                                            <t t-if="parsed_sources['social_media']">
                                                <div class="media-item">
                                                    <t t-foreach="parsed_sources['social_media']" t-as="url">
                                                        <span class="social-media-tag" t-esc="url"></span>
                                                    </t>
                                                </div>
                                            </t>
                                            <t t-if="not parsed_sources['social_media']">
                                                <div class="media-item">No social media sources</div>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Uploaded PDFs Section -->
                                    <div class="summary-section">
                                        <div class="section-header">
                                            <i class="fa fa-file-pdf-o section-icon"></i>
                                            <span class="section-title">Uploaded PDFs</span>
                                        </div>
                                        <div class="pdf-list">
                                            <t t-if="parsed_sources['pdf_files']">
                                                <div class="pdf-item">
                                                    <t t-foreach="parsed_sources['pdf_files']" t-as="pdf">
                                                        <div class="pdf-single-item">
                                                            <i class="fa fa-file-pdf-o pdf-icon"></i>
                                                            <span class="pdf-name ms-1" t-esc="pdf['name']"></span>
                                                        </div>
                                                    </t>
                                                </div>
                                            </t>
                                            <t t-if="not parsed_sources['pdf_files']">
                                                <div class="pdf-item">
                                                    <i class="fa fa-file-pdf-o pdf-icon"></i>
                                                    <span class="pdf-name">No PDF files uploaded</span>
                                                </div>
                                            </t>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                </main>
            </div>

        </t>

        <div class="modal fade" id="prioritize-modal" tabindex="-1" aria-labelledby="prioritize-modal-label"
            aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5">Prioritise</h1>
                        <button type="button" class="close border-0" data-bs-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>

                    <div class="modal-body">
                        <div class="container">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="row ">
                                        <label class="criteria-header">Criterion</label>
                                        <div class="d-flex flex-column flex-lg-row gap-3">
                                            <input type="text" class="form-control" value="Environmental Impact"
                                                readonly="readonly"/>
                                            <div class="options d-flex flex-column flex-lg-row gap-2">
                                                <input type="number" id="criteria1"
                                                    class="form-control criteria-weight-input"
                                                    placeholder="Enter weight"
                                                    t-att-value="criteria_weights and criteria_weights.get('env', '')"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mt-3">
                                        <label class="criteria-header">Criterion</label>
                                        <div class="d-flex flex-column flex-lg-row gap-3">
                                            <input type="text" class="form-control" value="Economic Factors"
                                                readonly="readonly"/>
                                            <div class="options d-flex flex-column flex-lg-row gap-2">
                                                <input type="number" id="criteria2"
                                                    class="form-control criteria-weight-input"
                                                    placeholder="Enter weight"
                                                    t-att-value="criteria_weights and criteria_weights.get('eco', '')"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mt-3">
                                        <label class="criteria-header">Criterion</label>
                                        <div class="d-flex flex-column flex-lg-row gap-3">
                                            <input type="text" class="form-control" value="Social Equity"
                                                readonly="readonly"/>
                                            <div class="options d-flex flex-column flex-lg-row gap-2">
                                                <input type="number" id="criteria3"
                                                    class="form-control criteria-weight-input"
                                                    placeholder="Enter weight"
                                                    t-att-value="criteria_weights and criteria_weights.get('soc', '')"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mt-3">
                                        <label for="criteria4" class="criteria-header">Criterion</label>
                                        <div class="d-flex flex-column flex-lg-row gap-3">
                                            <input type="text" class="form-control" value="Technology &amp; Innovation"
                                                readonly="readonly"/>
                                            <div class="options d-flex flex-column flex-lg-row gap-2">
                                                <input type="number" id="criteria4"
                                                    class="form-control criteria-weight-input"
                                                    placeholder="Enter weight"
                                                    t-att-value="criteria_weights and criteria_weights.get('tech', '')"/>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <div class="col-md-4">

                                    <div class="card instruction-box p-3 mt-3"
                                        style="background-color: #e7f1ff; 
            border: 2px solid #e7f1ff; 
            border-radius: 5px;">
                                        <h5 class="card-title">Instructions</h5>
                                        <ol>

                                            <li class="mb-1">
                                                Prioritise according to your criteria.


                                            </li>
                                            <li class="mb-1">
                                                <strong>0.9</strong> represents the <strong>maximum
                                                    impact</strong> that should be Prioritised, while <strong>
                                                    0.1</strong> indicates <strong>minimum
                                                    impact</strong> according to the criteria. </li>
                                            <li class="mb-1">
                                                <strong> Note: The sum of all the scores of criteria
                                                    must equal 1.</strong>

                                            </li>

                                        </ol>
                                    </div>

                                </div>
                            </div>


                        </div>

                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary text-white" id="submit-prioritize">Output</button>
                    </div>
                </div>
            </div>
        </div>


        <!-- Hidden button to open the dilemma modal programmatically -->
        <button type="button" id="open-dilemma-modal" class="d-none" data-bs-toggle="modal"
            data-bs-target="#dilemma-modal"></button>

        <div class="modal fade" id="dilemma-modal" tabindex="-1" aria-labelledby="prioritize-modal-label"
            aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5">title</h1>
                        <button type="button" class="close border-0" data-bs-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>

                    <div class="modal-body">
                        description and questions
                    </div>

                </div>
            </div>
        </div>

    </template>
</odoo>