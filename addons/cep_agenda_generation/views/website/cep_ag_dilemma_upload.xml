<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="cep_ag_dilemma_upload" name="Upload Dilemmas">
        <t t-call="website.layout">
            <t t-set="head">
                <!-- Include custom assets -->
                <t t-call-assets="web.assets_common"/>
                <t t-call-assets="cep_agenda_generation.ag_assets"/>
            </t>

            <div class="container mt-4">
                <div class="row">
                    <div class="col-12">
                        <h2>Upload Dilemmas</h2>
                        <div class="card">
                            <div class="card-body">
                                <form id="dilemma-upload-form" class="mt-3">
                                  
                                    
                                    <div class="mb-3">
                                        <label for="excelFile" class="form-label">Upload Excel File</label>
                                        <input type="file" class="form-control" id="excelFile" name="file" accept=".csv"/>
                                        <small class="text-muted">Please upload an Excel file with the following columns: Dilemma ID, Title, Description, Questions, ENV, ECO, SOC, TECH</small>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">Upload Dilemmas</button>
                                </form>
                                
                                <!-- Success/Error Messages -->
                                <div id="upload-messages" class="mt-3"></div>
                                
                                <!-- Loading Spinner -->
                                <div id="loading-spinner" class="text-center d-none">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>