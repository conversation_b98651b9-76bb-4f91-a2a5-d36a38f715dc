<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="cep_agenda_generation.ag_create_project" name="Agenda Generation Create Project">
        <t t-call="website.layout">
            <t t-set="head">
                <!-- Include custom assets -->
                <t t-call-assets="web.assets_frontend_minimal"/>
                <t t-call-assets="cep_agenda_generation.ag_assets"/>
            </t>

            <t t-set="title"> Agenda Dashboard</t>
            <div id="wrapwrap" class="homepage">

                <aside class="sidebar">
                    <div class="climas-logo">
                        <img src="/cep_agenda_generation/static/src/img/CLIMAS_logo.png" alt="CLIMAS Logo"/>
                    </div>
                    <div class="header">
                        <h4>AGENDA</h4>
                        
                    </div>
                    <div class="divider"></div>
                    <div class="menu">
                        <a href="/cep_agenda_generation" class="menu-item "><i class="fa fa-home"></i> Home</a>
                        <div class="menu-item-group">
                            <a href="#" class="menu-item has-submenu" id="create-project-menu">
                                <i class="fa fa-plus"></i> Create Agenda
                                <i class="fa fa-chevron-down submenu-icon"></i>
                            </a>
                            <div class="submenu">
                                <a href="/cep_agenda_generation/create_project?type=climate_assembly" class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Climate Assembly
                                </a>
                                <a href="/cep_agenda_generation/create_project?type=climate_dialogue" class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Climate Dialogue
                                </a>
                                <a href="/cep_agenda_generation/create_project?type=just_transition_dialogue" class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Just Transition Dialogue
                                </a>
                            </div>
                        </div>
                        <a href="/cep_agenda_generation/list" class="menu-item"><i class="fa fa-save"></i>
                            Project list</a>
                          <a href="/cep_agenda_generation/project/favorites" class="menu-item"><i class="fa fa-heart"></i> Favorites</a>
                    </div>
                </aside>
                <!-- Button to toggle sidebar for small screens -->
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i id="sidebar-icon" class="fa fa-bars"></i>
                </button>

                <!-- Main Content -->
                <main class="main-content new-project-main-content">
                    <h4 class="heading-4">Create Agenda for <t t-esc="request.params.get('type', '').replace('_', ' ').title()"/></h4>
                    <div class="container-fluid mt-4">

                        <div class="row">
                            <form t-att-action="'create'" role='form' method="post"
                                class="col-12 col-lg-6 d-flex flex-column gap-4">
                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                <input type="hidden" name="category" t-att-value="request.params.get('type', 'climate_assembly')"/>

                                <!-- Project Title -->
                                <div class="d-flex flex-column gap-2">
                                    <label for="title">
                                        Project Title 
                                        <div class="js-tooltip">
                                                <span class="info-symbol ms-1"><i class="fa fa-info"></i></span>
                                                <div class="tooltip-content">Choose a descriptive title for your climate agenda analysis project. This will help you identify and organize your projects in the project list.</div>
                                        </div>

                                        
                                    </label>
                                    <input name="title" id="title" type="text" class="search-box"
                                        placeholder="Project Title" required="required" t-att-value="request.params.get('title', '')"/>
                                    <t t-if="error and error.get('title')">
                                        <div class="text-danger mt-1" t-esc="error.get('title')"/>
                                    </t>
                                </div>
                                <!-- Project Description -->
                                <div class="d-flex flex-column gap-2">
                                    <label for="description">
                                        Project Description
                                        <div class="js-tooltip">
                                                <span class="info-symbol ms-1"><i class="fa fa-info"></i></span>
                                                <div class="tooltip-content">Provide a detailed description of your project's purpose and objectives. This helps you track the project's goals but does not affect the analysis results.</div>
                                        </div>
                                    </label>
                                    <textarea name="description" id="description" class="search-box"
                                        placeholder="Project Description" required="required"><t t-esc="request.params.get('description', '')"/></textarea>
                                    <t t-if="error and error.get('description')">
                                        <div class="text-danger mt-1" t-esc="error.get('description')"/>
                                    </t>
                                </div>
                                <!-- submit button -->
                                <button type="submit" class="save-btn text-white">Submit</button>
                            </form>
                        </div>
                    </div>
                </main>
            </div>
        </t>
    </template>
</odoo>