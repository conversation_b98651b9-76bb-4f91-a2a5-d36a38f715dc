<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_cep_ag_dilemma_tree" model="ir.ui.view">
        <field name="name">cep.ag.dilemma.tree</field>
        <field name="model">cep.ag.dilemma</field>
        <field name="arch" type="xml">
            <tree>
                <field name="dilemma_serial_id"/>
                <field name="title"/>
                <field name="environment"/>
                <field name="eco"/>
                <field name="soc"/>
                <field name="tech"/>
                <field name="questions"/>

            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_ag_dilemma_form" model="ir.ui.view">
        <field name="name">cep.ag.dilemma.form</field>
        <field name="model">cep.ag.dilemma</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="dilemma_serial_id"/>
                            <field name="title"/>
                        </group>
                        <group>
                            <field name="environment"/>
                            <field name="eco"/>
                            <field name="soc"/>
                            <field name="tech"/>
                        </group>
                    </group>
                    <group>
                        <field name="description"/>
                        <field name="questions"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_cep_ag_dilemma_search" model="ir.ui.view">
        <field name="name">cep.ag.dilemma.search</field>
        <field name="model">cep.ag.dilemma</field>
        <field name="arch" type="xml">
            <search>
                <field name="dilemma_serial_id"/>
                <field name="title"/>
                <field name="questions"/>
                <!-- Group by section removed as project_id field is not defined in the model -->
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_cep_ag_dilemma" model="ir.actions.act_window">
        <field name="name">Dilemmas</field>
        <field name="res_model">cep.ag.dilemma</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first dilemma
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_cep_ag_dilemma"
              name="All Dilemmas"
              parent="cep_ag_root"
              action="action_cep_ag_dilemma"
              sequence="20"/>
</odoo>