<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_ag_agenda_source_search" model="ir.ui.view">
        <field name="name">cep.ag.agenda_source</field>
        <field name="model">cep.ag.agenda_source</field>
        <field name="arch" type="xml">
            <search string="CEP Agenda Source">
                <field name="type"/>
            </search>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_ag_agenda_source_form" model="ir.ui.view">
        <field name="name">cep.ag.agenda_source.form</field>
        <field name="model">cep.ag.agenda_source</field>
        <field name="arch" type="xml">
            <form string="CEP Agenda Source">
                <sheet>
                    <group>
                        <field name="project_id"/>
                        <field name="type"/>
                        <field name="urls"/>
                    </group>
                    <notebook>
                        <page name="attachments" string="Attachment">

                            <field name="attachment_ids" widget="one2many_list"
                                options="{'no_create': True, 'no_open': True}" readonly="1">
                                <tree editable="top">
                                    <field name="name"/>
                                    <field name="create_date"/>
                                </tree>
                                <form>

                                    <sheet>
                                        <group>
                                            <field name="type"/>
                                            <field name="datas" filename="name"
                                                attrs="{'invisible':[('type','=','url')]}"/>
                                            <field name="url" widget="url" attrs="{'invisible':[('type','=','binary')]}"/>
                                            <field name="mimetype" groups="base.group_no_one"/>
                                        </group>

                                    </sheet>

                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>


    <!-- List (Tree) View -->
    <record id="view_cep_ag_agenda_source_tree" model="ir.ui.view">
        <field name="name">cep.ag.agenda_source.tree</field>
        <field name="model">cep.ag.agenda_source</field>
        <field name="arch" type="xml">
            <tree>
                <field name="project_id"/>
                <field name="type"/>
                <field name="urls"/>
                <field name="attachment_ids"/>
            </tree>
        </field>
    </record>


    <!-- action -->
    <record id='cep_ag_agenda_source_action' model='ir.actions.act_window'>
        <field name="name">Agenda Source</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.ag.agenda_source</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new Agenda Source
            </p>
        </field>
    </record>


    <menuitem id="cep_ag_agenda_source" parent="cep_ag_root" name="Agenda Source" action="cep_ag_agenda_source_action"/>

</odoo>