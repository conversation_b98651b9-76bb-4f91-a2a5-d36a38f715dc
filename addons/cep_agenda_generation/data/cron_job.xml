<?xml version="1.0"?>
<odoo>
    <data noupdate="1">
        <!-- Define the cron job -->
        <record id="ir_cron_server_start" model="ir.cron">
            <field name="name">Run on Server Start</field>
            <field name="model_id" ref="model_cep_ag_result"/>
            <field name="state">code</field>
            <field name="code">model.start_consumer()</field>
            <field name="active">True</field>
            <field name="interval_type">minutes</field>
            <field name="interval_number">1</field> <!-- Set an interval, but we'll trigger it once immediately -->
            <field name="numbercall">1</field> <!-- Ensure it runs only once -->
            <field name="nextcall" eval="(datetime.utcnow()).strftime('%Y-%m-%d %H:%M:%S')"/> <!-- This sets the nextcall to the current time, so it runs immediately -->
        </record>
    </data>
</odoo>
