from odoo import models, fields, api


class Dilemma(models.Model):
    _name = 'cep.ag.dilemma'
    _description = 'CEP Agenda Generation Dilemma'

    dilemma_serial_id = fields.Integer('dilemma_serial_id', required=True)
    title = fields.Char('Title', required=True)
    description = fields.Text('Description')
    questions = fields.Text('Questions')
    environment = fields.Float('ENV Score')
    eco = fields.Float('ECO Score')
    soc = fields.Float('SOC Score')
    tech = fields.Float('TECH Score')