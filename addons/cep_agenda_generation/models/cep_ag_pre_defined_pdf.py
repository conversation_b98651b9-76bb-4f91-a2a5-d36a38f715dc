from odoo import fields, models, api
from odoo.exceptions import ValidationError

class PreDefinedPdf(models.Model):
    _name = 'cep.ag.pre_defined_pdf'
    _description = 'Predefined PDF'

    name = fields.Char(string='File Name', required=True)
    description = fields.Text(string='Description', help="Brief description of the PDF content")
    attachment = fields.Binary(string='PDF File', attachment=True)
    attachment_id = fields.Many2one('ir.attachment', domain=[('res_model', '=', 'cep.ag.pre_defined_pdf')], string='Attachment Reference', readonly=True)

    @api.model
    def create(self, vals):
        record = super(PreDefinedPdf, self).create(vals)
        if vals.get('attachment'):
            record._create_attachment()
        return record

    def write(self, vals):
        res = super(PreDefinedPdf, self).write(vals)
        if vals.get('attachment'):
            self._update_attachment()
        return res

    def _create_attachment(self):
        self.ensure_one()
        attachment = self.env['ir.attachment'].create({
            'name': self.name,
            'datas': self.attachment,
            'res_model': self._name,
            'res_id': self.id,
            'type': 'binary',
            'public': True,
        })
        self.attachment_id = attachment.id

    def _update_attachment(self):
        self.ensure_one()
        if self.attachment_id:
            self.attachment_id.write({
                'name': self.name,
                'datas': self.attachment,
            })
        else:
            self._create_attachment()

    def unlink(self):
        # Delete related attachments when deleting PDF records
        self.attachment_id.unlink()
        return super(PreDefinedPdf, self).unlink()