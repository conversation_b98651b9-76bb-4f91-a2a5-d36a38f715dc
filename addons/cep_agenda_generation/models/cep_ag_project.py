from odoo import models, fields, api
from datetime import datetime, timedelta
import logging
import json
import threading
_logger = logging.getLogger(__name__)

class Projects(models.Model):
    _name = 'cep.ag.project'
    _description = 'Project'
    _rec_name = 'title'

    title = fields.Char(string='Title', required=True)
    description = fields.Text(string='Description')
    keywords = fields.Char(string='Keywords', )
    owner_id = fields.Many2one('res.users', string='Owner', index=True, tracking=True, default=lambda self: self.env.user)
    target_country = fields.Char(string='Country', )
    start_year = fields.Char(string='Start Year', )
    end_year = fields.Char(string='End Year', )
    with_ontology = fields.Boolean(string='With Ontology', default=False)
    use_ontology = fields.Boolean(string='Use Ontology', default=False)
    favorite = fields.Boolean(string='Favorite', )
    category = fields.Selection([
        ('climate_assembly', 'Climate Assembly'),
        ('climate_dialogue', 'Climate Dialogue'),
        ('just_transition_dialogue', 'Just Transition Dialogue')
    ], string='Category', required=True, default='climate_assembly')
    agenda_source_ids = fields.One2many('cep.ag.agenda_source', 'project_id', string='Sources')
    result_ids = fields.One2many('cep.ag.result', 'project_id', string='Results')
    criteria_weights = fields.Text(string='Criteria Weights', default="{}" )
  
    status = fields.Selection([
        ('draft', 'Draft'),
        ('dilemma_in_progress', 'Dilemma In Progress'),
        ('agenda_in_progress', 'Agenda In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ], string='Status', default='draft')
    dilemma_status = fields.Selection([
        ('idle', 'Idle'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ], string='Dilemma Status', default='idle')
    analyzer_status = fields.Selection([
        ('idle', 'Idle'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ], string='Analyzer Status', default='idle')
    sentiment_analyzer_status = fields.Selection([
        ('idle', 'Idle'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ], string='Sentiment Analyzer Status', default='idle')
    agenda_status = fields.Selection([
        ('idle', 'Idle'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ], string='Agenda Status', default='idle')
    ngram_status = fields.Selection([
        ('idle', 'Idle'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ], string='N-Gram Status', default='idle')
    dilemma_error_message =  fields.Char(string='Dilemma Error Message', )
    ngram_error_message =  fields.Char(string='N-Gram Error Message', )
    analyzer_error_message =  fields.Char(string='Frequency Analyzer Error Message', )
    sentiment_analyzer_error_message =  fields.Char(string='Sentiment Analyzer Error Message', )
    agenda_error_message =  fields.Char(string='Agenda Error Message', )
    
    
    def save_failed_message(self, body): 
       
        project_id = body.get('project_id')
        status = body.get('status')
        message = body.get('message')
        type = body.get('type')
        project = self.env['cep.ag.project'].browse(int(project_id)).sudo()

        if type == 'dilemma':
            project.write({'dilemma_status': status, 'dilemma_error_message': message,  "status": status, })
        elif type == 'agenda':
            project.write({'agenda_status': status, 'agenda_error_message': message, 'status': status})
        elif type == 'ngram':
            project.write({'ngram_status': status, 'ngram_error_message': message})
        elif type == 'frequency_analysis':
            project.write({'analyzer_status': status, 'analyzer_error_message': message})
        elif type == 'sentiment_analysis':
            project.write({'sentiment_analyzer_status': status, 'sentiment_analyzer_error_message': message})
       

    @api.model
    def create(self, vals):
       
        if 'criteria_weights' in vals and isinstance(vals['criteria_weights'], dict):
            vals['criteria_weights'] = json.dumps(vals['criteria_weights']) 
        return super(Projects, self).create(vals)

    def write(self, vals):
        if 'criteria_weights' in vals and isinstance(vals['criteria_weights'], dict):
            vals['criteria_weights'] = json.dumps(vals['criteria_weights']) 
        return super(Projects, self).write(vals)
           