from odoo import http
from odoo.http import request,content_disposition
import json
import base64
import logging

_logger = logging.getLogger(__name__)


def isAuthenticate():
    return request.env.user.id != request.website.user_id


def isAgProject(owner_id):
    return owner_id.id == request.env.user.id


class AGController(http.Controller):

    _root_url = '/cep_agenda_generation'

    @http.route([f'{_root_url}/get_frequency'], type='http', auth='user', website=True, csrf=False)
    def get_frequency(self, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        
        try:
            data = json.loads(request.httprequest.data)
            project_id = data.get('project_id')
          
            if not project_id:
                return json.dumps({
                    'status': False,
                    'message': 'Project ID is required'
                })
            project = request.env['cep.ag.project'].browse(int(project_id)).sudo()
            project.write({'analyzer_status': 'running'})

            self.send_mq_data(int(project_id), event="ANALYZE_KEYWORD_FREQUENCY")
            
            return json.dumps({
                'status': True,
                'message': 'Frequency analysis request sent successfully'
            })
            
        except Exception as e:
            return json.dumps({
                'status': False,
                'message': str(e)
            })
            
    @http.route([f'{_root_url}/get_sentiment'], type='http', auth='user', website=True, csrf=False)
    def get_sentiment(self, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
            
        try:
            data = json.loads(request.httprequest.data)
            project_id = data.get('project_id')
            
            if not project_id:
                return json.dumps({
                    'status': False,
                    'message': 'Project ID is required'
                })
                
            project = request.env['cep.ag.project'].browse(int(project_id)).sudo()
            project.write({'sentiment_analyzer_status': 'running'})
            
            # Send MQ data for sentiment analysis
            self.send_mq_data(int(project_id),  event="ANALYZE_SENTIMENT")
            
            return json.dumps({
                'status': True,
                'message': 'Sentiment analysis request sent successfully'
            })
            
        except Exception as e:
            return json.dumps({
                'status': False,
                'message': str(e)
            })
            
   
    @http.route([f'{_root_url}/keyword/save'], type='http', auth='user', website=True, csrf=False)
    def keyword_create(self, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        
         # Read and parse the JSON body
        try:
            data = json.loads(request.httprequest.data)
        except Exception as e:
            return json.dumps({
                    'status': False,
                    'message': 'Invalid JSON payload'
                })
     

        id = data.get('project_id')
        
        printed_media_source_id = data.get('printed_media_source_id')
        social_media_source_id = data.get('social_media_source_id')
        
        keywords = data.get('keywords')
        type = data.get('type')
        urls = data.get('urls')
        
        source_id = None

        if printed_media_source_id:
            source_id = printed_media_source_id
        elif social_media_source_id:
            source_id = social_media_source_id

        if not id : 
            return json.dumps({
                'status': False,
                'message': 'Project ID is required'
            })
        
        if source_id: 
            source = request.env['cep.ag.agenda_source'].browse(int(source_id)).sudo()
            source.write({
                'urls': urls
            })
        else:
            request.env['cep.ag.agenda_source'].sudo().create({
                'project_id': int(id),
                'type': type,
                'urls': urls
            })
        if keywords:
            project = request.env['cep.ag.project'].browse(int(id)).sudo()
            project.write({'keywords': keywords})


        return json.dumps({
            'status': True,
            'message': 'Agenda data saved successfully'
        })
   
       
    @http.route([f'{_root_url}/process_data'], type='http', auth='user', website=True, csrf=False)
    def process_data(self, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        project_id = kwargs.get('project_id')
        keywords = kwargs.get('keywords')
        attachments = request.httprequest.files.getlist('files[]')
        target_country = kwargs.get('target_country')
        start_year = kwargs.get('start_year')
        end_year = kwargs.get('end_year')
        use_ontology = kwargs.get('use_ontology', False)
        printed_media_source_id = kwargs.get('printed_media_source_id', None)
        social_media_source_id = kwargs.get('social_media_source_id', None)
        pdf_source_id = kwargs.get('pdf_source_id', None)
        selected_social_media = kwargs.get('selected_social_media')
        selected_print_media = kwargs.get('selected_print_media')
        
        # Handle predefined PDFs
        predefined_pdf_ids = []
        if kwargs.get('predefined_pdf_ids'):
            try:
                predefined_pdf_ids = json.loads(kwargs.get('predefined_pdf_ids'))
            except:
                _logger.error("Failed to parse predefined PDF IDs")
        
        # Create or update PDF source if we have attachments or predefined PDFs
        has_pdf_content = len(attachments) > 0 or predefined_pdf_ids
        
        if has_pdf_content:
            if pdf_source_id: 
                pdf_source = request.env['cep.ag.agenda_source'].browse(int(pdf_source_id)).sudo()
            else:
                pdf_source = request.env['cep.ag.agenda_source'].sudo().create({
                    'project_id': int(project_id),
                    'type': 'pdf',
                })
                
            attachment_ids = []
            
            for attachment in attachments:
                attachment_data = {
                    'name': attachment.filename,
                    'datas': base64.b64encode(attachment.read()),
                    'res_model': 'cep.ag.agenda_source',
                    'res_id': pdf_source.id,
                    'public': True,
                }
                attachment_id = request.env['ir.attachment'].sudo().create(attachment_data)
                attachment_ids.append(attachment_id.id)

            if attachment_ids:
                pdf_source.sudo().write({'attachment_ids': [(6, 0, attachment_ids)]})
            
            # Add predefined PDFs if any
            if predefined_pdf_ids:
                pdf_source.sudo().write({'pre_defined_pdf_ids': [(6, 0, predefined_pdf_ids)]})


        
        if len(selected_social_media) > 0:

            if social_media_source_id: 
                social_media_source = request.env['cep.ag.agenda_source'].browse(int(social_media_source_id)).sudo()
                social_media_source.write({
                      'urls': selected_social_media
                })
            else:
                social_media_source = request.env['cep.ag.agenda_source'].sudo().create({
                    'project_id': int(project_id),
                    'type': 'social_media',
                    'urls': selected_social_media
                })

        if len(selected_print_media) > 0:

            if printed_media_source_id: 
                printed_media_source = request.env['cep.ag.agenda_source'].browse(int(printed_media_source_id)).sudo()
                printed_media_source.write({
                      'urls': selected_print_media
                })
            else:
                printed_media_source = request.env['cep.ag.agenda_source'].sudo().create({
                    'project_id': int(project_id),
                    'type': 'printed_media',
                    'urls': selected_print_media
                })


        if start_year and end_year:
            request.env['cep.ag.result'].delete_records_by_project( project_id=int(project_id))           
            project = request.env['cep.ag.project'].browse(int(project_id)).sudo()
            project.write({
                'target_country': target_country, 
                'start_year': start_year,
                'end_year': end_year,
                'status': 'dilemma_in_progress',
                'keywords': keywords,
                'with_ontology': False,
                'dilemma_status': 'running',
                'ngram_status': 'running',
                'agenda_status': 'idle',
                'analyzer_status': 'idle',
                'sentiment_analyzer_status': 'idle',
                'use_ontology': int(use_ontology),
                'dilemma_error_message': '',
                'agenda_error_message': '',
                'ngram_error_message': '',
                'analyzer_error_message': '',
                'sentiment_analyzer_error_message': '',
                })
        
       
        self.send_mq_data(int(project_id), event='PROCESS_DILEMMA')
        return json.dumps({'status': True})
        
        return json.dumps({'status': True})
        
            
    def send_mq_data(self, project_id, event='PROCESS_DILEMMA'):
        try:
            # Fetch the project by ID
            project = request.env['cep.ag.project'].sudo().search([('id', '=', project_id)], limit=1)
            
            if not project:
                return False

            # Prepare project data
            project_data = {
                'id': project.id,
                'title': project.title,
                'description': project.description,
                'keywords': project.keywords,
                'owner_id': project.owner_id.id,
                'target_country': project.target_country,
                'start_year': project.start_year,
                'end_year': project.end_year,
                'status': project.status,
                'use_ontology': project.use_ontology,
            }

            # Fetch related agenda sources
            agenda_sources = request.env['cep.ag.agenda_source'].sudo().search([('project_id', '=', project.id)])

            # Prepare the agenda sources data
            agenda_data = []
            for agenda in agenda_sources:
                attachment_urls = []
                # base_url = "http://host.docker.internal:8069"
                base_url =  request.env['ir.config_parameter'].sudo().get_param('web.base.url')
                for attachment in agenda.attachment_ids:
                    attachment_urls.append(f"{base_url}/web/content/{attachment.id}?download=true")

                # Get predefined PDF data if this is a PDF source
            
                if agenda.type == 'pdf' and agenda.pre_defined_pdf_ids:
                    for attachment in agenda.predefined_attachment_ids:
                        attachment_urls.append(f"{base_url}/web/content/{attachment.id}?download=true")
                
                agenda_data.append({
                    'type': agenda.type,
                    'urls': json.loads(agenda.urls) if agenda.urls else [],
                    'attachments': attachment_urls,
                })

            # Publish data to RabbitMQ
            mq = request.env['rabbitmq.server'].sudo().search([], limit=1)
            if not mq:
                return {'error': 'RabbitMQ server configuration not found.'}

            mq.publish_message(body={ 'event': event, 'project': project_data, 'agenda_sources': agenda_data}, queue_name="AGENDA_SERVICE")

            return True

        except Exception as e:
            return False
        
    @http.route(['/cep_public/attachment/<int:attachment_id>'], type='http', auth='none' ,methods=['GET'], website=True)
    def get_public_attachment(self, attachment_id):
        
     
     
        attachment = request.env['ir.attachment'].sudo().browse(attachment_id)
        
        # Check if the attachment is related to cep.ag.agenda_source model
        if attachment.res_model != 'cep.ag.agenda_source':
            return request.not_found()
        
        # If the attachment exists and belongs to the correct model
        if attachment.exists():
            # Get the attachment content
            content = base64.b64decode(attachment.datas)
            
            # Return the file with proper headers
            return http.send_file(
                content,
                filename=attachment.name,
                mimetype=attachment.mimetype,
                as_attachment=True
            )
        
        return request.not_found()
