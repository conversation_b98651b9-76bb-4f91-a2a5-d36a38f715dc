from odoo import http
from odoo.http import request
import json
import base64
import logging
import os
_logger = logging.getLogger(__name__)
import requests


def isAuthenticate():
    return request.env.user.id != request.website.user_id


def isAgProject(owner_id):
    return owner_id.id == request.env.user.id


class AGController(http.Controller):

    _root_url = '/cep_agenda_generation'
    @http.route(f'{_root_url}', type='http', auth='user', methods=['GET'], website=True)
    def index(self):
        if not isAuthenticate():
            return request.redirect('/web/login')
        
    
        projects = request.env['cep.ag.project'].sudo().search(
            [('owner_id', '=', request.env.user.id)],
            order='create_date desc',  
            limit=3  
        )
        
        context = {'projects': projects}

        return request.render('cep_agenda_generation.ag_homepage', context)

    @http.route(f'{_root_url}/project/favorites', type='http', auth='user', methods=['GET'], website=True)
    def favorites(self):
        if not isAuthenticate():
            return request.redirect('/web/login')
        
    
        projects = request.env['cep.ag.project'].sudo().search(
            [('owner_id', '=', request.env.user.id), ('favorite', '=', True)],
            order='create_date desc',  
            limit=3  
        )
        
        context = {'projects': projects}

        return request.render('cep_agenda_generation.ag_favorite', context)




    @http.route(f'{_root_url}/create_project', type='http', auth='user', methods=['GET'], website=True)
    def show_create_project(self, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        return request.render('cep_agenda_generation.ag_create_project')



    @http.route([f'{_root_url}/create'], type='http', auth='user', website=True)
    def create(self, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')

        # Server-side validation
        title = kwargs.get('title', '').strip()
        description = kwargs.get('description', '').strip()
        project_category = kwargs.get('category', 'climate_assembly').strip()

        errors = {}
        if not title or len(title) > 100:
            errors['title'] = 'Title must be between 10 and 100 characters'
        if not description or len(description) > 500:
            errors['description'] = 'Description must be between 50 and 500 characters'
        if not project_category:
            errors['category'] = 'Project category is required'

        if errors:
            return request.render('cep_agenda_generation.ag_create_project', {
                'error': errors,
                'values': kwargs
            })

        kwargs['owner_id'] = request.env.user.id
        kwargs['category'] = project_category
        project = request.env['cep.ag.project'].sudo().create(kwargs)
        return request.redirect('/cep_agenda_generation/{}/view'.format(project.id))


    @http.route([f'{_root_url}/<string:project_id>/edit'], type='http', auth='user', website=True, methods=['POST'],csrf=False)
    def edit(self, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
            
        try:
                data = json.loads(request.httprequest.data)
        except Exception as e:
            return json.dumps({
                    'status': False,
                    'message': 'Invalid JSON payload'
                })
        try:
            
            project_id = kwargs['project_id']
            project = request.env['cep.ag.project'].sudo().search([('id', '=', int(project_id))], limit=1)
            
            # Check if project exists and user has access
            if not project.exists() or project.owner_id.id != request.env.user.id:
                return json.dumps({
                    'status': True,
                    'error': 'Project not found or access denied'
                })
            
            _logger.debug("dipro")
            _logger.debug(data.get('project-title'))
            update_values = {}
            if data.get('project-title'):
                update_values['title'] = data.get('project-title')
            if data.get('project-description'):
                update_values['description'] = data.get('project-description')
                
            if update_values:
                project.write(update_values)
                
            return json.dumps({
                'status': True,
                'message': 'Project updated successfully'
            })
            
        except Exception as e:
            
            return json.dumps({
                'status': True,
                'error': str(e)
            })

    @http.route(f'{_root_url}/<string:id>/view', type='http', auth='user', methods=['GET'], website=True)
    def view_project(self, id, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
            
        project = request.env['cep.ag.project'].sudo().search([('id', '=', int(id))], limit=1)
        if not project.exists():
            return request.redirect(f'{self._root_url}/create_project')
            
        agenda_sources = request.env['cep.ag.agenda_source'].sudo().search([('project_id', '=', int(id))])
        europe_group = request.env['res.country.group'].sudo().search([('name', '=', 'SEPA Countries')], limit=1)
        countries = europe_group.country_ids if europe_group else request.env['res.country'].sudo().search([])
        
        social_media = printed_media = pdf = request.env['cep.ag.agenda_source'].sudo()

        for source in agenda_sources:
            if source.type == 'social_media':
                social_media = source
            elif source.type == 'printed_media':
                printed_media = source
            elif source.type == 'pdf':
                pdf = source

        # Get predefined PDFs
        predefined_pdfs = request.env['cep.ag.pre_defined_pdf'].sudo().search([])
        
        context = {
            'project': project,
            'social_media': social_media,
            'printed_media': printed_media,
            'countries': countries,
            'pdf': pdf,
            'predefined_pdfs': predefined_pdfs,
        }
        

        return request.render('cep_agenda_generation.ag_add_keyword', context)

    @http.route(f'{_root_url}/list', type='http', auth='user', website=True, methods=['GET'])
    def list(self, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        projects = request.env['cep.ag.project'].sudo().search([('owner_id', '=', request.env.user.id)])
      
        context = {'projects': projects}
        return request.render('cep_agenda_generation.project_list', context )


   
    @http.route(f'{_root_url}/<string:id>/delete', type='http', auth='user', methods=['GET'], website=True)
    def delete_project(self, id,**post):
        if not isAuthenticate():
            return request.redirect('/web/login')
        
        
        project = request.env['cep.ag.project'].browse(int(id))
            
        
        if not isAgProject(project.owner_id):
            return request.redirect('/web/login')

        project.unlink()
        return request.redirect('/cep_agenda_generation/list')
           

    # Toggle Favorite API
    @http.route(f'{_root_url}/project/<string:id>/favorite', type='http', auth='user', methods=['GET'], website=True,)
    def toggle_favorite(self, id, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        try:
            project = request.env['cep.ag.project'].sudo().search([('id', '=', int(id))], limit=1)

            
            if not project.exists():
                return json.dumps({
                    'status': False,
                    'message': 'Project not found'
                })
                
            project.favorite = not project.favorite
            return json.dumps({
                'status': True,
            })
            
        except Exception as e:
            return json.dumps ({
                'status': False,
                'message': str(e)
            })
