# -*- coding: utf-8 -*-
from odoo import http
from odoo.http import request
import json
import pandas as pd
import base64
from io import BytesIO

class DilemmaController(http.Controller):
    @http.route('/cep_agenda_generation/dilemma_upload', type='http', auth='user', website=True)
    def dilemma_upload(self, **kw):
        return request.render('cep_agenda_generation.cep_ag_dilemma_upload', {})

    @http.route('/cep_agenda_generation/upload_dilemmas', type='http', auth='user', methods=['POST'], csrf=False)
    def upload_dilemmas(self, **post):
        try:
            file = request.httprequest.files.get('file')
            
            if not file:
                return json.dumps({'status': 'error', 'message': 'No file uploaded'})
            
            # Read file content
            file_data = file.read()
            
            # Convert to BytesIO object for pandas to read
            csv_file = BytesIO(file_data)
            
            # Read CSV file
            df = pd.read_csv(csv_file)
            
            # Prepare dilemma data for batch creation
            dilemma_model = request.env['cep.ag.dilemma'].sudo()
            dilemma_values = [{
                'dilemma_serial_id': str(row.get('#', '')),
                'title': str(row.get('Title', '')),
                'description': str(row.get('Description', '')),
                'questions': str(row.get('Questions_Array', '')),
                'environment': float(row.get('ENV', 0.0)),
                'eco': float(row.get('ECO', 0.0)),
                'soc': float(row.get('SOC', 0.0)),
                'tech': float(row.get('TECH', 0.0))
            } for _, row in df.iterrows()]
            
            # Batch create dilemmas
            dilemma_model.create(dilemma_values)
            
            return json.dumps({'status': 'success', 'message': f"{len(df)} dilemmas imported successfully"})
            
        except Exception as e:
            return json.dumps({'status': 'error', 'message': str(e)})