<a name="readme-top"></a>

<div align="center">
  <h3 align="center">C2IMPRESS - Platform</h3>

  <p align="center">
    TODO
  </p>
</div>

<!-- TABLE OF CONTENTS -->
<details open>
  <summary>Table of Contents</summary>
  <ol>
    <li>
      <a href="#about-the-project">About The Project</a>
      <ul>
        <li><a href="#built-with">Built With</a></li>
      </ul>
    </li>
    <li>
      <a href="#getting-started">Getting Started</a>
      <ul>
        <li><a href="#prerequisites">Prerequisites</a></li>
        <li><a href="#installation">Installation</a></li>
      </ul>
    </li>
  </ol>
</details>

<!-- ABOUT THE PROJECT -->

## About The Project

### Built With

- [Odoo](https://www.odoo.com)

## Getting Started

You can set up and run this project locally by following these simple steps.

### Prerequisites

This is a list of the things you need to use the API and how to install them.

1. Docker

   Follow the instructions provided [here](https://docs.docker.com/get-docker/)

2. Docker Compose

   Follow the instructions provided [here](https://docs.docker.com/compose/install/)

### Installation

1. Clone the repo
   ```sh
   git clone https://github.com/tvsltd/c2impress-odoo.git
   ```
2. Change the directory
   ```sh
   cd c2impress-odoo
   ```

2. Copy environment and configuration files
   ```sh
   cp .env.example .env
   cp config/odoo.conf.example config/odoo.conf
   cp secrets/pg_pass.example secrets/pg_pass
   ```

3. Run docker compose build
   ```sh
   docker-compose -p c2impress build
   ```

4. Run docker services
   ```sh
   docker-compose -p c2impress up
   ```
<p align="right">(<a href="#readme-top">back to top</a>)</p>