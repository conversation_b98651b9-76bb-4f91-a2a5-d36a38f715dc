version: '3.1'

services:
  platform:
    scale: 2
    env_file: .env
    restart: always
    build:
      context: .
      dockerfile: Dockerfile
    labels:
      - traefik.enable=true
      #----------------------------------------------- routers for: odoo --------------------------------------------------
      # http
      - traefik.http.routers.platform.rule=Host(`${PLATFORM_HOSTNAME}`)
      - traefik.http.routers.platform.entrypoints=web
      - traefik.http.middlewares.platform-redirect-to-https.redirectscheme.scheme=https
      - traefik.http.routers.platform.middlewares=platform-redirect-to-https@docker
      - traefik.http.routers.platform.service=platform
      # https
      - traefik.http.routers.platform-secure.rule=Host(`${PLATFORM_HOSTNAME}`)
      - traefik.http.routers.platform-secure.entrypoints=websecure
      - traefik.http.routers.platform-secure.service=platform
      - traefik.http.routers.platform-secure.tls=true
      - traefik.http.routers.platform-secure.tls.certresolver=le
      - traefik.http.routers.platform-secure.middlewares=gzip,sslheader,limit

      #---------------------------------------- routes for: odoo/websocket ------------------------------------------------
      # http 
      - traefik.http.routers.platform-im.rule=Host(`${PLATFORM_HOSTNAME}`) && (PathPrefix(`/websocket`))
      - traefik.http.routers.platform-im.entrypoints=web
      - traefik.http.routers.platform-im.service=platform-im
      # https 
      - traefik.http.routers.platform-im-secure.rule=Host(`${PLATFORM_HOSTNAME}`) && (PathPrefix(`/websocket`))
      - traefik.http.routers.platform-im-secure.entrypoints=websecure
      - traefik.http.routers.platform-im-secure.service=platform-im
      - traefik.http.routers.platform-im-secure.tls.certresolver=le
      - traefik.http.routers.platform-im-secure.middlewares=gzip,sslheader,limit,platform-cors

      #====================================================== services ===========================================================
      - traefik.http.services.platform.loadbalancer.server.port=8069
      - traefik.http.services.platform-im.loadbalancer.server.port=8072

      #===================================================== middlewares =========================================================
      - traefik.http.middlewares.gzip.compress=true
      - traefik.http.middlewares.sslheader.headers.customrequestheaders.X-Forwarded-Proto=https
      - traefik.http.middlewares.limit.buffering.memRequestBodyBytes=20971520
      - traefik.http.middlewares.limit.buffering.maxRequestBodyBytes=20971520
      # Enable CORS headers for all requests for design purpose. TODO remove when not needed
      - traefik.http.middlewares.platform-cors.headers.accesscontrolallowmethods=GET,OPTIONS,PUT
      - traefik.http.middlewares.platform-cors.headers.accesscontrolallowheaders=*
      - traefik.http.middlewares.platform-cors.headers.accesscontrolalloworiginlist=*
      - traefik.http.middlewares.platform-cors.headers.accesscontrolmaxage=100
      - traefik.http.middlewares.platform-cors.headers.addvaryheader=true
    depends_on:
      - db
    # expose:
    #   - 8069
    # ports:
    #   - "8069:8069"
    volumes:
      - platform-data:/var/lib/odoo
      - ./config:/etc/odoo
      - ./addons:/mnt/extra-addons
    secrets:
      - postgresql_password
  db:
    env_file: .env
    restart: always
    image: postgres:13
    volumes:
      - db-data:/var/lib/postgresql/data/pgdata
    secrets:
      - postgresql_password
volumes:
  platform-data:
  db-data:

secrets:
  postgresql_password:
    file: secrets/pg_pass
