[options]
addons_path = /mnt/extra-addons
data_dir = /var/lib/odoo
admin_passwd = 3rPJ7YXear
proxy_mode = True
test_enable = False
debug_mode = False
gevent_port = 8072
list_db = True
; longpolling_port = 8072
; csv_internal_sep = ,
; db_maxconn = 64
; db_name = False
; db_template = template1
; dbfilter = .*
; email_from = False
limit_memory_hard = 2684354560
limit_memory_soft = 2147483648
limit_request = 8192
limit_time_cpu = 60
limit_time_real = 120
limit_time_real_cron = -1

workers = 2
; log_db = False
; log_handler = [':INFO']
; log_level = info
; logfile = None
; longpolling_port = 8072
max_cron_threads = 2
; osv_memory_age_limit = 1.0
; osv_memory_count_limit = False
; smtp_password = False
; smtp_port = 25
; smtp_server = localhost
; smtp_ssl = False
; smtp_user = False
; workers = 0
xmlrpc = True
xmlrpc_interface = 
xmlrpc_port = 8069
xmlrpcs = True
xmlrpcs_interface = 
xmlrpcs_port = 8071

; db_host = 
; db_port = 5432
; db_user = 
; db_password = 
; db_name = 